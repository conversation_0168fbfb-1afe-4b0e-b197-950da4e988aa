package com.facishare.open.aliyun.market.enums;

import lombok.AllArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
public enum AliyunProductEditionEnum implements Serializable {
    enterprise_edition("旗舰增强版"),
    strengthen_edition("旗舰版"),
    standardpro_edition("专业版"),
    marketing_strategy_pro_app("营销通-专业版"),
    order_plus_app("订货通"),
    service_connect_pro_app("服务通-专业版"),
    prm_app("代理通"),
    interconnect_app_basic_app("互联应用"),
    stock_control_app("库存管理"),
    advanced_outwork_app("高级外勤"),
    aliyun_offline_service_without_license("阿里云上门实施服务包"),
    aliyun_online_service_without_license("阿里云远程基础实施服务包")
    ;
    private String versionName;
}
