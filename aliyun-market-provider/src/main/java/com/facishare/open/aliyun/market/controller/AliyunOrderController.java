package com.facishare.open.aliyun.market.controller;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.aliyun.market.arg.*;
import com.facishare.open.aliyun.market.config.ConfigCenter;
import com.facishare.open.aliyun.market.entity.EnterpriseBindEntity;
import com.facishare.open.aliyun.market.entity.OrderInfoEntity;
import com.facishare.open.aliyun.market.manager.EnterpriseBindManager;
import com.facishare.open.aliyun.market.manager.OrderInfoManager;
import com.facishare.open.aliyun.market.model.OrderModel;
import com.facishare.open.aliyun.market.service.OrderEventService;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.uc.api.event.EnterpriseRunStatusEvent;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/aliyun/market/order")
public class AliyunOrderController {
    //云市场分配的秘钥，在服务商入驻云市场成功后，在管理后台（msp.aliyun.com）中可以查询到
    private static final String SECRET_KEY = "15625256-e2eb-42b1-8b47-0da1b6ea33a4";
    @Autowired
    private OrderEventService orderEventService;
    @Autowired
    private EnterpriseEditionService enterpriseEditionService;
    @Autowired
    private OrderInfoManager orderInfoManager;
    @Autowired
    private EnterpriseBindManager enterpriseBindManager;


    /**
     * default action
     * @return
     */
    @RequestMapping(value="")
    @ResponseBody
    public String defaultAction() {
        return generateErrorResponse("wrong action");
    }

    /**
     * 创建实例
     * @return
     */
    @RequestMapping(value="", params="action=createInstance")
    @ResponseBody
    public String createInstance(HttpServletRequest request, CreateInstanceArg arg) {
        log.info("AliyunOrderController.createInstance,arg={}", arg);
        //1. 校验参数（必传参数，参数格式...）
        //..

        //2. 校验token
        if(!validateToken(request)) {
            return generateErrorResponse("token is invalid");
        }

        //3. 处理具体业务逻辑
        //	请不要阻塞此接口，若耗时较长，可使用队列做缓冲，设置instanceId=0，然后立即返回。
        //  若操作失败也请设置instanceId=0，云市场都会再次调用，直到获取到instanceId。
        //  ..
        //4. 返回结果
        JSONObject appInfo = new JSONObject();
        JSONObject result = new JSONObject();
        OrderModel orderModel = new OrderModel();
        BeanUtils.copyProperties(arg, orderModel);
        log.info("AliyunOrderController.createInstance,orderModel={}",orderModel);
        if(ObjectUtils.isEmpty(orderModel)) {
            result.put("instanceId", "0");
            return result.toJSONString();
        }

        //阿里云收不到正确的instanceId，会不断调用接口，我们下订单为异步操作，创建企业为异步执行，我们通过判断订单是否创建、企业绑定关系是否存在、企业的绑定状态是否正常去判断企业开通是否成功
        //查看有无订单信息
        OrderInfoEntity orderInfoEntity = orderInfoManager.getEntity(orderModel.getOrderId());
        log.info("AliyunOrderController.createInstance,orderInfoEntity={}", orderInfoEntity);
        if(ObjectUtils.isNotEmpty(orderInfoEntity)) {
            //查询企业ea
            List<EnterpriseBindEntity> enterpriseBindList = enterpriseBindManager.getEnterpriseBindList(ChannelEnum.aliyun, orderInfoEntity.getPaidCorpId());
            log.info("AliyunOrderController.createInstance,enterpriseBindList={}", enterpriseBindList);
            if(CollectionUtils.isNotEmpty(enterpriseBindList) && enterpriseBindList.get(0).getBindStatus() == BindStatusEnum.create) {
                GetEnterpriseDataArg enterprise = new GetEnterpriseDataArg();
                enterprise.setEnterpriseAccount(enterpriseBindList.get(0).getFsEa());
                GetEnterpriseDataResult getEnterpriseDataResult = enterpriseEditionService.getEnterpriseData(enterprise);
                log.info("AliyunOrderController.createInstance,getEnterpriseDataResult={}", getEnterpriseDataResult);
                if(ObjectUtils.isNotEmpty(getEnterpriseDataResult)
                        && ObjectUtils.isNotEmpty(getEnterpriseDataResult.getEnterpriseData())
                        && getEnterpriseDataResult.getEnterpriseData().getRunStatus() == EnterpriseRunStatusEvent.RUN_STATUS_NORMAL) {
                    //企业已开通，改为正常状态
                    enterpriseBindManager.updateBindStatus(enterpriseBindList.get(0).getFsEa(), BindStatusEnum.normal);
                    //返回开通成功的通知
                    appInfo.put("authUrl", ConfigCenter.CRM_DOMAIN + "/aliyun/market/login");
                    result.put("instanceId", orderModel.getOrderBizId());
                    result.put("appInfo", appInfo.toJSONString());
                    log.info("AliyunOrderController.createInstance,result={}", result);
                    return result.toJSONString();
                }
            }
            if(CollectionUtils.isNotEmpty(enterpriseBindList) && orderModel.getProductCode().equals("cmfw00059470")) {
                result.put("instanceId", orderModel.getOrderBizId());
                log.info("AliyunOrderController.createInstance,result2={}", result);
                return result.toJSONString();

            }
        } else {
            //异步执行下订单操作
            Thread thread = new Thread(() -> orderEventService.upgradeInstance(orderModel));
            thread.start();
        }

        result.put("instanceId", "0");
        log.info("AliyunOrderController.createInstance,result1={}", result);
        return result.toJSONString();
    }

    /**
     * 续费实例
     * @return
     */
    @RequestMapping(value="", params="action=renewInstance")
    @ResponseBody
    public String renewInstance(HttpServletRequest request, RenewInstanceArg arg) {
        log.info("AliyunOrderController.renewInstance,arg={}",arg);
        //1. 校验参数（必传参数，参数格式...）
        //..

        //2. 校验token
        if(!validateToken(request)) {
            return generateErrorResponse("token is invalid");
        }

        //3. 处理具体业务逻辑
        //...

        //4. 返回结果
        OrderModel orderModel = new OrderModel();
        BeanUtils.copyProperties(arg, orderModel);
        log.info("AliyunOrderController.renewInstance,orderModel={}",orderModel);
        if(ObjectUtils.isEmpty(orderModel)) {
            return generateErrorResponse("orderModel is null.");
        }

        //异步执行下订单操作
        Thread thread = new Thread(() -> orderEventService.upgradeInstance(orderModel));
        thread.start();

        return generateCorrectResponse();
    }

    /**
     * 商品升级
     * @return
     */
    @RequestMapping(value="", params="action=upgradeInstance")
    @ResponseBody
    public String upgradeInstance(HttpServletRequest request, UpgradeInstanceArg arg) {
        log.info("AliyunOrderController.upgradeInstance,arg={}",arg);
        //1. 校验参数（必传参数，参数格式...）
        //..

        //2. 校验token
        if(!validateToken(request)) {
            return generateErrorResponse("token is invalid");
        }

        //3. 处理具体业务逻辑
        //...

        //4. 返回结果
        OrderModel orderModel = new OrderModel();
        BeanUtils.copyProperties(arg, orderModel);
        log.info("AliyunOrderController.upgradeInstance,orderModel={}",orderModel);
        if(ObjectUtils.isEmpty(orderModel)) {
            return generateErrorResponse("orderModel is null.");
        }

        //异步执行下订单操作
        Thread thread = new Thread(() -> orderEventService.upgradeInstance(orderModel));
        thread.start();

        return generateCorrectResponse();
    }

    /**
     * 过期实例
     * @return
     */
    @RequestMapping(value="", params="action=expiredInstance")
    @ResponseBody
    public String expiredInstance(HttpServletRequest request, ExpiredInstanceArg arg) {
        log.info("AliyunOrderController.expiredInstance,arg={}",arg);
        //1. 校验参数（必传参数，参数格式...）
        //..

        //2. 校验token
        if(!validateToken(request)) {
            return generateErrorResponse("token is invalid");
        }

        //3. 处理具体业务逻辑
        //...

        //4. 返回结果
        return generateCorrectResponse();
    }

    /**
     * 释放实例
     * @return
     */
    @RequestMapping(value="", params="action=releaseInstance")
    @ResponseBody
    public String releaseInstance(HttpServletRequest request, ReleaseInstanceArg arg) {
        log.info("AliyunOrderController.releaseInstance,arg={}",arg);
        //1. 校验参数（必传参数，参数格式...）
        //..

        //2. 校验token
        if(!validateToken(request)) {
            return generateErrorResponse("token is invalid");
        }

        //3. 处理具体业务逻辑
        //...

        //4. 返回结果
        return generateCorrectResponse();
    }

    /**
     * 校验token
     * @return
     */
    private boolean validateToken(HttpServletRequest request) {
        String genToken = generateToken(request);
        return genToken == null ? false : genToken.equals(request.getParameter("token"));
    }

    /**
     * 根据请求参数生成token
     * @return
     */
    private String generateToken(HttpServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        String[] sortedKeys = (String[])parameterMap.keySet().toArray(new String[0]);
        Arrays.sort(sortedKeys);
        StringBuilder baseStringBuilder = new StringBuilder();
        for(String key : sortedKeys) {
            if(!"token".equals(key)) {
                baseStringBuilder.append(key).append("=").append(parameterMap.get(key)[0]).append("&");
            }
        }
        baseStringBuilder.append("key").append("=").append(SECRET_KEY);
        return md5(baseStringBuilder.toString());
    }


    /**
     * md5工具方法
     * @param s
     * @return
     */
    private static String md5(String s) {
        try {
            MessageDigest DIGESTER = MessageDigest.getInstance("MD5");
            byte[] digest = DIGESTER.digest(s.getBytes());
            return bytesToString(digest);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static String bytesToString(byte[] data) {
        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
                'e', 'f'};
        char[] temp = new char[data.length * 2];
        for (int i = 0; i < data.length; i++) {
            byte b = data[i];
            temp[i * 2] = hexDigits[b >>> 4 & 0x0f];
            temp[i * 2 + 1] = hexDigits[b & 0x0f];
        }
        return new String(temp);
    }

    /**
     * 返回错误结果
     * @param errorMessage
     * @return
     */
    private String generateErrorResponse(String errorMessage) {
        JSONObject result = new JSONObject();
        result.put("success", false);
        result.put("message", errorMessage);
        return result.toJSONString();
    }

    /**
     * 返回正确结果
     * @return
     */
    private String generateCorrectResponse() {
        JSONObject result = new JSONObject();
        result.put("success", true);
        return result.toJSONString();
    }
}
