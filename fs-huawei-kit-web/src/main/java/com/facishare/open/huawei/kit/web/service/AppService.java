package com.facishare.open.huawei.kit.web.service;

import com.facishare.open.huawei.kit.web.entity.AppInfoEntity;
import com.facishare.open.huawei.kit.web.result.result.Result;

/**
 * 应用服务
 * <AUTHOR>
 * @date 20220717
 */
public interface AppService {
//    Result<Integer> updateAppInfo(AppInfoEntity entity);
//
//    Result<AppInfoEntity> getAppInfo(String outEa);
//
//    Result<AppInfoEntity> getAppInfo(String outEa, String appId);


}