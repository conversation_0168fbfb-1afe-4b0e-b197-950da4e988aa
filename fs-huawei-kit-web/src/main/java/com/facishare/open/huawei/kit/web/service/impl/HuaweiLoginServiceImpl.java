package com.facishare.open.huawei.kit.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.huawei.kit.web.config.ConfigCenter;
import com.facishare.open.huawei.kit.web.info.HuaweiUserInfoVo;
import com.facishare.open.huawei.kit.web.result.result.Result;
import com.facishare.open.huawei.kit.web.result.result.ResultCodeEnum;
import com.facishare.open.huawei.kit.web.service.AppService;
import com.facishare.open.huawei.kit.web.service.CorpService;
import com.facishare.open.huawei.kit.web.service.HuaweiLoginService;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.params.HuaweiAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.admin.HuaweiEnterpriseConnectVo;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Service("huaweiLoginService")
public class HuaweiLoginServiceImpl implements HuaweiLoginService {
    @Resource
    private AppService appService;
    @Autowired
    private ProxyHttpClient proxyHttpClient;
    @Autowired
    private CorpService corpService;
    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Override
    public Result<HuaweiUserInfoVo> getUserByCode(String code, String outTenantId) {
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.huawei, outTenantId, ConfigCenter.HUAWEI_APP_ID);
        if(ObjectUtils.isEmpty(enterpriseBindEntities)) {
            return Result.newError(ResultCodeEnum.FS_EA_NOT_BIND);
        }
        OuterOaEnterpriseBindEntity enterpriseBindEntity = enterpriseBindEntities.get(0);
        HuaweiEnterpriseConnectVo enterpriseConnectParams = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), HuaweiEnterpriseConnectVo.class);


        Result<String> result = getAccessToken(code, outTenantId, enterpriseConnectParams.getOutDomain());
        String getUserInfoUrl=String.format(ConfigCenter.GET_AUTH_USER_INFO,enterpriseConnectParams.getOutDomain());

        Map<String, String> hearMap = Maps.newHashMap();
        hearMap.put("Authorization","Bearer " + result.getData());
        hearMap.put("Accept","application/json");
//        hearMap.put("Content-Type","application/x-www-form-urlencoded");
//        Map<String, Object> paramsMap = Maps.newHashMap();

        String json = proxyHttpClient.getUrl(getUserInfoUrl, hearMap);

        HuaweiUserInfoVo huaweiUserInfoVo = JSONObject.parseObject(json, new TypeReference<HuaweiUserInfoVo>() {
        });

        LogUtils.info("HuaweiLoginServiceImpl.getUserByCode huaweiUserInfoVo:{}", huaweiUserInfoVo);

        return Result.newSuccess(huaweiUserInfoVo);
    }

    public Result<String> getAccessToken(String code, String outTenantId, String domainName) {
        //查询appInfo
        OuterOaAppInfoEntity oaAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.huawei, outTenantId, ConfigCenter.HUAWEI_APP_ID);
        if(ObjectUtils.isEmpty(oaAppInfoEntity)) {
            return Result.newError(ResultCodeEnum.PARAM_ILLEGAL);
        }
        HuaweiAppInfoParams huaweiAppInfoParams = JSON.parseObject(oaAppInfoEntity.getAppInfo(), HuaweiAppInfoParams.class);

        String clientSecret = huaweiAppInfoParams.getClientSecret();
        String clientId = huaweiAppInfoParams.getClientId();
        String domainUrl = String.format(ConfigCenter.GET_TOKEN_URL, domainName, code);
        domainUrl = domainUrl + "&client_id=" + clientId + "&client_secret=" + clientSecret;
        Map<String, String> hearMap = Maps.newHashMap();

        hearMap.put("Content-Type","application/x-www-form-urlencoded");
        Map<String, Object> paramsMap = Maps.newHashMap();

        Map<String, String> result = proxyHttpClient.postUrl(domainUrl, paramsMap, hearMap, new TypeReference<Map<String, String>>() {
        });

//        HttpResponseMessage httpResponseMessage = proxyHttpClient.postUrl(domainUrl, paramsMap, hearMap);
//        Map<String, String> tokenMap = JSONObject.parseObject(httpResponseMessage.getContent(), new TypeReference<Map<String, String>>() {
//        });
        LogUtils.info("get access token content:{}", result);
        String accessToken = result.get("access_token");
        //code每次进来都会改变，暂时先不缓存refreshToken
        String refreshToken = result.get("refresh_token");
        return Result.newSuccess(accessToken);
    }
}
