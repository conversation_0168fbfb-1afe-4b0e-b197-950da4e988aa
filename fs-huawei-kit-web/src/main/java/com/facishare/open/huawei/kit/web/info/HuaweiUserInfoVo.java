package com.facishare.open.huawei.kit.web.info;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2021/10/19 15:22
 * @Version 1.0
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HuaweiUserInfoVo implements Serializable {
    private String id;//新增时候使用
    private String name;//新增时候使用
    private String userName;//更新时候使用
    private String email;
    private String mobile;
    private String error;
    private String error_description;
    private String role;
    private String organizationName;
    private String userId;
    private String organizationCode;
    private String projectName;//外部用户ID
    private String tenant;
}
