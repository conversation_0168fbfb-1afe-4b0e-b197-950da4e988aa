package com.facishare.open.qywx.web.job;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class AutoInterfaceLicenseHandlerTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private AutoInterfaceLicenseHandler autoInterfaceLicenseHandler;

    @Test
    public void test() throws Exception {
        autoInterfaceLicenseHandler.execute(null);
    }
}
