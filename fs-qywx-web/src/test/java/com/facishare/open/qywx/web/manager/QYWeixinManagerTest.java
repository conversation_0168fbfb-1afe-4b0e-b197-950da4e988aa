package com.facishare.open.qywx.web.manager;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.qywx.accountbind.model.QyweixinAccountEmployeeMapping;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountinner.model.QyweixinExternalContactRsp;
import com.facishare.open.qywx.accountinner.result.Result;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinCorpBindBo;
import com.facishare.open.qywx.accountsync.model.qyweixin.bo.QyweixinExternalContactBo;
import com.facishare.open.qywx.accountsync.utils.xml.SuiteAuthXml;
import com.facishare.open.qywx.web.arg.BatchGetEmpBindArg;
import com.facishare.open.qywx.web.db.dao.QyweixinCorpBindDao;
import com.facishare.open.qywx.web.db.dao.QyweixinExternalContactDao;
import com.facishare.open.qywx.web.model.TagDetailModel;
import com.facishare.open.qywx.web.model.qyweixin.*;
import com.facishare.open.qywx.web.result.accountsync.BatchEmpBindResult;
import com.facishare.open.qywx.web.result.accountsync.UploadResult;
import com.facishare.open.qywx.web.utils.HttpHelper;
import com.fxiaoke.common.Pair;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.rmi.RemoteException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class QYWeixinManagerTest extends AbstractJUnit4SpringContextTests {
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @ReloadableProperty("crmAppId")
    private String crmAppId;
    @ReloadableProperty("repAppId")
    private String repAppId;


    @Test
    public void setCallback() throws NoSuchAlgorithmException {
        Result<String> result = qyWeixinManager.setCallback("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7");
        System.out.println(result);
    }
}
