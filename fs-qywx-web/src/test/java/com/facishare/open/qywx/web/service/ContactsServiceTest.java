package com.facishare.open.qywx.web.service;

import com.alibaba.fastjson.JSON;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class ContactsServiceTest extends AbstractJUnit4SpringContextTests {
    @Resource
    private ContactsService contactsService;
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;

    @Test
    public void test() throws Exception {
        Result<List<ObjectData>> listResult = fsContactsServiceProxy.batchGetAllFsDep(89780, "999999");
        log.info("listResult={}", JSON.toJSONString(listResult));
    }

    @Test
    public void test2() throws Exception {
        contactsService.syncOuterContacts("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7", "change_auth", "<xml><SuiteId><![CDATA[dkdf3684b6720635f7]]></SuiteId><InfoType><![CDATA[change_auth]]></InfoType><TimeStamp>1746530545</TimeStamp><AuthCorpId><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></AuthCorpId></xml>");
    }

    @Test
    public void test3() throws Exception {
        contactsService.syncOuterContacts("wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA", "dkdf3684b6720635f7", "change_contact", "<xml><ToUserName><![CDATA[wpwx1mDAAAACm9gof1ABfLCuMlTM9kaA]]></ToUserName><FromUserName><![CDATA[sys]]></FromUserName><CreateTime>1746532694</CreateTime><MsgType><![CDATA[event]]></MsgType><Event><![CDATA[change_contact]]></Event><ChangeType><![CDATA[update_user]]></ChangeType><UserID><![CDATA[wowx1mDAAArLI4aPJXTqJx_UJCABHILQ]]></UserID><Alias><![CDATA[center11]]></Alias></xml>");
    }
}
