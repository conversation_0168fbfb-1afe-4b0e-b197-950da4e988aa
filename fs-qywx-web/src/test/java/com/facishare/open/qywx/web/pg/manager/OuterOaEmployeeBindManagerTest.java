package com.facishare.open.qywx.web.pg.manager;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.AbstractJUnit4SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = "classpath:spring-test/spring-common-test.xml")
public class OuterOaEmployeeBindManagerTest extends AbstractJUnit4SpringContextTests {

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Test
    public void insert() {
        // 创建 OuterOaEmployeeBindEntity 对象
        OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
        bindEntity.setChannel(ChannelEnum.qywx); // 渠道
        bindEntity.setDcId("67dc043db043084d300f7c21"); // 数据中心id
        bindEntity.setFsEa("xkhdx003"); // 纷享企业 ea
        bindEntity.setOutEa("wpwx1mDAAAaaLlKWQ8uGm23tj_KM8gIA"); // 外部企业 ea
        bindEntity.setAppId("dkdf3684b6720635f7"); // 外部应用 appId
        bindEntity.setFsEmpId("1000"); // 纷享员工 id
        bindEntity.setOutEmpId("wowx1mDAAAswtw60xRomOCQexeLXdU3w"); // 外部员工 id
        bindEntity.setBindStatus(BindStatusEnum.normal); // 绑定状态
        bindEntity.setCreateTime(System.currentTimeMillis()); // 创建时间
        bindEntity.setUpdateTime(System.currentTimeMillis()); // 修改时间

        // 插入数据
        Integer result = outerOaEmployeeBindManager.insert(bindEntity);
        System.out.println(result);
    }
}
