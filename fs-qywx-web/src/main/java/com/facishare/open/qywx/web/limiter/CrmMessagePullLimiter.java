package com.facishare.open.qywx.web.limiter;

import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.web.manager.SfaApiManager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fxiaoke.otherrestapi.function.arg.FunctionServiceExecuteArg;
import com.fxiaoke.otherrestapi.function.data.FunctionServiceParameterData;
import com.fxiaoke.otherrestapi.function.data.HeaderObj;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.rmi.RemoteException;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class CrmMessagePullLimiter {
    @Resource
    private SfaApiManager sfaApiManager;

    public Boolean messageIsPull(Integer ei, String apiName, Object object) {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> sourceMsg = objectMapper.convertValue(object, new TypeReference<Map<String, Object>>() {});

        HeaderObj headerObj = new HeaderObj(ei,-10000);
        FunctionServiceExecuteArg executeArgarg = new FunctionServiceExecuteArg();
        executeArgarg.setApiName(apiName);
        executeArgarg.setBindingObjectAPIName("NONE");
        executeArgarg.setNameSpace("controller");

        List<FunctionServiceParameterData> parameters = new LinkedList<>();
        FunctionServiceParameterData<Map> data = new FunctionServiceParameterData();
        data.setName("sourceMsg");
        data.setType("Map");
        data.setValue(sourceMsg);
        parameters.add(data);
        executeArgarg.setParameters(parameters);
        Result<Object> objectResult = null;
        try {
            objectResult = sfaApiManager.executeCustomFunction(headerObj, executeArgarg);
        } catch (RemoteException e) {
            e.printStackTrace();
        }
        if(objectResult == null || !objectResult.isSuccess() || ObjectUtils.isEmpty(objectResult.getData())) {
            return Boolean.FALSE;
        }
        //判断是否可以发送
        Map<String, Object> objectMap = new Gson().fromJson(objectResult.getData().toString(), new TypeToken<Map<String, Object>>() {
        });
        boolean isContinue = Boolean.parseBoolean(objectMap.get("continue").toString());
        log.info("QYWeixinMessageSendServiceImpl.sendQyWeixinMsg,isContinue={}", isContinue);
        return isContinue;
    }
}
