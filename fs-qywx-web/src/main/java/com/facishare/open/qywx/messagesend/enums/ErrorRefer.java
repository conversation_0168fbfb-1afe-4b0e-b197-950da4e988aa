package com.facishare.open.qywx.messagesend.enums;

/**
 * Created by fengyh on 2018/7/23.
 */
public enum ErrorRefer {
    DATABASE_RETURN_NULL(203, "数据库返回为空"),

    NULL_POINTER_EXCEPTION(202, "空指针异常"),

    BIND_ERROR(0, "绑定数量为0"),

    QUERRY_EMPTY(0, "查询数据为空"),


    FIELD(900, "Service返回失败"),
    NO_BIND_EMPLOYEE(901, "没有找到和CRM人员绑定的企业微信人员"),
    FUNCTION_ERROR(910, "自定义函数调用失败"),
    MSG_RECEIVER_CANNOT_BE_EMPTY(911, "消息接收人不能为空");

    private int code;               //返回码
    private String message;        //返回描述

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    ErrorRefer(int code, String message){
        this.code = code;
        this.message = message;
    }
}
