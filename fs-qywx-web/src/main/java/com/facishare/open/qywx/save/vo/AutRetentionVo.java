package com.facishare.open.qywx.save.vo;

import com.facishare.open.qywx.save.result.FileMessageResult;
import com.facishare.open.qywx.save.result.Result;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class AutRetentionVo implements Serializable {

    private int ei;  // 83384

    private String ea;  // 83384

    private String roomFsId; //1000  群主的纷享账号

    private String roomName; //群名称

    private String fsId;  //1000

    private String fsUserName;  //chenzongxin

    private String userName;  // U-FSQYWX-chenzon

    private String contactId; //

    private String contactName; // 糕云

    private Result<List<FileMessageResult>> pagerResult;

    private Map<String, Object> relatedObject;

    private Map<String, Object> attempt;





}
