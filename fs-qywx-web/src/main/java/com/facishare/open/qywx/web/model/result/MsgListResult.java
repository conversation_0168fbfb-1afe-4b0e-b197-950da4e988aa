package com.facishare.open.qywx.web.model.result;

import com.facishare.open.qywx.save.result.QywxResult;
import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * 消息列表返回结果实体
 * 对应飞书/企业微信消息拉取接口返回结构
 */
@Data
public class MsgListResult extends QywxResult {
    /**
     * 是否还有更多数据，1表示有更多
     */
    private Integer has_more;
    /**
     * 下次分页游标
     */
    private String next_cursor;
    /**
     * 消息列表
     */
    private List<MsgListItem> msg_list;

    @Data
    public static class MsgListItem implements Serializable {
        /**
         * 消息ID
         */
        private String msgid;
        /**
         * 消息详情
         */
        private MsgDetail msg_detail;
        /**
         * 命中规则列表
         */
        private List<HitRule> hit_rule_list;
    }

    @Data
    public static class MsgDetail implements Serializable {
        /**
         * 发送者信息
         */
        private Sender sender;
        /**
         * 接收者列表
         */
        private List<Receiver> receiver_list;
        /**
         * 群聊ID
         */
        private String chatid;
        /**
         * 发送时间
         */
        private Long send_time;
        /**
         * 消息类型
         */
        private Integer msgtype;
        /**
         * 服务端加密信息
         */
        private ServiceEncryptInfo service_encrypt_info;
    }

    @Data
    public static class Sender implements Serializable {
        /**
         * 发送者类型
         */
        private Integer type;
        /**
         * 发送者ID
         */
        private String id;
    }

    @Data
    public static class Receiver implements Serializable {
        /**
         * 接收者类型
         */
        private Integer type;
        /**
         * 接收者ID
         */
        private String id;
    }

    @Data
    public static class ServiceEncryptInfo implements Serializable {
        /**
         * 加密密钥
         */
        private String encrypted_secret_key;
        /**
         * 公钥版本
         */
        private Integer public_key_ver;
    }

    @Data
    public static class HitRule implements Serializable {
        /**
         * 规则ID
         */
        private String rule_id;
        /**
         * 是否命中关键词
         */
        private Boolean has_hit_keyword;
        /**
         * 语义命中列表
         */
        private List<Integer> semantics_list;
    }
} 