package com.facishare.open.qywx.web.model.qyweixin;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @create 2024/10/25 19:11
 * 企业微信许可接口详情
 * @desc
 */
@Data
public class QywxPermissionOrderDetail extends QywxBaseResult implements Serializable {

    private OrderInfo order;

    @Data
    public class OrderInfo implements Serializable {
        private String order_id;
        private Integer order_type;//1：购买账号，2：续期账号，5：应用版本付费迁移订单，6：历史合同迁移订单
        private Integer order_status;//订单状态，0：待支付，1：已支付，2：已取消（未支付，订单已关闭）3：未支付，订单已过期，
        // 4：申请退款中，5：退款成功，6：退款被拒绝，7：订单已失效（将企业从服务商测试企业列表中移除时会将对应测试企业的所有测试订单置为已失效）
        private String corpid;
        private Long price;
        private OrderInfoAccount account_count;
        private OrderInfoDuration account_duration;
        private Long  create_time;
        private Long pay_time;

    }

    @Data
    public  static class OrderInfoAccount implements Serializable {
        //基础账号个数
        private Integer base_count;
        //互通账号个数
        private Integer external_contact_count;

    }

    @Data
    public static class OrderInfoDuration implements Serializable {
        //购买的月数
        private Integer months;
        //购买的天数
        private Integer days;
        //下单续期账号中指定新过期时间时返回
        private Long new_expire_time;
    }
    //计算激活
    public Long calculateActiveExpireTime(){

        if(this.getOrder().getAccount_duration().getDays()!=null){
            if(this.getOrder().getAccount_duration().getDays()<=365){
                return System.currentTimeMillis() + 365 * 24 * 60 * 60 * 1000L;
            }
            //防止整数溢出
            if(this.getOrder().getAccount_duration().getDays()>=365){
                return System.currentTimeMillis() + this.getOrder().getAccount_duration().getDays() * 24 * 60 * 60 * 1000L;
            }
        }
        if(this.getOrder().getAccount_duration().getMonths()!=null){
            if(this.getOrder().getAccount_duration().getMonths()<=12){
                return System.currentTimeMillis() + 12 * 31 * 24 * 60 * 60 * 1000L;
            }else {
                return System.currentTimeMillis() + this.getOrder().getAccount_duration().getMonths() *31* 24 * 60 * 60 * 1000L;
            }
        }
        if(this.getOrder().getAccount_duration().getNew_expire_time()!=null){
            return this.getOrder().getAccount_duration().getNew_expire_time();
        }
        return null;
    }
    //计算购买时长
    public Integer calculateDuration(){

        if(this.getOrder().getAccount_duration().getDays()!=null){
            return this.getOrder().getAccount_duration().getDays();
        }
        if(this.getOrder().getAccount_duration().getMonths()!=null){
            return this.getOrder().getAccount_duration().getMonths()*31;
        }
        //暂不计算指定过期时间
        return null;
    }
}
