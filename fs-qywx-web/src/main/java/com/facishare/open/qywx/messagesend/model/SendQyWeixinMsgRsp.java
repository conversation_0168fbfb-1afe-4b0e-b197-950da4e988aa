package com.facishare.open.qywx.messagesend.model;

import lombok.Data;

import java.io.Serializable;

/**
 * Created by fengyh on 2018/7/23.
 */
@Data
public class SendQyWeixinMsgRsp  implements Serializable {

    private static final long serialVersionUID = 4871724409360957829L;
    private String errcode;

    private String errmsg;

    //发送失败的用户列表
    private String invaliduser;  //不区分大小写，返回的列表都统一转为小写    userid1|userid2
    //消息id
    private String msgid;

    public boolean isSuccess() {
        if ("0".equals(errcode)) {
            return true;
        }
        return false;
    }
}
