package com.facishare.open.qywx.web.controller.outer;

import com.alibaba.dubbo.common.URL;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.oa.base.dbproxy.ch.manager.OAConnectorOpenDataModel;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaAppInfoParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.oa.base.dbproxy.utils.SecurityUtil;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.params.QyweixinAppInfoParams;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.qywx.web.manager.OANewBaseManager;
import com.facishare.open.qywx.web.manager.QYWeixinManager;
import com.facishare.open.qywx.web.template.model.GenFsTicketModel;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.arg.SendTextNoticeArg;
import com.facishare.open.qywx.accountinner.model.QywxCorpAuthorizeInfo;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.NotificationService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService;
import com.facishare.open.qywx.accountsync.core.enums.QYWXDataTypeEnum;
import com.facishare.open.qywx.accountsync.model.EnterpriseWechatUserModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.*;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.web.enums.UserContextSingleton;
import com.facishare.open.qywx.web.manager.APLManager;
import com.facishare.open.qywx.web.model.qyweixin.QyweixinExchangeId;
import com.facishare.open.qywx.web.template.inner.login.QyweixinLoginTemplate;
import com.facishare.open.qywx.web.utils.MD5Helper;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18NStringManager;
import com.facishare.open.outer.oa.connector.i18n.qywx.I18nUtils;
import com.facishare.open.qywx.web.utils.url.UrlUtils;
import com.facishare.organization.api.model.employee.EmployeeDto;
import com.facishare.organization.api.model.type.EmployeeEntityStatus;
import com.facishare.uc.api.model.enterprise.arg.GetEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.common.Pair;
import com.fxiaoke.common.StopWatch;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Base64Utils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by liuwei on 2018/06/15
 */
@RestController
@Slf4j
@RequestMapping("/qyweixin/")
// IgnoreI18nFile
public class ControllerQYWeixin {

    @Autowired
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private ContactBindInnerService contactBindInnerService;
    @Resource
    private QyweixinGatewayInnerService qyweixinGatewayService;

    @Autowired
    private QyweixinAccountBindService qyweixinAccountBindService;

    @Autowired
    private QyweixinAccountSyncService qyweixinAccountSyncService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private NotificationService notificationService;
    @Resource(name = "qywxI18NStringManager")
    private I18NStringManager i18NStringManager;
    @Autowired
    private APLManager aplManager;

    @Resource
    private QyweixinLoginTemplate qyweixinLoginTemplate;

    //北研提供的登录模板  www.xxx.yyy
    @ReloadableProperty("loginDomain")
    private String loginDomain;

    @ReloadableProperty("providerCorpId")
    private String corpId;

    //纷享的域名前缀 open.xxx.yyy
    @ReloadableProperty("domainPrefix")
    private String domainPrefix;
    //紫光云的域名前缀
    @ReloadableProperty(("UcdDomainPrefix"))
    private String UcdDomainPrefix;
    @ReloadableProperty("ucdCorpIds")
    private String ucdCorpIds;

    @ReloadableProperty("contactAppId")
    private String contactAppId;

    @ReloadableProperty("profile")
    private String profile;

    @ReloadableProperty("appLoginAuthScope")
    private String appLoginAuthScope;

    /**
     * 代开发应用app主页URL配置
     */
    @ReloadableProperty("repAppMainPage")
    private String repAppMainPage;

    //注册定制化模板ID, 企业微信上叫 "推广包ID". 不会变，测试环境不能测，直接写死
    private static final String regTemplateId="tpl141a559bd493acc0";

    private static final String STATE = "neeJw0w3Yw32Qhi6";

    private static final String VER = "V1_";
    @ReloadableProperty("qywx_ip_url")
    private String qywxIpUrl;

    @Resource
    private QYWeixinManager qyweixinManager;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Resource
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Resource
    private OANewBaseManager oANewBaseManager;

    /**
     * 获取添加企业微信应用的链接，点击链接就会跳转到授权安装应用和设置可见范围的页面。
     * @param appID : 企业微信平台分配给应用的ID
     * @return：　URL链接
     */
    @RequestMapping(value = "/doGetAppInstallAuthURL", method = RequestMethod.GET)
    @ResponseBody
    public String doGetAppInstallAuthURL(@RequestParam String appID) throws IOException {
        String pre_auth_code = qyweixinGatewayService.getPreAuthCode(appID);
        String redirect_uri = URL.encode(domainPrefix + "/qyweixin/callback/installAuth");
        String url ="https://open.work.weixin.qq.com/3rdapp/install?suite_id="+appID+"&pre_auth_code="+ pre_auth_code+"&redirect_uri="+redirect_uri+"&state="+appID;
        log.info("trace getNewAuthURL, get url:{} ", url);
        return url;
    }

    /**
     * 官网的添加应用后面的地址,
     * http://open.xxx.com/qyweixin/doAppInstallAuth?appID=wxyyy
     *
     * 管理员可以扫码授权安装
     * @param appID : 企业微信平台分配给应用的ID
     * @param fromEa 来源纷享EA，主要用于专属云，比如紫光云等
     * @param fromOrigin 来源域名，比如https://www.fxiaoke.com
     * @return：无
     */
    @RequestMapping(value = "/doAppInstallAuth", method = RequestMethod.GET)
    public void doAppInstallAuth(@RequestParam String appID,
                                 @RequestParam(required = false) String fromEa,
                                 @RequestParam(required = false) String fromOrigin,
                                 HttpServletResponse response) throws IOException {
        log.info("ControllerQYWeixin.doAppInstallAuth,appID={},fromEa={},fromOrigin={}",appID,fromEa,fromOrigin);
        String pre_auth_code = qyweixinGatewayService.getPreAuthCode(appID);
        if(StringUtils.isNotEmpty(fromOrigin)) {
            fromOrigin = URL.decode(fromOrigin);
        } else {
            fromOrigin="";
        }
        String redirect_uri = URL.encode(domainPrefix + "/qyweixin/callback/installAuth?from_ea="+fromEa+"&from_origin="+fromOrigin);
        String url ="https://open.work.weixin.qq.com/3rdapp/install?suite_id="+appID+"&pre_auth_code="+ pre_auth_code+"&redirect_uri="+redirect_uri+"&state="+appID;
        log.info("ControllerQYWeixin.doAppInstallAuth,url={}",url);
        response.sendRedirect(url);
    }

    /**
     * 使用 doAppInstallAuth()安装完成后，企业微信回调此接口。
     * http://open.xxx.com/qyweixin/callback/installAuth(),
     * 然后我们再回调北研的登录模板页接口，实现登录
     * http://www.xxx.com/hcrm/wechat/login?ticket=xxx
     *
     * 应用授权回调
     * @param auth_code：企业微信传过来的当前用户加密过的身份
     * @param from_ea 来源纷享EA，主要用于专属云，比如紫光云等
     * @param from_origin 来源域名，比如https://www.fxiaoke.com
     * @param expires_in: auth_code剩余有效时间
     * @param state：防止跨站请求攻击的参数。
     * @return: 无
     */
//    @RequestMapping(value = "/callback/installAuth", method = RequestMethod.GET)
//    public void installAuth(@RequestParam String auth_code,
//                            @RequestParam(required = false) String from_ea,
//                            @RequestParam(required = false) String from_origin,
//                            @RequestParam Long expires_in,
//                            @RequestParam(required = false) String state,
//                            HttpServletResponse response) throws IOException {
//        String fsEa = null;
//        if(UserContextSingleton.INSTANCE.getUserContext()!=null) {
//            fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        } else {
//            fsEa = from_ea;
//        }
//
//        if(StringUtils.isNotEmpty(from_origin)) {
//            from_origin = URL.decode(from_origin);
//        } else {
//            from_origin="";
//        }
//
//        log.info("ControllerQYWeixin.installAuth,authCode:{},from_ea:{},fsEa:{},from_origin={},state:{}, userContext:{}",
//                auth_code,
//                from_ea,
//                fsEa,
//                from_origin,
//                state,
//                UserContextSingleton.INSTANCE.getUserContext());
//
//        Result<String> ticket = qyweixinGatewayService.getAuthInfoDoInitCorp(auth_code, state,fsEa);
//        log.info("ControllerQYWeixin.installAuth,ticket={}",ticket);
//        if(!ticket.isSuccess()){
//            log.info("ControllerQYWeixin.installAuth,ticket.isSuccess()=false");
//            throw new RuntimeException(ticket.getErrorMsg());
//        }
//        String loginUrl = getRedirectLoginUrl(ticket.getData(), state,"",from_origin);
//        log.info("ControllerQYWeixin.installAuth,loginUrl={}",loginUrl);
//        response.sendRedirect(loginUrl);
//    }

    /**
     * 获取登录模板页跳转地址。
     * */
    private String getRedirectLoginUrl(String ticket, String appId,String corpId,String fromOrigin, String fsEa) {
        if(StringUtils.isNotEmpty(fromOrigin)) {
            fromOrigin = URL.decode(fromOrigin);
            if(!fromOrigin.endsWith("/")) {
                fromOrigin+="/";
            }
        } else {
            fromOrigin="";
        }
        log.info("ControllerQYWeixin.getRedirectLoginUrl,ticket={},appId={},corpId={},fromOrigin={}",ticket,appId,corpId,fromOrigin);

        String redirectLoginUrl = qyweixinGatewayService.getRedirectLoginUrl(ticket, appId, corpId,fromOrigin, fsEa);

        log.info("ControllerQYWeixin.getRedirectLoginUrl,redirectLoginUrl={}", redirectLoginUrl);

        return redirectLoginUrl;
    }

    /**
     * 注册定制化(需要应用上线，才能测试的。因为配置模板id，需要选择上线应用)
     */
    @RequestMapping(value = "/registerCorp")
    public void registerCorp(@RequestParam String templateId, HttpServletResponse response) throws IOException {
        /**这里强行用配置中心的注册定制化模板ID覆盖。因为官网企业微信CRM频道 前端传过来的值，有时候会带有随机字符。
         * 怀疑是浏览器兼容的问题，但是已经找不到负责这块的前端去查问题了。
        */
        templateId=regTemplateId;
        String registerCode = qyweixinGatewayService.getRegisterCode(templateId);
        String registerUrl = "https://open.work.weixin.qq.com/3rdservice/wework/register?register_code="+registerCode;
        response.sendRedirect(registerUrl);
    }

    /**
     * 官网使用企业微信账号登陆纷享的button后面的链接是
     * https://open.xxx.com/qyweixin/doLoginAuth?appID=wxyyy
     *
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doLoginAuth", method = RequestMethod.GET)
    public void doLoginAuth(HttpServletResponse response,
                            @RequestHeader(value = "user-agent") String userAgent) throws IOException {
        log.info("ControllerQYWeixin.doLoginAuth,userAgent={}",userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("ControllerQYWeixin.doLoginAuth,lang={}",lang);
        String redirect_uri= URL.encode(domainPrefix + "/qyweixin/callback/loginAuth?lang="+lang);
        String url ="https://open.work.weixin.qq.com/wwopen/sso/3rd_qrConnect?appid="+corpId+"&redirect_uri="+redirect_uri+"&state=&usertype=member";
        response.sendRedirect(url);
    }



    /**
     *使用 doLoginAuth()进行单点登录，用户确认授权以后，企业微信回调纷享的地址。
     * https://open.xxx.com/qyweixin/callback/loginAuth
     *
     * 场景1：从纷享官网，使用企业微信账号登录回调
     * 场景2：从企业微信上，CRM应用详情->登录到服务商后台
     *
     * @param auth_code: 微信回传授权码
     * @param state: 即appId
     * @param from_origin 来源域名，比如https://www.fxiaoke.com
     * @return：无
     */
    @RequestMapping(value = "/callback/loginAuth", method = RequestMethod.GET)
    public void loginAuth(@RequestParam(required = false) String auth_code,
                          @RequestParam(required = false) String from_origin,
                          @RequestParam(required = false) String state,
                          @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                          @RequestParam(required = false) String userInfo,
                          HttpServletResponse response,
                          HttpServletRequest request) throws Exception {
        log.info("ControllerQYWeixin.loginAuth,lang={}",lang);
//        if(StringUtils.isEmpty(state)) {
//            state = ConfigCenter.crmAppId;
//        }
        log.info("ControllerQYWeixin.loginAuth,auth_code={},from_origin={},state={},lang={}",auth_code,from_origin,state,lang);
        QyweixinLoginInfoRsp loginInfoRsp = null;
        if (StringUtils.isNotEmpty(auth_code) && StringUtils.isEmpty(userInfo)) {
            //1.用code换企业微信用户信息
            Result<QyweixinLoginInfoRsp> result = qyweixinGatewayService.code2WebLoginUserInfo(auth_code);
            log.info("ControllerQYWeixin.loginAuth,result={}",result);
            i18NStringManager.setDefaultRequestScope(request,lang);
            if(!result.isSuccess()) {
                request.setAttribute("errorCode", result.getErrorCode());
                request.setAttribute("errorMsg", result.getErrorMsg());
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s120,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            loginInfoRsp = result.getData();
        } else {
            String pramUrl = new String(Base64.decodeBase64(userInfo.getBytes()));
            loginInfoRsp = com.alibaba.fastjson2.JSON.parseObject(pramUrl, QyweixinLoginInfoRsp.class);
        }

        String outEa = loginInfoRsp.getCorp_info().getCorpid();
        String userId = loginInfoRsp.getUser_info().getUserid();

        log.info("ControllerQYWeixin.loginAuth,outEa={},userId={}",outEa,userId);

        outEa = qyweixinManager.corpId2OpenCorpId(outEa).getData();

        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(outEa)) {
            log.info("ControllerQYWeixin.loginAuth,redirect to new base,outEa={},userId={}",outEa,userId);
            return;
        }

        //有跨云逻辑不能直接查询人员绑定关系作为开始
        //优先查询企业绑定关系
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(state).build());

        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(state)
                    .channelId(ChannelEnum.qywx.name())
                    .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(outEa)
                    .outUserId(userId)
                    .errorCode("100")
                    .errorMsg(i18NStringManager.get(I18NStringEnum.s155,lang,null))
                    .build();
            qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
            String msg = i18NStringManager.get2(I18NStringEnum.s125.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s125.getI18nValue(),outEa,userId,TraceUtil.get()),
                    Lists.newArrayList(
                            outEa, userId, TraceUtil.get()
                    ));
            arg.setMsg(msg);
            notificationService.sendQYWXNotice(arg);

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s126.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s126.getI18nValue(),outEa,userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s127,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        //如果state为空，看下具体的appId

        String domain = domainPrefix;

        //获取正常状态下的企业列表
        List<OuterOaEnterpriseBindEntity> normalEnterpriseBindEntities = enterpriseBindEntities.stream()
                .filter(v -> v.getBindStatus() == BindStatusEnum.normal)
                .collect(Collectors.toList());

        boolean hasMore = Boolean.FALSE;
        List<OuterOaEmployeeBindEntity> employeeBindEntities = null;

        if(normalEnterpriseBindEntities.size() > 1) {
            //有跨云的企业不用
            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : normalEnterpriseBindEntities) {
                QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
                if (StringUtils.isNotEmpty(qywxConnectorVo.getDomain()) && !qywxConnectorVo.getDomain().equals(domainPrefix)) {
                    //取第一个appId
                    if (StringUtils.isEmpty(state)) {
                        state = enterpriseBindEntity.getAppId();
                    }
                    hasMore = Boolean.TRUE;
                    break;
                }
            }

            if (!hasMore) {
                //兼容老逻辑，看下人员绑定关系
                employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(state).outEmpId(userId).build());
                log.info("ControllerQYWeixin.loginAuth,employeeBindEntities={}", employeeBindEntities);
                //取第一个appId
                if (CollectionUtils.isNotEmpty(employeeBindEntities) && StringUtils.isEmpty(state)) {
                    state = employeeBindEntities.get(0).getAppId();
                }
                if (employeeBindEntities.size() > 1) {
                    hasMore = Boolean.TRUE;
                }
            }
        }
        log.info("ControllerQYWeixin.loginAuth,hasMore={}",hasMore);

        //一对多
        if(normalEnterpriseBindEntities.size() > 1 && hasMore) {
            //在纷享环境处理
            response.sendRedirect(domain + "/pc-login/build/select_enterprise.html?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+state), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8"));
        } else if(CollectionUtils.isEmpty(normalEnterpriseBindEntities)) {
            //没有正常的
            List<OuterOaEnterpriseBindEntity> createEnterpriseBindEntityList = enterpriseBindEntities.stream()
                    .filter(v -> v.getBindStatus() == BindStatusEnum.create)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(createEnterpriseBindEntityList)) {
                //只有停用状态的企业
                throw new RuntimeException(i18NStringManager.get(I18NStringEnum.s129,lang,null));
            }
            log.info("ControllerQYWeixin.loginAuth,createEnterpriseBindEntityList={}",createEnterpriseBindEntityList);
            long time = System.currentTimeMillis() - createEnterpriseBindEntityList.get(0).getCreateTime();
            log.info("ControllerQYWeixin.loginAuth,time={}",time);
            if(time > 15 * 1000) {
                //开通中的
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .appId(state)
                        .channelId(ChannelEnum.qywx.name())
                        .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .ea(createEnterpriseBindEntityList.get(0).getFsEa())
                        .corpId(outEa)
                        .outUserId(userId)
                        .errorCode("101")
                        .errorMsg(i18NStringManager.get(I18NStringEnum.s123,lang,null))
                        .build();
                qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                String msg = String.format(I18NStringEnum.s143.getI18nValue(), outEa, userId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get());
                arg.setMsg(i18NStringManager.get2(I18NStringEnum.s143.getI18nKey(),lang,null,msg,Lists.newArrayList(
                        outEa, userId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get()
                )));
                notificationService.sendQYWXNotice(arg);
            }

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s149.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s149.getI18nValue(),outEa,userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s144,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
        } else {
            //只有一对一，特殊逻辑，还需要查询
            OuterOaEnterpriseBindEntity enterpriseBindEntity = normalEnterpriseBindEntities.get(0);
            if(normalEnterpriseBindEntities.size() > 1 && CollectionUtils.isNotEmpty(employeeBindEntities)) {
                log.info("ControllerQYWeixin.appAuth, hasMore={}, employeeBindEntities={}", hasMore, employeeBindEntities);
                String fsAccount = employeeBindEntities.get(0).getFsEa();

                // 使用 Stream API 查找匹配项
                Optional<OuterOaEnterpriseBindEntity> matchingMapping = normalEnterpriseBindEntities.stream()
                        .filter(mapping -> mapping.getFsEa().equals(fsAccount))
                        .findFirst();

                if(matchingMapping.isPresent()) {
                    enterpriseBindEntity = matchingMapping.get();
                }
            }
            log.info("ControllerQYWeixin.loginAuth,enterpriseMapping={}", enterpriseBindEntity);
            QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
            domain = qywxConnectorVo.getDomain();
            if(ObjectUtils.isNotEmpty(domain) && !domain.equals(domainPrefix)) {
                //转到对应的环境处理
                String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+ URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+state), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+enterpriseBindEntity.getFsEa()), "utf-8");
                response.sendRedirect(redirectUri);
                return;
            }
            //查询人员是否正常
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), state, userId);
            log.info("AuthController.callbackAuth,employeeBindEntity={}",employeeBindEntity);
            //如果是自动绑定的企业
//            if(ObjectUtils.isEmpty(employeeBindEntity)) {
//                if (enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
//                    try {
//                        //如果人员免登录时，发现人员没有自动绑定，默认替客户创建人员并建立绑定关系
//                        Result<Void> addUserResult = contactBindInnerService.addUser(enterpriseBindEntity, userId);
//                        log.info("ControllerQYWeixin.loginAuth,addUserResult={}", addUserResult);
//                        if(addUserResult.isSuccess()) {
//                            employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), state, userId);
//                            log.info("ControllerQYWeixin.loginAuth,employeeBindEntity2={}", employeeBindEntity);
//                        } else {
//                            //有些失败已经确定，不需要处理
//                            if(addUserResult.getErrorCode().equalsIgnoreCase(ErrorRefer.QYWX_NOT_PRIVILEGE_ERROR.getCode())) {
//                                request.setAttribute("errorCode", "s320050002");
//                                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s148.getI18nKey(),
//                                        lang,
//                                        null,
//                                        String.format(I18NStringEnum.s148.getI18nValue(), outEa, userId),
//                                        Lists.newArrayList(
//                                                outEa, userId
//                                        )));
//                                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s156,lang,null));
//                                request.getRequestDispatcher("/index.jsp").forward(request, response);
//                                return;
//                            } else if(addUserResult.getErrorCode().equalsIgnoreCase(ErrorRefer.CRM_USER_UPPER_LIMIT_INITED.getCode())) {
//                                request.setAttribute("errorCode", "s320050002");
//                                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s148.getI18nKey(),
//                                        lang,
//                                        null,
//                                        String.format(I18NStringEnum.s148.getI18nValue(), outEa, userId),
//                                        Lists.newArrayList(
//                                                outEa, userId
//                                        )));
//                                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s157,lang,null));
//                                request.getRequestDispatcher("/index.jsp").forward(request, response);
//                                return;
//                            }
//                        }
//                    } catch (Exception e) {
//                        log.info("ControllerQYWeixin.loginAuth,addUserList exception={}", e.getMessage());
//                    }
//                }
//            }

            if(ObjectUtils.isEmpty(employeeBindEntity)) {
//                if(enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
//                    //上报
//                    OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
//                            .appId(state)
//                            .channelId(ChannelEnum.qywx.name())
//                            .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
//                            .ea(enterpriseBindEntity.getFsEa())
//                            .corpId(outEa)
//                            .outUserId(userId)
//                            .errorCode("102")
//                            .errorMsg(i18NStringManager.get(I18NStringEnum.s130,lang,null))
//                            .build();
//                    qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
//                    //告警
//                    SendTextNoticeArg arg = new SendTextNoticeArg();
//                    arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
//                    List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
//                    arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
//                    arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
//                    String msg = String.format(I18NStringEnum.s145.getI18nValue(), outEa, userId, enterpriseBindEntity.getFsEa(), TraceUtil.get());
//                    arg.setMsg(i18NStringManager.get2(I18NStringEnum.s145.getI18nKey(),lang,null,msg,Lists.newArrayList(
//                            outEa, userId, enterpriseBindEntity.getFsEa(), TraceUtil.get()
//                    )));
//                    notificationService.sendQYWXNotice(arg);
//
//                    request.setAttribute("errorCode", "s320050002");
//                    request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
//                        lang,
//                        null,
//                        String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
//                        Lists.newArrayList(
//                                outEa, userId
//                        )));
//                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s131,lang,null));
//                    request.setAttribute("errorPageOutEa", outEa);
//                    request.setAttribute("errorPageFsEa", enterpriseBindEntity.getFsEa());
//                    request.setAttribute("errorPageOutUserId", userId);
//                    request.setAttribute("errorPageToken", ConfigCenter.ERROR_PAGE_TOKEN);
//                    request.setAttribute("errorPageAppId", state);
//                    request.setAttribute("createEmployee", Boolean.TRUE);
//                    request.getRequestDispatcher("/index.jsp").forward(request, response);
//                    return;
//                }
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                        Lists.newArrayList(
                                outEa, userId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            if(employeeBindEntity.getBindStatus() == BindStatusEnum.stop) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s150.getI18nValue(),outEa,userId),
                        Lists.newArrayList(
                                outEa, userId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
//                if(enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
//                    //启用
//                    Result<Void> addUserResult = contactBindInnerService.updateUser(enterpriseBindEntity, userId);
//                    if(addUserResult.isSuccess()) {
//                        employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), state, userId);
//                    } else {
//                        request.setAttribute("errorCode", "s320050002");
//                        request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
//                                lang,
//                                null,
//                                String.format(I18NStringEnum.s150.getI18nValue(),outEa,userId),
//                                Lists.newArrayList(
//                                        outEa, userId
//                                )));
//                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
//                        request.getRequestDispatcher("/index.jsp").forward(request, response);
//                        return;
//                    }
//                } else {
//                    request.setAttribute("errorCode", "s320050002");
//                    request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
//                            lang,
//                            null,
//                            String.format(I18NStringEnum.s150.getI18nValue(),outEa,userId),
//                            Lists.newArrayList(
//                                    outEa, userId
//                            )));
//                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
//                    request.getRequestDispatcher("/index.jsp").forward(request, response);
//                    return;
//                }
            }
            //查询人员在crm的状态
            Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(enterpriseBindEntity.getFsEa(), 1000, Lists.newArrayList(Integer.parseInt(employeeBindEntity.getFsEmpId())));
            if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s133,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s151.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s151.getI18nValue(),enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()),
                        Lists.newArrayList(
                                enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()
                        )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            } else if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s134,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s152.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s152.getI18nValue(),enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()),
                        Lists.newArrayList(
                                enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()
                        )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //正常登陆
            //4.如果一个企微企业只关联一个CRM企业，直接重定向到CRM登录页面
            Result<CorpTicketResult> ticket = qyweixinGatewayService.loginAuth(loginInfoRsp, state,employeeBindEntity.getFsEa());
            log.info("AuthController.callbackAuth,ticket={}",ticket);
            if(!ticket.isSuccess()) {
                request.setAttribute("errorCode", ticket.getErrorCode());
                request.setAttribute("errorMsg", ticket.getErrorMsg());
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //为兼容专属云老企业，还不能直接去掉
            if(StringUtils.isEmpty(from_origin)) {
                GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(employeeBindEntity.getFsEa());
                if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
                    domain = enterpriseInfo.getEnterpriseData().getDomain();
                }
                from_origin = domain;
                log.info("AuthController.callbackAuth,from_origin={}",from_origin);
            }

            String loginUrl = getRedirectLoginUrl(ticket.getData().getTicket(), state,ticket.getData().getCorpId(),from_origin, employeeBindEntity.getFsEa());
            log.info("ControllerQYWeixin.loginAuth,loginUrl={}",loginUrl);
            response.sendRedirect(loginUrl);
        }
    }


    /**
     * 企业微信APP,应用登录入口
     * 然后浏览器重定向到 https://open.xxx.com/qyweixin/callback/appAuth
     * @return
     */
    @RequestMapping(value = "/doAppLogin", method = RequestMethod.GET)
    public void doAppLogin(@RequestParam String appID,
                           @RequestHeader(value = "user-agent") String userAgent,
                           HttpServletResponse response,
                           HttpServletRequest request) throws IOException {
        log.info("ControllerQYWeixin.doAppLogin,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("ControllerQYWeixin.doAppLogin,lang={}", lang);
        String redirect_uri = URL.encode(domainPrefix + "/qyweixin/callback/appAuth?lang="+lang);
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+appID+"&redirect_uri="+redirect_uri+"&response_type=code&scope="+appLoginAuthScope+"&state="+appID+"#wechat_redirect";
        log.info("trace doAppLogin, get url:{} ", url);
        response.sendRedirect(url);
    }

    /**
     * 企业微信APP,应用登录入口
     * 然后浏览器重定向到 https://open.xxx.com/qyweixin/callback/appAuth
     * @param appId 代开发应用ID
     * @param corpId 企微企业ID
     * @param go2Crm 是否进入CRM主页，代开发应用模拟三方应用
     * @return
     */
    @RequestMapping(value = "/doRepAppLogin", method = RequestMethod.GET)
    public void doRepAppLogin(@RequestParam String corpId,
                              @RequestParam String appId,
                              @RequestParam(required = false) boolean go2Crm,
                              @RequestParam(required = false) String browserLocation,
                              @RequestHeader(value = "user-agent") String userAgent,
                              HttpServletRequest request,
                              HttpServletResponse response) throws Exception {
        log.info("ControllerQYWeixin.doRepAppLogin,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("ControllerQYWeixin.doRepAppLogin,lang={}", lang);
        i18NStringManager.setDefaultRequestScope(request,lang);
        String redirect_uri = URL.encode(domainPrefix + "/qyweixin/callback/repAppAuth?lang="+lang);
        if(go2Crm) {
            redirect_uri = URL.encode(domainPrefix + "/qyweixin/callback/appAuth?lang="+lang);
        }
        String state = appId + "@" + corpId;

        corpId = qyweixinManager.corpId2OpenCorpId(corpId).getData();
        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(corpId)) {
            log.info("ControllerQYWeixin.doRepAppLogin,redirect to new base,outEa={}",corpId);
            return;
        }

        List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).build());
        log.info("ControllerQYWeixin.doRepAppLogin,appInfoEntities={}", appInfoEntities);

        if(CollectionUtils.isEmpty(appInfoEntities)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s117,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s118,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        OuterOaAppInfoEntity appInfoEntity = appInfoEntities.get(0);
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);


        String agentId = String.valueOf(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());

        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+corpId+"&redirect_uri="+redirect_uri+"&response_type=code&scope=snsapi_privateinfo&state="+state+"&agentid=" +agentId+"#wechat_redirect";
        log.info("trace doRepAppLogin, get url:{} ", url);
        if (StringUtils.isNotEmpty(browserLocation) && browserLocation.equalsIgnoreCase("default")) {
            request.setAttribute("corpId", corpId);
            request.setAttribute("appId", appId);
            request.setAttribute("loginRedirectUrl", URLEncoder.encode(url, "UTF-8"));
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("loginErrorMsg", i18NStringManager.get(I18NStringEnum.s121,lang,null));
            request.setAttribute("loginErrorPropose", i18NStringManager.get(I18NStringEnum.s206,lang,null));
            request.setAttribute("loginSuccess", i18NStringManager.get(I18NStringEnum.s205,lang,null));
            request.setAttribute("createJsapiSignatureErrorMsg", i18NStringManager.get(I18NStringEnum.s74,lang,null));

            // 转发到 login.jsp
            request.getRequestDispatcher("/WEB-INF/login/login.jsp").forward(request, response);
            return;
        }
        response.sendRedirect(url);
    }

    /**
     * 代开发应用登录入口
     * @param code
     * @param state = appId
     * @param response
     * @param request
     * @throws Exception
     */
    @RequestMapping(value = "/callback/repAppAuth")
    public void repAppAuth(@RequestParam String code,
                           @RequestParam String state,
                           @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                           HttpServletResponse response,
                           HttpServletRequest request) throws Exception {
        log.info("ControllerQYWeixin.repAppAuth,code={},state={},lang={}", code, state,lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        List<String> items = Splitter.on("@").splitToList(state);
        String appId = items.get(0);
        String corpId = items.get(1);

        corpId = qyweixinManager.corpId2OpenCorpId(corpId).getData();
        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(corpId)) {
            log.info("ControllerQYWeixin.repAppAuth,redirect to new base,corpId={}",corpId);
            return;
        }

        //1.用code换企业微信用户信息
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("code", code);
        dataMap.put("appId", appId);
        dataMap.put("outEa", corpId);
		dataMap.put("isIsvCode", Boolean.FALSE);
        MethodContext context = MethodContext.newInstance(dataMap);

        qyweixinLoginTemplate.getOutUserInfoByCode(context);

        Result<QyweixinRepUserDetailInfoRsp> result = (Result<QyweixinRepUserDetailInfoRsp>) context.getResult().getData();
//        Result<QyweixinRepUserDetailInfoRsp> result = qyweixinGatewayService.getRepAppLoginUserInfo(appId, corpId, code);
        log.info("ControllerQYWeixin.repAppAuth,result={}", result);
        if (!StringUtils.equalsIgnoreCase(result.getErrorCode(),"0")) {
            request.setAttribute("errorCode", result.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(result.getErrorMsg()) ? result.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s119,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s120,lang,null));

            log.info("ControllerQYWeixin.repAppAuth,redirect to index.jsp");
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        EnterpriseWechatUserModel userModel = new EnterpriseWechatUserModel();
        userModel.setAppId(appId);
        userModel.setCorpId(corpId);
        userModel.setUserId(result.getData().getUserid());
        userModel.setMobile(result.getData().getMobile());
        qyweixinGatewayService.sendEnterpriseWechatUserInfo(userModel);

        Map<String, String> repAppMainPageMap = JSONObject.parseObject(repAppMainPage, Map.class);
        log.info("ControllerQYWeixin.repAppAuth,repAppMainPageMap={}", repAppMainPageMap);
        if (repAppMainPageMap != null) {
            String mainPageUrl = repAppMainPageMap.get(appId);
            log.info("ControllerQYWeixin.repAppAuth,mainPageUrl={}", mainPageUrl);
            if (StringUtils.isNotEmpty(mainPageUrl)) {
                mainPageUrl = StringUtils.contains(mainPageUrl, "?") ? mainPageUrl : mainPageUrl + "?";
                mainPageUrl += "phone=" + result.getData().getMobile();
                log.info("ControllerQYWeixin.repAppAuth,mainPageUrl.2={}", mainPageUrl);
                response.sendRedirect(mainPageUrl);
            }
        }
    }

    /**
     * 企业微信内从工作台 免登录跳转到纷享CRM。
     * 企业微信先跳转到 https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx4c7edab730f4fdc9&redirect_uri=http%3A%2F%2Fopen.ceshi113.com%2Fqyweixin%2Fcallback%2FappAuth&response_type=code&scope=snsapi_base&state=wx4c7edab730f4fdc9#wechat_redirect
     * 然后浏览器重定向到 https://open.xxx.com/qyweixin/callback/appAuth
     * 然后再进行一次浏览器重定向：http://www.xxx.com/hcrm/wechat/login？ticket=xxx 就实现了登录纷享
     *
     * @param code
     * @param state 传递appId
     * @param from_origin 来源域名，比如https://www.fxiaoke.com
     * @return
     */
    @RequestMapping(value = "/callback/appAuth")
    public void appAuth(@RequestParam(required = false) String code,
                        @RequestParam String state,
                        @RequestParam(required = false) String from_origin,
                        @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                        @RequestParam(required = false) String userInfoStr,
                        HttpServletResponse response,
                        HttpServletRequest request) throws Exception {
        StopWatch stopWatch = StopWatch.createStarted("total");
        log.info("ControllerQYWeixin.appAuth,code={},from_origin={},state={},lang={}",code,from_origin,state,lang);

        i18NStringManager.setDefaultRequestScope(request,lang);

        String appId = state;
        String outEa = null;
        if(StringUtils.startsWithIgnoreCase(state,"dk")) {
            List<String> items = Splitter.on("@").splitToList(state);
            appId = items.get(0);
            outEa = items.get(1);
            state = appId;
        }

        Object userInfo = null;
        if (StringUtils.isNotEmpty(code) && StringUtils.isEmpty(userInfoStr)) {
            //1.用code换企业微信用户信息
            StopWatch stopWatch1 = StopWatch.createStarted("code2AppLoginUserInfo");

            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("code", code);
            dataMap.put("appId", appId);
            dataMap.put("outEa", outEa);
            dataMap.put("isIsvCode", Boolean.TRUE);
            MethodContext context = MethodContext.newInstance(dataMap);

            qyweixinLoginTemplate.getOutUserInfoByCode(context);

            Result<Object> result = (Result<Object>) context.getResult().getData();

//        Result<Object> result = qyweixinGatewayService.code2AppLoginUserInfo(code, appId, outEa);
            log.info("ControllerQYWeixin.appAuth,result={}",result);
            stopWatch1.stop();
            log.info("ControllerQYWeixin.appAuth,code2AppLoginUserInfo,cost time={}",stopWatch1.getTotalTimeMillis());
            if(!result.isSuccess() || ObjectUtils.isEmpty(result.getData())) {
                request.setAttribute("errorCode", result.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(result.getErrorMsg()) ? result.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s119,lang,null));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s120,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }
            userInfo = result.getData();
        } else {
            String pramUrl = new String(Base64.decodeBase64(userInfoStr.getBytes()));
            JSONObject jsonObject = JSON.parseObject(pramUrl);
            String type = jsonObject.getString("type"); // 获取类型信息
            Class<?> clazz = Class.forName(type); // 加载类
            userInfo = jsonObject.getObject("data", clazz);
            log.info("ControllerQYWeixin.appAuth,userInfoStr={},userInfo={}",userInfoStr,userInfo);
        }

        //String outEa = null;
        String upstreamOutEa = null;
        String userId = null;
        String mobile = null;
        if (userInfo instanceof QyweixinUserDetailInfoRsp) {
            QyweixinUserDetailInfoRsp userDetailInfoRsp = (QyweixinUserDetailInfoRsp) userInfo;
            outEa = userDetailInfoRsp.getCorpid();
            upstreamOutEa = outEa;
            userId = userDetailInfoRsp.getUserid();
            mobile = userDetailInfoRsp.getMobile();
            if(StringUtils.containsIgnoreCase(userId,"/")) {
                List<String> items = Splitter.on("/").splitToList(userId);
                outEa = items.get(0);
                userId = items.get(1);
                userDetailInfoRsp.setCorpid(outEa);
                userDetailInfoRsp.setUserid(userId);
                log.info("ControllerQYWeixin.appAuth,newOutEa={},newUserId={}",outEa,userId);
            }
        } else if(userInfo instanceof QyweixinUserSimpleInfoRsp) {
            QyweixinUserSimpleInfoRsp userSimpleInfoRsp = (QyweixinUserSimpleInfoRsp) userInfo;
            outEa = userSimpleInfoRsp.getCorpId();
            upstreamOutEa = outEa;
            userId = userSimpleInfoRsp.getUserId();
            if(StringUtils.containsIgnoreCase(userId,"/")) {
                List<String> items = Splitter.on("/").splitToList(userId);
                outEa = items.get(0);
                userId = items.get(1);
                userSimpleInfoRsp.setCorpId(outEa);
                userSimpleInfoRsp.setUserId(userId);
                log.info("ControllerQYWeixin.appAuth,newOutEa={},newUserId={}",outEa,userId);
            }
        }
        log.info("ControllerQYWeixin.appAuth,upstreamOutEa={},outEa={},userId={}",upstreamOutEa,outEa,userId);

        outEa = qyweixinManager.corpId2OpenCorpId(outEa).getData();
        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(outEa)) {
            log.info("ControllerQYWeixin.appAuth,redirect to new base,outEa={},userId={}",outEa,userId);
            return;
        }

        //有跨云逻辑不能直接查询人员绑定关系作为开始
        //优先查询企业绑定关系
        StopWatch stopWatch2 = StopWatch.createStarted("selectAllEnterpriseBind");
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).build());
        stopWatch2.stop();
        log.info("ControllerQYWeixin.appAuth,selectAllEnterpriseBind,cost time={}",stopWatch2.getTotalTimeMillis());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            //上报
            OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                    .appId(appId)
                    .channelId(ChannelEnum.qywx.name())
                    .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                    .corpId(outEa)
                    .outUserId(userId)
                    .errorCode("100")
                    .errorMsg(i18NStringManager.get(I18NStringEnum.s155,lang,null))
                    .build();
            qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
            //告警
            SendTextNoticeArg arg = new SendTextNoticeArg();
            arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
            List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
            arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
            arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
            String msg = i18NStringManager.get2(I18NStringEnum.s125.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s125.getI18nValue(),outEa,userId,TraceUtil.get()),
                    Lists.newArrayList(
                            outEa, userId, TraceUtil.get()
                    ));
            arg.setMsg(msg);
            notificationService.sendQYWXNotice(arg);

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s126.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s126.getI18nValue(),outEa,userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s127,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        String domain = domainPrefix;

        //获取正常状态下的企业列表
        List<OuterOaEnterpriseBindEntity> normalEnterpriseBindEntities = enterpriseBindEntities.stream()
                .filter(v -> v.getBindStatus() == BindStatusEnum.normal)
                .collect(Collectors.toList());

        boolean hasMore = Boolean.FALSE;
        List<OuterOaEmployeeBindEntity> employeeBindEntities = null;

        if(normalEnterpriseBindEntities.size() > 1) {
            //有跨云的企业不用
            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : normalEnterpriseBindEntities) {
                QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
                if (StringUtils.isNotEmpty(qywxConnectorVo.getDomain()) && !qywxConnectorVo.getDomain().equals(domainPrefix)) {
                    //取第一个appId
                    if (StringUtils.isEmpty(appId)) {
                        appId = enterpriseBindEntity.getAppId();
                    }
                    hasMore = Boolean.TRUE;
                    break;
                }
            }

            if (!hasMore) {
                //兼容老逻辑，看下人员绑定关系
                employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).outEmpId(userId).build());
                log.info("ControllerQYWeixin.loginAuth,employeeBindEntities={}", employeeBindEntities);
                //取第一个appId
                if (CollectionUtils.isNotEmpty(employeeBindEntities) && StringUtils.isEmpty(appId)) {
                    appId = employeeBindEntities.get(0).getAppId();
                }
                if (employeeBindEntities.size() > 1) {
                    hasMore = Boolean.TRUE;
                }
            }
        }
        log.info("ControllerQYWeixin.appAuth,hasMore={}",hasMore);

        //一对多
        if(normalEnterpriseBindEntities.size() > 1 && hasMore) {
            //在纷享环境处理
            response.sendRedirect(domain + "/pc-login/build/select_enterprise.html?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8"));
        } else if(CollectionUtils.isEmpty(normalEnterpriseBindEntities)) {
            //没有正常的
            List<OuterOaEnterpriseBindEntity> createEnterpriseBindEntityList = enterpriseBindEntities.stream()
                    .filter(v -> v.getBindStatus() == BindStatusEnum.create)
                    .collect(Collectors.toList());
            if(CollectionUtils.isEmpty(createEnterpriseBindEntityList)) {
                //只有停用状态的企业
                throw new RuntimeException(i18NStringManager.get(I18NStringEnum.s129,lang,null));
            }
            log.info("ControllerQYWeixin.appAuth,createEnterpriseBindEntityList={}",createEnterpriseBindEntityList);
            long time = System.currentTimeMillis() - createEnterpriseBindEntityList.get(0).getCreateTime();
            log.info("ControllerQYWeixin.appAuth,time={}",time);
            if(time > 15 * 1000) {
                //开通中的
                //上报
                OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                        .appId(appId)
                        .channelId(ChannelEnum.qywx.name())
                        .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                        .ea(createEnterpriseBindEntityList.get(0).getFsEa())
                        .corpId(outEa)
                        .outUserId(userId)
                        .errorCode("101")
                        .errorMsg(i18NStringManager.get(I18NStringEnum.s123,lang,null))
                        .build();
                qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                //告警
                SendTextNoticeArg arg = new SendTextNoticeArg();
                arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                List<String> receivers = new LinkedList<>(ConfigCenter.ENTERPRISE_OPEN_NOTIFICATION_MEMBERS);
                arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                String msg = i18NStringManager.get2(I18NStringEnum.s143.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s143.getI18nValue(),outEa, userId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get()),
                        Lists.newArrayList(
                                outEa, userId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get()
                        ));
                arg.setMsg(i18NStringManager.get2(I18NStringEnum.s143.getI18nKey(),lang,null,msg,Lists.newArrayList(
                        outEa, userId, createEnterpriseBindEntityList.get(0).getFsEa(), TraceUtil.get()
                )));
                notificationService.sendQYWXNotice(arg);
            }

            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s149.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s149.getI18nValue(),outEa,userId),
                    Lists.newArrayList(
                            outEa, userId
                    )));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s144,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
        } else {
            //只有一对一，特殊逻辑，还需要查询
            OuterOaEnterpriseBindEntity enterpriseBindEntity = normalEnterpriseBindEntities.get(0);
            if(normalEnterpriseBindEntities.size() > 1 && CollectionUtils.isNotEmpty(employeeBindEntities)) {
                log.info("ControllerQYWeixin.appAuth, hasMore={}, employeeBindEntities={}", hasMore, employeeBindEntities);
                String fsAccount = employeeBindEntities.get(0).getFsEa();

                // 使用 Stream API 查找匹配项
                Optional<OuterOaEnterpriseBindEntity> matchingMapping = normalEnterpriseBindEntities.stream()
                        .filter(mapping -> mapping.getFsEa().equals(fsAccount))
                        .findFirst();

                if(matchingMapping.isPresent()) {
                    enterpriseBindEntity = matchingMapping.get();
                }
            }
            log.info("ControllerQYWeixin.appAuth,enterpriseBindEntity={}", enterpriseBindEntity);
            QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
            domain = qywxConnectorVo.getDomain();
            if(ObjectUtils.isNotEmpty(domain) && !domain.equals(domainPrefix)) {
                //转到对应的环境处理
                String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+appId), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+userId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+enterpriseBindEntity.getFsEa()), "utf-8");
                response.sendRedirect(redirectUri);
                return;
            }
            //查询人员是否正常
            StopWatch stopWatch3 = StopWatch.createStarted("getEmployeeMapping");
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), appId, userId);
            log.info("ControllerQYWeixin.appAuth,employeeBindEntity={}",employeeBindEntity);
            stopWatch3.stop();
            log.info("ControllerQYWeixin.appAuth,getEmployeeMapping,cost time={}",stopWatch3.getTotalTimeMillis());

            if(ObjectUtils.isEmpty(employeeBindEntity)) {
                if (enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
                    try {
                        //如果人员免登录时，发现人员没有自动绑定，默认替客户创建人员并建立绑定关系
                        Result<Void> addUserResult = contactBindInnerService.addUser(enterpriseBindEntity, userId);
                        log.info("ControllerQYWeixin.loginAuth,addUserResult={}", addUserResult);
                        if(addUserResult.isSuccess()) {
                            employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), appId, userId);
                            log.info("ControllerQYWeixin.loginAuth,employeeBindEntity2={}", employeeBindEntity);
                        } else {
                            //有些失败已经确定，不需要处理
                            if(addUserResult.getErrorCode().equalsIgnoreCase(ErrorRefer.QYWX_NOT_PRIVILEGE_ERROR.getCode())) {
                                request.setAttribute("errorCode", "s320050002");
                                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s148.getI18nKey(),
                                        lang,
                                        null,
                                        String.format(I18NStringEnum.s148.getI18nValue(), outEa, userId),
                                        Lists.newArrayList(
                                                outEa, userId
                                        )));
                                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s156,lang,null));
                                request.getRequestDispatcher("/index.jsp").forward(request, response);
                                return;
                            } else if(addUserResult.getErrorCode().equalsIgnoreCase(ErrorRefer.CRM_USER_UPPER_LIMIT_INITED.getCode())) {
                                request.setAttribute("errorCode", "s320050002");
                                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s148.getI18nKey(),
                                        lang,
                                        null,
                                        String.format(I18NStringEnum.s148.getI18nValue(), outEa, userId),
                                        Lists.newArrayList(
                                                outEa, userId
                                        )));
                                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s157,lang,null));
                                request.getRequestDispatcher("/index.jsp").forward(request, response);
                                return;
                            }
                        }
                    } catch (Exception e) {
                        log.info("ControllerQYWeixin.loginAuth,addUserList exception={}", e.getMessage());
                    }
                }
            }

            if(ObjectUtils.isEmpty(employeeBindEntity)) {
                if(enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
                    //上报
                    OAConnectorOpenDataModel model = OAConnectorOpenDataModel.builder()
                            .appId(appId)
                            .channelId(ChannelEnum.qywx.name())
                            .dataTypeId(QYWXDataTypeEnum.EMPLOYEE_LOGIN.getDataType())
                            .ea(enterpriseBindEntity.getFsEa())
                            .corpId(outEa)
                            .outUserId(userId)
                            .errorCode("102")
                            .errorMsg(i18NStringManager.get(I18NStringEnum.s130,lang,null))
                            .build();
                    qyweixinAccountSyncService.uploadOaConnectorOpenData(model);
                    //告警
                    SendTextNoticeArg arg = new SendTextNoticeArg();
                    arg.setEnterpriseAccount(ConfigCenter.NOTIFICATION_EA);
                    List<String> receivers = new LinkedList<>(ConfigCenter.NOTIFICATION_MEMBERS);
                    arg.setReceivers(receivers.stream().map(Integer::valueOf).collect(Collectors.toList()));
                    arg.setMsgTitle(i18NStringManager.get(I18NStringEnum.s124,lang,null));
                    String msg = String.format(I18NStringEnum.s145.getI18nValue(), outEa, userId, enterpriseBindEntity.getFsEa(), TraceUtil.get());
                    arg.setMsg(i18NStringManager.get2(I18NStringEnum.s145.getI18nKey(),lang,null,msg,Lists.newArrayList(
                            outEa, userId, enterpriseBindEntity.getFsEa(), TraceUtil.get()
                    )));
                    notificationService.sendQYWXNotice(arg);

                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                            Lists.newArrayList(
                                    outEa, userId
                            )));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s131,lang,null));
                    request.setAttribute("errorPageOutEa", outEa);
                    request.setAttribute("errorPageFsEa", enterpriseBindEntity.getFsEa());
                    request.setAttribute("errorPageOutUserId", userId);
                    request.setAttribute("errorPageToken", ConfigCenter.ERROR_PAGE_TOKEN);
                    request.setAttribute("errorPageAppId", appId);
                    request.setAttribute("createEmployee", Boolean.TRUE);
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s147.getI18nKey(),
                        lang,
                        null,
                        String.format(I18NStringEnum.s147.getI18nValue(), outEa, userId),
                        Lists.newArrayList(
                                outEa, userId
                        )));
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s132,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            if(employeeBindEntity.getBindStatus() == BindStatusEnum.stop) {
                if(enterpriseBindEntity.getBindType() == BindTypeEnum.auto) {
                    //启用
                    Result<Void> addUserResult = contactBindInnerService.updateUser(enterpriseBindEntity, userId);
                    if(addUserResult.isSuccess()) {
                        employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, enterpriseBindEntity.getFsEa(), appId, userId);
                    } else {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
                                lang,
                                null,
                                String.format(I18NStringEnum.s150.getI18nValue(),outEa,userId),
                                Lists.newArrayList(
                                        outEa, userId
                                )));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
                        request.getRequestDispatcher("/index.jsp").forward(request, response);
                        return;
                    }
                } else {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s150.getI18nKey(),
                            lang,
                            null,
                            String.format(I18NStringEnum.s150.getI18nValue(),outEa,userId),
                            Lists.newArrayList(
                                    outEa, userId
                            )));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s146,lang,null));
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }
            }
            //查询人员在crm的状态
            StopWatch stopWatch4 = StopWatch.createStarted("getFsEmpUser");
            Result<List<EmployeeDto>> fsEmpUserResult = qyweixinAccountSyncService.getFsEmpUser(enterpriseBindEntity.getFsEa(), 1000, Lists.newArrayList(Integer.parseInt(employeeBindEntity.getFsEmpId())));
            stopWatch4.stop();
            log.info("ControllerQYWeixin.appAuth,getFsEmpUser,cost time={}",stopWatch4.getTotalTimeMillis());
            if(!fsEmpUserResult.isSuccess() || ObjectUtils.isEmpty(fsEmpUserResult.getData())) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s133,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s151.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s151.getI18nValue(),enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()),
                    Lists.newArrayList(
                            enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()
                    )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            } else if(fsEmpUserResult.getData().get(0).getStatus() != EmployeeEntityStatus.NORMAL) {
                request.setAttribute("errorCode", fsEmpUserResult.getErrorCode());
                request.setAttribute("errorMsg", StringUtils.isNotEmpty(fsEmpUserResult.getErrorMsg()) ? fsEmpUserResult.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s134,lang,null));
                request.setAttribute("errorMsg", i18NStringManager.get2(I18NStringEnum.s152.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s152.getI18nValue(),enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()),
                    Lists.newArrayList(
                            enterpriseBindEntity.getFsEa(),employeeBindEntity.getFsEmpId()
                    )));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            String fsEa = enterpriseBindEntity.getFsEa();
            String fsUserId = employeeBindEntity.getFsEmpId();
            APLManager.FunctionResult functionResult = aplManager.executeApl3(upstreamOutEa,
                    outEa,
                    userId,
                    fsEa,
                    fsUserId,
                    mobile,
                    appId);
            if(functionResult.getCode()!=0) {
                request.setAttribute("errorCode", "s320050002");
                request.setAttribute("errorMsg", functionResult.getMsg());
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s199,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //正常登陆
            //4.如果一个企微企业只关联一个CRM企业，直接重定向到CRM登录页面
            StopWatch stopWatch5 = StopWatch.createStarted("appAuth");
            Result<CorpTicketResult> ticket = qyweixinGatewayService.appAuth(userInfo, appId,fsEa);
            log.info("ControllerQYWeixin.appAuth,ticket={}",ticket);
            stopWatch5.stop();
            log.info("ControllerQYWeixin.appAuth,appAuth,cost time={}",stopWatch5.getTotalTimeMillis());
            if(!ticket.isSuccess()) {
                request.setAttribute("errorCode", ticket.getErrorCode());
                request.setAttribute("errorMsg", ticket.getErrorMsg());
                request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s135,lang,null));
                request.getRequestDispatcher("/index.jsp").forward(request, response);
                return;
            }

            //为兼容专属云老企业，还不能直接去掉
            if(StringUtils.isEmpty(from_origin)) {
                GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(enterpriseBindEntity.getFsEa());
                if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
                    domain = enterpriseInfo.getEnterpriseData().getDomain();
                }
                from_origin = domain;
                log.info("ControllerQYWeixin.appAuth,from_origin={}",from_origin);
            }

            StopWatch stopWatch6 = StopWatch.createStarted("getRedirectLoginUrl");
            String loginUrl = getRedirectLoginUrl(ticket.getData().getTicket(), appId,ticket.getData().getCorpId(), from_origin, fsEa);
            log.info("ControllerQYWeixin.appAuth,loginUrl={}",loginUrl);
            stopWatch6.stop();
            log.info("ControllerQYWeixin.appAuth,getRedirectLoginUrl,cost time={}",stopWatch6.getTotalTimeMillis());
            response.sendRedirect(loginUrl);
        }
        stopWatch.stop();
        log.info("ControllerQYWeixin.appAuth,total cost time={}",stopWatch.getTotalTimeMillis());
    }

    /**
     * 企业微信内点击纷享应用发送的消息，跳转到纷享后，要获取当前用户的身份自动免登录。
     * 消息中的URL格式如下：
     *
     * http://open.xxx.com/qyweixin/doFunction?
     * param="https://www.ceshi113.com/hcrm/wechat/function/todo?id=fde70df775104dfea9a831a6c0bac08b&apiname=MarketingEventObj"&
     * appID=wx4c7edab730f4fdc9
     *
     * 接收消息模板参数
     * @param param  如：https://www.ceshi112.com/hcrm/cloudhub/function/{}?id=xx&apiname=yy
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doFunction")
    public void doFunction(@RequestParam String param,
                           @RequestParam String appID,
                           @RequestParam(required = false) String fsEa,
                           @RequestHeader(value = "user-agent") String userAgent,
                           HttpServletRequest request,
                           HttpServletResponse response) throws IOException {
        log.info("ControllerQYWeixin.doFunction,userAgent={}", userAgent);
        String lang = I18nUtils.getLang(userAgent);
        log.info("ControllerQYWeixin.doFunction,lang={}", lang);

        try {
            //兼容老待办，从isv转成代开发的场景
            String redirectUrl = new String(Base64.decodeBase64(param.getBytes()));
            if (StringUtils.isEmpty(fsEa)) {
                //一对多场景，CRM待办、审批、提醒等需要从URL参数中取出ea
                fsEa = URL.valueOf(redirectUrl).getParameter("ea");
                log.info("ControllerQYWeixin.doFunction,ea={}", fsEa);
            }
            if (StringUtils.isNotEmpty(fsEa)) {
                //todo：老代办兼容
                //看下是否需要转发
                List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByFsEa(ChannelEnum.qywx, fsEa);
                if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                    Map<String, Map<String, String>> interconnectSecretKeyMap = new Gson().fromJson(ConfigCenter.QYWX_NEED_OLD_MESSAGE, new TypeToken<Map<String, Map<String, String>>>() {
                    });
                    if (!interconnectSecretKeyMap.containsKey(fsEa)) {
                        request.setAttribute("errorCode", "s320050002");
                        request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s117,lang,null));
                        request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s118,lang,null));
                        request.getRequestDispatcher("/index.jsp").forward(request, response);
                        return;
                    }
                    Map<String, String> enterpriseInfoMap = interconnectSecretKeyMap.get(fsEa);

                    //涉及到了跨云，直接重定向到代开发云就行，不会出现死循环，因为代开发不用这些跳转
                    GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(fsEa);
                    String domain = domainPrefix;
                    if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
                        domain = enterpriseInfo.getEnterpriseData().getDomain();
                    }
                    //修改、重定向
                    String redirectUrl2 = UrlUtils.buildUrl(domain + "/qyweixin/doRepFunction",
                            "param", param,
                            "appId", enterpriseInfoMap.get("appId"),
                            "corpId", enterpriseInfoMap.get("outEa"),
                            "fsEa", fsEa,
                            "go2Crm", String.valueOf(Boolean.TRUE)); // 将 boolean 转换为 String
                    log.info("ControllerQYWeixin.doFunction.redirectUrl2={}", redirectUrl2);
                    response.sendRedirect(redirectUrl2);
                    return;
                } else {
                    enterpriseBindEntities = enterpriseBindEntities.stream().
                            filter(e -> e.getBindStatus() == BindStatusEnum.normal && StringUtils.isNotEmpty(e.getOutEa())).collect(Collectors.toList());
                    //是否存在appId的绑定
                    boolean anyMatch = enterpriseBindEntities.stream()
                            .anyMatch(enterpriseBindEntity -> enterpriseBindEntity.getAppId().equals(appID));
                    if (!anyMatch) {
                        Optional<OuterOaEnterpriseBindEntity> finalEnterpriseBindEntityOptional = enterpriseBindEntities.stream()
                                .filter(enterpriseBindEntity -> enterpriseBindEntity.getAppId().startsWith("dk")).findFirst();
                        if (finalEnterpriseBindEntityOptional.isPresent()) {
                            OuterOaEnterpriseBindEntity finalEnterpriseBindEntity = finalEnterpriseBindEntityOptional.get();
                            //修改、重定向
                            String redirectUrl2 = UrlUtils.buildUrl(domainPrefix + "/qyweixin/doRepFunction",
                                    "param", param,
                                    "appId", finalEnterpriseBindEntity.getAppId(),
                                    "corpId", finalEnterpriseBindEntity.getOutEa(),
                                    "fsEa", fsEa,
                                    "go2Crm", String.valueOf(Boolean.TRUE)); // 将 boolean 转换为 String
                            log.info("ControllerQYWeixin.doFunction.redirectUrl2={}", redirectUrl2);
                            response.sendRedirect(redirectUrl2);
                            return;
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.info("ControllerQYWeixin.doFunction.e=}", e);
        } catch (ServletException e) {
            throw new RuntimeException(e);
        }

//        String redirectUri= URL.encode(domainPrefix + "/qyweixin/callback/oauth2?lang="+lang+"&param="+param+"&fsEa="+fsEa);
        String redirectUri= URL.encode(UrlUtils.buildUrl(domainPrefix + "/qyweixin/callback/oauth2", "lang", lang, "param", param, "fsEa", fsEa));

        String oauthUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope="+appLoginAuthScope+"&state=%s#wechat_redirect";
        oauthUrl = String.format(oauthUrl, appID, redirectUri, appID);
        log.info("trace doFunction, appID:{},oauthUrl:{}", appID, oauthUrl);
        response.sendRedirect(oauthUrl);
    }

    /**
     * 点击企微侧自建或代开发应用内的卡片消息，跳转到指定的URL
     * 消息中的URL格式如下：
     *
     * http://open.xxx.com/qyweixin/doFunction?
     * param="https://www.ceshi113.com/hcrm/wechat/function/todo?id=fde70df775104dfea9a831a6c0bac08b&apiname=MarketingEventObj"&
     * appID=wx4c7edab730f4fdc9
     *
     * 接收消息模板参数
     * @param param  如：https://www.ceshi112.com/hcrm/cloudhub/function/{}?id=xx&apiname=yy
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/doRepFunction")
    public void doRepFunction(@RequestParam String corpId,
                              @RequestParam String appId,
                              @RequestParam String param,
                              @RequestParam(required = false) String fsEa,
                              @RequestParam(required = false) boolean go2Crm,
                              @RequestHeader(value = "user-agent") String userAgent,
                              HttpServletResponse response,
                              HttpServletRequest request) throws IOException, ServletException {
        String lang = I18nUtils.getLang(userAgent);
        log.info("ControllerQYWeixin.doRepAppLogin,lang={}", lang);
        i18NStringManager.setDefaultRequestScope(request,lang);
        String redirectUri = URL.encode(domainPrefix + "/qyweixin/callback/rep/oauth2?param=" + param);
        if(go2Crm) {
            redirectUri= URL.encode(UrlUtils.buildUrl(domainPrefix + "/qyweixin/callback/oauth2", "param", param, "fsEa", fsEa));
        }

        corpId = qyweixinManager.corpId2OpenCorpId(corpId).getData();
        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(corpId)) {
            log.info("ControllerQYWeixin.doRepAppLogin,redirect to new base,corpId={}",corpId);
            return;
        }

        List<OuterOaAppInfoEntity> appInfoEntities = outerOaAppInfoManager.getEntities(OuterOaAppInfoParams.builder().channel(ChannelEnum.qywx).outEa(corpId).appId(appId).build());
        log.info("ControllerQYWeixin.doRepFunction,appInfoEntities={}", appInfoEntities);

        if(CollectionUtils.isEmpty(appInfoEntities)) {
            request.setAttribute("errorCode", "s320050002");
            request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s117,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s118,lang,null));
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }
        OuterOaAppInfoEntity appInfoEntity = appInfoEntities.get(0);
        QyweixinAppInfoParams qyweixinAppInfoParams = JSON.parseObject(appInfoEntity.getAppInfo(), QyweixinAppInfoParams.class);

        String agentId = String.valueOf(qyweixinAppInfoParams.getAuthAppInfo().getAgentId());

        String state = appId + "@" + corpId;

        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+corpId+"&redirect_uri="+redirectUri+"&response_type=code&scope=snsapi_privateinfo&state="+state+"&agentid=" +agentId+"#wechat_redirect";
        log.info("trace doRepFunction, get url:{} ", url);
        response.sendRedirect(url);
    }


    @RequestMapping(value = "doGetFunctionUrl", method = RequestMethod.POST)
    public String doGetFunctionUrl(@RequestBody Map<String, String> req){
        String appId = String.valueOf(req.get("appId"));
        String functionUrl = String.valueOf(req.get("functionUrl"));
        String severPrefix = String.valueOf(req.get("serverPrefix"));
        String param = Base64.encodeBase64String(functionUrl.getBytes());

        String messageUrl= severPrefix + "/qyweixin/doFunction?param="+ param +"&appID="+appId;
        log.info("trace doGetFunctionUrl, get Url:{}", messageUrl);
        return messageUrl;
    }

    /**doMessageFunction()的企业微信回调地址。
     * http://open.xxx.com/qyweixin/callback/oauth2
     *
     * oauth2回调获取用户信息，回调param
     * @param code
     * @param state
     * @param param param解码后的内容格式：https://www.ceshi112.com/hcrm/wechat/function/todo?apiname=MarketingEventObj&id=637edb165e2ffd000179b226&ea=85879
     * @param response
     */
    @RequestMapping(value = "/callback/oauth2")
    public void messageFunction(@RequestParam(required = false) String code,
                                @RequestParam(required = false) String state,
                                @RequestParam String param,
                                @RequestParam(required = false) String fsEa,
                                @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                                @RequestParam(required = false) String userInfoStr,
                                HttpServletResponse response,
                                HttpServletRequest request) throws IOException, ClassNotFoundException, ServletException {
        log.info("ControllerQYWeixin.messageFunction,code={},state={},param={},lang={}", code,state,param,lang);
        i18NStringManager.setDefaultRequestScope(request,lang);

        String redirectUrl = new String(Base64.decodeBase64(param.getBytes()));

        if (StringUtils.isEmpty(fsEa)) {
            //一对多场景，CRM待办、审批、提醒等需要从URL参数中取出ea
            fsEa = URL.valueOf(redirectUrl).getParameter("ea");
            log.info("ControllerQYWeixin.messageFunction,ea={}", fsEa);
        }

        String appId = state;
        String outEa = null;
        if(StringUtils.startsWithIgnoreCase(state,"dk")) {
            List<String> items = Splitter.on("@").splitToList(state);
            appId = items.get(0);
            outEa = items.get(1);
            state = appId;
        }

        Object userInfo;
        if (StringUtils.isNotEmpty(code) && StringUtils.isEmpty(userInfoStr)) {
            //1.用code换企业微信用户信息
            Map<String, Object> dataMap = Maps.newHashMap();
            dataMap.put("code", code);
            dataMap.put("appId", state);
            dataMap.put("outEa", outEa);
            dataMap.put("isIsvCode", Boolean.TRUE);
            MethodContext context = MethodContext.newInstance(dataMap);

            qyweixinLoginTemplate.getOutUserInfoByCode(context);

            Result<Object> result = (Result<Object>) context.getResult().getData();

//        Result<Object> result = qyweixinGatewayService.code2AppLoginUserInfo(code, state, outEa);
            userInfo = result.getData();
        } else {
            String pramUrl = new String(Base64.decodeBase64(userInfoStr.getBytes()));
            JSONObject jsonObject = JSON.parseObject(pramUrl);
            String type = jsonObject.getString("type"); // 获取类型信息
            Class<?> clazz = Class.forName(type); // 加载类
            userInfo = jsonObject.getObject("data", clazz);
//            String pramUrl = new String(Base64.decodeBase64(userInfoStr.getBytes()));
//            userInfo = JSONObject.parseObject(pramUrl, Object.class);
        }

        String outUserId = null;
        if (userInfo instanceof QyweixinUserDetailInfoRsp) {
            QyweixinUserDetailInfoRsp userDetailInfoRsp = (QyweixinUserDetailInfoRsp) userInfo;
            outEa = userDetailInfoRsp.getCorpid();
            outUserId = userDetailInfoRsp.getUserid();
        } else if(userInfo instanceof QyweixinUserSimpleInfoRsp) {
            QyweixinUserSimpleInfoRsp userSimpleInfoRsp = (QyweixinUserSimpleInfoRsp) userInfo;
            outEa = userSimpleInfoRsp.getCorpId();
            outUserId = userSimpleInfoRsp.getUserId();
        }
        log.info("AuthController.callbackAuth,outEa={},outUserId={}",outEa,outUserId);

        outEa = qyweixinManager.corpId2OpenCorpId(outEa).getData();

        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntities(OuterOaEnterpriseBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).build());
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            //todo：老代办兼容
            if (StringUtils.isNotEmpty(code)) {
                Map<String, Map<String, String>> interconnectSecretKeyMap = new Gson().fromJson(ConfigCenter.QYWX_NEED_OLD_MESSAGE, new TypeToken<Map<String, Map<String, String>>>() {
                });
                if (!interconnectSecretKeyMap.containsKey(outEa)) {
                    request.setAttribute("errorCode", "s320050002");
                    request.setAttribute("errorMsg", i18NStringManager.get(I18NStringEnum.s117,lang,null));
                    request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s118,lang,null));
                    request.getRequestDispatcher("/index.jsp").forward(request, response);
                    return;
                }
                Map<String, String> enterpriseInfoMap = interconnectSecretKeyMap.get(outEa);
                //涉及到了跨云，直接重定向到代开发云就行，不会出现死循环，因为判断了code
                GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(enterpriseInfoMap.get("fsEa"));
                String cloudDomain = domainPrefix;
                if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
                    cloudDomain = enterpriseInfo.getEnterpriseData().getDomain();
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("type", userInfo.getClass().getName()); // 添加类型信息
                jsonObject.put("data", userInfo); // 添加实际数据
                String userInfo2 = new String(Base64.encodeBase64(com.alibaba.fastjson2.JSON.toJSONBytes(jsonObject)));
//            state = appId + "@" + outEa;
                String newRedirectUrl = UrlUtils.buildUrl(cloudDomain + "/qyweixin/callback/oauth2",
                        "param", param,
                        "state", enterpriseInfoMap.get("appId") + "@" + enterpriseInfoMap.get("outEa"),
                        "userInfoStr", userInfo2,
                        "fsEa", enterpriseInfoMap.get("fsEa"),
                        "lang", lang);
                response.sendRedirect(newRedirectUrl);
                return;
            }
        }

        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(outEa)) {
            log.info("ControllerQYWeixin.callbackAuth,redirect to new base,outEa={}",outEa);
            return;
        }
        //查询企业绑定关系
        String domain;
        OuterOaEnterpriseBindEntity enterpriseBindEntity;
        if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
            throw new RuntimeException(i18NStringManager.get2(I18NStringEnum.s154.getI18nKey(),
                    lang,
                    null,
                    String.format(I18NStringEnum.s154.getI18nValue(),outEa),
                    Lists.newArrayList(
                            outEa
                    )));
        }
        enterpriseBindEntity = enterpriseBindEntities.get(0);

        lang = i18NStringManager.getDefaultLangByFsEa(enterpriseBindEntity.getFsEa());
        QywxConnectorVo qywxConnectorVo = JSON.parseObject(enterpriseBindEntity.getConnectInfo(), QywxConnectorVo.class);
        domain = qywxConnectorVo.getDomain();
        if(ObjectUtils.isNotEmpty(domain) && !domain.equals(domainPrefix)) {
            //转移环境
            String redirectUri = domain + "/qyweixin/loginCloudAuth?channel=qywx&outEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outEa), "utf-8")+"&appId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+state), "utf-8")+"&userId="+URLEncoder.encode(SecurityUtil.encryptStr(VER+outUserId), "utf-8")+"&fsEa="+URLEncoder.encode(SecurityUtil.encryptStr(VER+fsEa), "utf-8")+"&url="+URLEncoder.encode(redirectUrl, "utf-8");
            response.sendRedirect(redirectUri);
            return;
        }

        GenFsTicketModel ticketModel = new GenFsTicketModel();
        ticketModel.setAppId(appId);
        ticketModel.setOutEa(outEa);
        ticketModel.setFsEa(fsEa);
        ticketModel.setFsUserId(outUserId);

        MethodContext context2 = MethodContext.newInstance(ticketModel);
        qyweixinLoginTemplate.genFsTicket(context2);
        Result<String> ticket = context2.getResultData();

//        Result<String> ticket = qyweixinGatewayService.genFsTicket(state, outEa, outUserId, ea);
        redirectUrl = redirectUrl + (redirectUrl.contains("?") ? "&" : "?") + "ticket="+ticket.getData();
        log.info("trace messageFunction redirectUrl:{}", redirectUrl);

        //兼容专属云老企业
        GetEnterpriseDataResult enterpriseInfo = this.getEnterpriseInfo(enterpriseBindEntity.getFsEa());
        if(ObjectUtils.isNotEmpty(enterpriseInfo) && ObjectUtils.isNotEmpty(enterpriseInfo.getEnterpriseData()) && StringUtils.isNotEmpty(enterpriseInfo.getEnterpriseData().getDomain())) {
            domain = enterpriseInfo.getEnterpriseData().getDomain();
        }
        redirectUrl=redirectUrl.replace("https://www.fxiaoke.com",domain);
        log.info("trace messageFunction new redirectUrl:{}", redirectUrl);
        response.sendRedirect(redirectUrl);
    }

    /**
     * 自建或代开发应用的oauth2回调获取用户信息，回调param
     * @param code
     * @param state
     * @param param
     * @param response
     */
    @RequestMapping(value = "/callback/rep/oauth2")
    public void repMessageFunction(@RequestParam String code,
                                   @RequestParam String state,
                                   @RequestParam String param,
                                   @RequestParam(required = false,defaultValue = I18NStringManager.DEFAULT_LANG) String lang,
                                   HttpServletRequest request,
                                   HttpServletResponse response) throws Exception {
        log.info("ControllerQYWeixin.repMessageFunction,code={},state={},param={},lang={}", code,state,param,lang);

        String redirectUrl = new String(Base64.decodeBase64(param.getBytes()));
        log.info("ControllerQYWeixin.repMessageFunction,redirectUrl={}", redirectUrl);

        i18NStringManager.setDefaultRequestScope(request,lang);

        List<String> items = Splitter.on("@").splitToList(state);
        String appId = items.get(0);
        String corpId = items.get(1);

        corpId = qyweixinManager.corpId2OpenCorpId(corpId).getData();
        //不会拦截器做路由，在这里判断加重定向到新服务
        if(!oANewBaseManager.canRunInNewBaseByOutEa(corpId)) {
            return;
        }

        //1.用code换企业微信用户信息
        Map<String, Object> dataMap = Maps.newHashMap();
        dataMap.put("code", code);
        dataMap.put("appId", appId);
        dataMap.put("outEa", corpId);
		dataMap.put("isIsvCode", Boolean.FALSE);
        MethodContext context = MethodContext.newInstance(dataMap);

        qyweixinLoginTemplate.getOutUserInfoByCode(context);

        Result<QyweixinRepUserDetailInfoRsp> result = (Result<QyweixinRepUserDetailInfoRsp>) context.getResult().getData();
//        Result<QyweixinRepUserDetailInfoRsp> result = qyweixinGatewayService.getRepAppLoginUserInfo(appId, corpId, code);
        log.info("ControllerQYWeixin.repMessageFunction,result={}", result);
        if (!StringUtils.equalsIgnoreCase(result.getErrorCode(),"0")) {
            request.setAttribute("errorCode", result.getErrorCode());
            request.setAttribute("errorMsg", StringUtils.isNotEmpty(result.getErrorMsg()) ? result.getErrorMsg() : i18NStringManager.get(I18NStringEnum.s119,lang,null));
            request.setAttribute("propose", i18NStringManager.get(I18NStringEnum.s120,lang,null));

            log.info("ControllerQYWeixin.repMessageFunction,redirect to index.jsp");
            request.getRequestDispatcher("/index.jsp").forward(request, response);
            return;
        }

        EnterpriseWechatUserModel userModel = new EnterpriseWechatUserModel();
        userModel.setAppId(appId);
        userModel.setCorpId(corpId);
        userModel.setUserId(result.getData().getUserid());
        userModel.setMobile(result.getData().getMobile());
        qyweixinGatewayService.sendEnterpriseWechatUserInfo(userModel);

        redirectUrl = StringUtils.contains(redirectUrl, "?") ? redirectUrl : redirectUrl + "?";
        redirectUrl += "phone=" + result.getData().getMobile();
        log.info("ControllerQYWeixin.repMessageFunction,redirectUrl={}", redirectUrl);
        response.sendRedirect(redirectUrl);

    }

    /**
     * 获取个人手机号等敏感信息，包括手机号
     *  redirect_uri?code=CODE&state=STATE
     */
    @RequestMapping(value = "/getPrivateInfoCallback", method = RequestMethod.GET)
    public void getPrivateInfoCallback(@RequestParam String code,
                                       @RequestParam(required = false) String fsState,
                                       @RequestParam String fsRedirectUri,
                                       HttpServletResponse response) throws IOException {

        log.info("trace getPrivateInfoCallback,code:{}, fsState:{}, fsRedirectUri:{} ", code, fsState, fsRedirectUri);
        String corpId=fsEaToOutEa(UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount());
        Result<Pair<String,String>> userPhoneResult = qyweixinAccountSyncService.getUserPhone(ConfigCenter.crmAppId, corpId, code);

        if(userPhoneResult.isSuccess()) {
            fsRedirectUri+="?userId="+userPhoneResult.getData().first+"&userPhone="+userPhoneResult.getData().second;
        } else {
            fsRedirectUri+="?userId=null&userPhone=null";
        }
        log.info("trace fsRedirectUri:{}",fsRedirectUri);
        response.sendRedirect(fsRedirectUri);
    }



    /**
     * 获取个人手机号等敏感信息，包括手机号. 控制浏览器跳转下面的链接
     * http://open.xxx.com/qyweixin/getPrivateInfo
     * 然后企业微信回调到
     * http://open.xxx.com/qyweixin/getPrivateInfoCallback
     *
     * @param fsRedirectUri: 纷享的回调地址
     * @param fsState: 防止跨站请求攻击，非必填项。
     */
    @RequestMapping(value = "/getPrivateInfo", method = RequestMethod.GET)
    public void getPrivateInfo(@RequestParam String fsRedirectUri,
                               @RequestParam(required = false) String fsState,
                               HttpServletResponse response) throws IOException {
        log.info("trace getPrivateInfo curuser:{}", UserContextSingleton.INSTANCE.getUserContext());
        String corpId=fsEaToOutEa(UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount());
        String redirect_uri= URLEncoder.encode(domainPrefix+"/qyweixin/getPrivateInfoCallback?fsRedirectUri="+fsRedirectUri+"&fsState"+fsState);
        String response_type="code";
        String scope = "snsapi_privateinfo";
        String agentid=getAgentID(ConfigCenter.crmAppId, corpId);
        String state = calcFsState(UserContextSingleton.INSTANCE.getUserContext().toString());

        String weixinRedirectUri="https://open.weixin.qq.com/connect/oauth2/authorize?";
        weixinRedirectUri += "appid="+corpId;
        weixinRedirectUri += "&redirect_uri="+redirect_uri;
        weixinRedirectUri += "&response_type="+response_type;
        weixinRedirectUri += "&scope="+scope;
        weixinRedirectUri += "&agentid="+agentid;
        weixinRedirectUri += "&state="+state;
        weixinRedirectUri += "#wechat_redirect";
        log.info("trace getPrivateInfo redirect uri: {}", weixinRedirectUri);
        response.sendRedirect(weixinRedirectUri);
    }

    private String calcFsState(String loginSig) {
        if(StringUtils.isEmpty(loginSig)) {
            return "jdyqndijglsu08jd8";
        }
        return MD5Helper.getMD5HexString(loginSig);
    }



    private String getAgentID(String appId, String corpId) {
        Result<QywxCorpAuthorizeInfo> result = qyweixinGatewayService.getQywxCorpAuthorizeInfo(appId, corpId);
        if(result.isSuccess()) {
            return result.getData().getAgent_id();
        }
        return null;
    }

    private String fsEaToOutEa(String fsEa) {
        return qyweixinAccountBindService.fsEaToOutEa("qywx", fsEa).getData();
    }



    //TODO 微信小程序 第三方登录凭证校验 jscode2session
    @RequestMapping(value = "/miniprogram/jscode2session", method = RequestMethod.GET)
    public Result<String> miniprogramJscode2session(@RequestParam String code, @RequestParam String appId){
//        Result<String> userKey = qyweixinGatewayService.jscode2sessionService(code, appId);
//        return userKey;
        return new Result<>();
    }

    //TODO 解密数据
    @RequestMapping(value = "/miniprogram/encryptedData", method = RequestMethod.POST)
    public Result<String> encryptedData(@RequestBody Map<String, String> req){
//        String ticket = req.get("ticket");
//        String encryptedData = req.get("encryptedData");
//        String iv = req.get("iv");
//        return qyweixinGatewayService.miniprogramEncryptData(ticket, encryptedData, iv);
        return new Result<>();
    }

    /**
     * 调用企业微信公费电话
     * @param callerEi
     * @param callerUid
     * @param calleeEa
     * @return
     * @throws IOException
     */
    @RequestMapping(value = "/getDial", method = {RequestMethod.GET,RequestMethod.POST})
    @ResponseBody
    @Deprecated
    public Result<String> Dial(@RequestParam String callerEi,
                       @RequestParam String callerUid,
                       @RequestParam String calleeEa) throws IOException{
//        String fsEa = UserContextSingleton.INSTANCE.getUserContext().getEnterpriseAccount();
//        log.info("ControllerQYWeixin.Dial,fsEa={}", fsEa);
        String source="qywx";
        log.info("ControllerQYWeixin.Dial,callerEi={},callerUid={},calleeEa={}", callerEi,callerUid,calleeEa);
        Integer nCallerEi = null;
        try {
            nCallerEi=Integer.parseInt(callerEi);
        } catch (Exception e) {
            return Result.newInstance(ErrorRefer.PARAM_ERROR);
        }
        String callerEa = eieaConverter.enterpriseIdToAccount(nCallerEi);
        String fsCaller="E."+callerEa+"."+callerUid;
        log.info("ControllerQYWeixin.Dial,fsCaller={}", fsCaller);
        // 在db把fscaller换成userid,用下面接口实现
        com.facishare.open.qywx.accountbind.result.Result<Map<String, String>> userMap =
                qyweixinAccountBindService.fsAccountToOutAccountBatch(source, ConfigCenter.crmAppId, Lists.newArrayList(fsCaller));
        log.info("ControllerQYWeixin.Dial,userMap={}", userMap);
        String callerQywxUid=userMap.getData().get(fsCaller);
        log.info("ControllerQYWeixin.Dial,callerQywxUid={}", callerQywxUid);
        //企业内部换外部账号
        com.facishare.open.qywx.accountbind.result.Result<String> corpId = qyweixinAccountBindService.fsEaToOutEa(source, calleeEa);
        log.info("ControllerQYWeixin.Dial,corpId={}", corpId);
        Result<String> result = qyweixinGatewayService.doDial(callerQywxUid, corpId.getData());
        log.info("ControllerQYWeixin.Dial,result={}", result);
        return result;
    }

    /**
     * 通过auth_code换取当前用户信息
     *
     * @param code 企业微信侧auth_code
     * @return
     */
    @RequestMapping(value = "/current-user", method = RequestMethod.GET)
    @ResponseBody
    public Result<QyweixinEmployeeInfo> getCurrentUser(@RequestParam("code") String code) {
        Map<String, String> argument = Collections.singletonMap("auth_code", code);
        return qyweixinAccountSyncService.getQyweixinCurrentUser(argument);
    }

    @CrossOrigin
    @PostMapping("/fsIds2OpenIds")
    public Result<List<QyweixinExchangeId.Result>> getOutAccountsByFsAccounts(@RequestBody QyweixinExchangeId.Argument argument) {
        String fsEa = argument.getEa();
        log.info("ControllerQYWeixin.getOutAccountsByFsAccounts,fsEa={}", fsEa);
        List<QyweixinExchangeId.Result> result = new ArrayList<>();
        List<Integer> ids = argument.getFsIds();
        int type = argument.getType();
        if (StringUtils.isBlank(fsEa) || CollectionUtils.isEmpty(ids)) return new Result<>(result);
        log.info("ControllerQYWeixin.getOutAccountsByFsAccounts,argument={}", argument);
        ids = ids.stream().filter(id -> !"null".equalsIgnoreCase(String.valueOf(id))).collect(Collectors.toList());
        result = type == 1 ? processDepartment(fsEa, ids) : processEmployeeAccount(fsEa, ids);
        log.info("ControllerQYWeixin.getOutAccountsByFsAccounts,result={}", result);
        return new Result<>(result);
    }

    private List<QyweixinExchangeId.Result> processDepartment(String fsEa, List<Integer> departmentIds) {
        List<QyweixinExchangeId.Result> result = new ArrayList<>();
        Map<Integer, String> map = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, null, null, departmentIds.stream().map(String::valueOf).collect(Collectors.toList()), null)
                .parallelStream()
                .collect(Collectors.toMap(
                        entity -> Integer.valueOf(entity.getFsDepId()),
                        OuterOaDepartmentBindEntity::getOutDepId,
                        (k1, k2) -> k2));
        departmentIds.forEach(departmentId -> {
            String outDepartmentId = map.get(departmentId);
            if (StringUtils.isNotBlank(outDepartmentId)){
                result.add(new QyweixinExchangeId.Result(departmentId, outDepartmentId));
            }
        });
        log.info("ControllerQYWeixin.processDepartment success. fsEa:{}, map:{}, departmentIds:{}, result:{}", fsEa, map, departmentIds, result);
        return result;
    }

    private List<QyweixinExchangeId.Result> processEmployeeAccount(String fsEa, List<Integer> userIds) {
        List<QyweixinExchangeId.Result> result = new ArrayList<>();
        Map<String, String> map = outerOaEmployeeBindManager.batchGetEmployeeBindEntity(ChannelEnum.qywx, null, fsEa, null, userIds.stream().map(String::valueOf).collect(Collectors.toList()), null)
                .parallelStream()
                .collect(Collectors.toMap(OuterOaEmployeeBindEntity::getFsEmpId,
                        OuterOaEmployeeBindEntity::getOutEmpId,
                        (key1, key2) -> key2));
        // 保持有序
        userIds.forEach(userId -> {
            String outAccount = map.get(String.valueOf(userId));
            if (StringUtils.isNotBlank(outAccount)){
                result.add(new QyweixinExchangeId.Result(userId, outAccount));
            }
        });
        log.info("ControllerQYWeixin.processEmployeeAccount success. fsEa:{}, map:{}, userIds:{}, result:{}", fsEa, map, userIds, result);
        return result;
    }

    /**
     *
     * @param response
     * @return
     * <AUTHOR>
     * @Date 14:04 2020/8/31
     * @Desc 获取企业微信用户授权
     */
    @RequestMapping(value= "/getUserAuthorize",method =RequestMethod.GET)
    public void getUserAuthorize(@RequestParam String suffix,
                                 @RequestParam(required = false) String appid,
                                 @RequestParam(required = false) String queryParam,
                                 HttpServletResponse response) throws IOException {
        log.info("ControllerQYWeixin.getUserAuthorize,suffix={},appid={},queryParam={}",suffix,appid,queryParam);
        if(StringUtils.isEmpty(appid)){
            appid = ConfigCenter.crmAppId;
        }
        String redirectUrl =domainPrefix + "/qyweixin/callbackUserAuthorize?appid="+appid+"&suffix="+suffix;
        if(StringUtils.isNotEmpty(queryParam)) {
            queryParam = Base64Utils.encodeToString(URL.decode(queryParam).getBytes("utf-8"));
            redirectUrl+="&queryParam="+queryParam;
        }
        redirectUrl =URL.encode(redirectUrl);
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+appid+"&redirect_uri="+redirectUrl+"&response_type=code&scope="+appLoginAuthScope+"&state="+STATE+"#wechat_redirect";
        log.info("企业微信oauth2身份认证, 请求url:{}, 回调接口url:{} ", url, redirectUrl);
        log.info("ControllerQYWeixin.getUserAuthorize,url={},redirectUrl={}",url,redirectUrl);
        response.sendRedirect(url);
    }

    /**
     *
     * @param code
     * @param state
     * @param response
     * @return
     * <AUTHOR>
     * @Date 14:10 2020/8/31
     * @Desc 获取code生成ticket，并重定向至suffix接口
     */
    @RequestMapping(value = "/callbackUserAuthorize")
    public void callbackUserAuthorize(@RequestParam String code,
                                      @RequestParam String state,
                                      @RequestParam(required = false) String appid,
                                      @RequestParam String suffix,
                                      @RequestParam(required = false) String queryParam,
                                      HttpServletResponse response) throws IOException {
        log.info("ControllerQYWeixin.callbackUserAuthorize,code={},state={},suffix={},appid={}",code,state,suffix,appid);
        if(StringUtils.isEmpty(appid)){
            appid = ConfigCenter.crmAppId;
        }
        if( !STATE.equals(state)){ // 防止CSRF攻击
            log.error("state参数:{} 错误",state);
            throw new IllegalArgumentException("state参数错误!");
        }

        Result<CorpTicketResult> ticket = qyweixinGatewayService.appAuth(code, appid,null);
        String concat = suffix.contains("?") ? "&" : "?";
        String loginUrl =  domainPrefix + suffix + concat +"ticket=" + ticket.getData().getTicket();
        if(StringUtils.isNotEmpty(queryParam)) {
            byte[] data = Base64Utils.decodeFromString(queryParam);
            String query = new String(data,"utf-8");
            log.info("ControllerQYWeixin.getUserAuthorize,query={}",query);
            loginUrl+="&"+query;
        }
        log.info("ControllerQYWeixin.callbackUserAuthorize,loginUrl={}",loginUrl);
        response.sendRedirect(loginUrl);
    }

    private GetEnterpriseDataResult getEnterpriseInfo(String fsEa) {
        GetEnterpriseDataArg arg = new GetEnterpriseDataArg();
        arg.setEnterpriseAccount(fsEa);
        GetEnterpriseDataResult result = enterpriseEditionService.getEnterpriseData(arg);
        Map<String, String> cloudDomainEa = new Gson().fromJson(ConfigCenter.CLOUD_DOMAIN_EA, new TypeToken<Map<String, String>>() {
        });
        if(ObjectUtils.isNotEmpty(result) && cloudDomainEa.containsKey(fsEa)) {
            log.info("ControllerQYWeixin.getEnterpriseInfo,old={},new={}", result.getEnterpriseData().getDomain(), cloudDomainEa.get(fsEa));
            result.getEnterpriseData().setDomain(cloudDomainEa.get(fsEa));
        }
        log.info("ControllerQYWeixin.getEnterpriseInfo,result={}",result);
        return result;
    }
}



