package com.facishare.open.qywx.web.model.qyweixin;

import com.facishare.open.qywx.accountsync.core.enums.QyweixinErrorCodeEnum;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinUserDetailInfoRsp;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2018/7/24.
 */
@Data
public class QyweixinTagEmployeeListRsp implements Serializable {
    private Integer errcode;
    private String errmsg;
    private String tagname;
    List<QyweixinUserDetailInfoRsp> userlist;

    List<String> partylist;

    public boolean isSuccess(){
        return QyweixinErrorCodeEnum.SUCCESS.getErrCode().equals(errcode);
    }
}
