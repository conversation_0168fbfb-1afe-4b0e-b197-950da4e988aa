package com.facishare.open.qywx.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutDepartmentInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorSyncEventDataMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutUserInfoDoc;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorSyncEventDataDoc;
import com.facishare.open.oa.base.dbproxy.pg.entity.*;
import com.facishare.open.oa.base.dbproxy.pg.manager.*;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaDepartmentBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeBindParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEmployeeDataParams;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.order.contacts.proxy.api.arg.FsDeptArg;
import com.facishare.open.order.contacts.proxy.api.arg.FsEmpArg;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource;
import com.facishare.open.order.contacts.proxy.api.enums.FsEmployeeRoleCodeEnum;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutDepModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutEmpModel;
import com.facishare.open.order.contacts.proxy.api.model.contacts.OutUpdateTagModel;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsDepartmentServiceProxy;
import com.facishare.open.order.contacts.proxy.api.service.FsEmployeeServiceProxy;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import com.facishare.open.outer.oa.connector.common.api.admin.QywxConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.*;
import com.facishare.open.outer.oa.connector.common.api.info.EnterpriseManualAccountSyncInfo;
import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import com.facishare.open.outer.oa.connector.common.api.object.QywxEmployeeObject;
import com.facishare.open.outer.oa.connector.common.api.result.SettingsResult;
import com.facishare.open.outer.oa.connector.common.api.result.SystemFieldMappingResult;
import com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService;
import com.facishare.open.qywx.accountbind.utils.TraceUtil;
import com.facishare.open.qywx.accountinner.service.ContactBindInnerService;
import com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService;
import com.facishare.open.qywx.accountsync.model.QyweixinEnterpriseOrder;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinDepartmentRsp;
import com.facishare.open.qywx.accountsync.utils.xml.*;
import com.facishare.open.qywx.web.config.ConfigCenter;
import com.facishare.open.qywx.accountsync.core.enums.SourceTypeEnum;
import com.facishare.open.qywx.web.manager.*;
import com.facishare.open.qywx.accountsync.model.ContactScopeModel;
import com.facishare.open.qywx.web.model.GetFsUserIdsByRestResult;
import com.facishare.open.qywx.web.model.TagDetailModel;
import com.facishare.open.qywx.accountsync.model.qyweixin.AppInfo;
import com.facishare.open.qywx.accountsync.model.qyweixin.QyweixinUserDetailInfoRsp;
import com.facishare.open.qywx.accountsync.result.ErrorRefer;
import com.facishare.open.qywx.accountsync.result.Result;
import com.facishare.open.qywx.accountsync.service.ContactsService;
import com.facishare.open.qywx.web.model.qyweixin.*;
import com.facishare.open.qywx.web.service.OrderService;
import com.facishare.open.qywx.web.utils.ContactsUtils;
import com.facishare.open.qywx.web.utils.RedisLockUtils;
import com.fxiaoke.api.IdGenerator;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.fxiaoke.crmrestapi.result.ActionAddResult;
import com.fxiaoke.crmrestapi.result.IncrementUpdateResult;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("contactsService")
public class ContactsServiceImpl implements ContactsService {
    @Autowired
    private QYWeixinManager qyWeixinManager;
    @Autowired
    private CorpManager corpManager;
    @Resource
    private QyweixinAccountBindService qyweixinAccountBindService;
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;
    @Resource
    private FsDepartmentServiceProxy fsDepartmentServiceProxy;
    @Resource
    private FsEmployeeServiceProxy fsEmployeeServiceProxy;
    @Resource
    private EIEAConverter eieaConverter;
    @Resource
    private QyweixinAccountBindInnerService qyweixinAccountBindInnerService;
    @Autowired
    private RedisDataSource redisDataSource;
    @Resource
    private RedissonClient redissonClient;
//    @Resource
//    private SyncEventDataManger syncEventDataManger;
    @Resource
    private FsManager fsManager;
//    @Resource
//    private OutUserInfoManger outUserInfoManger;
//    @Resource
//    private OutDepartmentInfoManger outDepartmentInfoManger;

    @ReloadableProperty("repAppId")
    private String repAppId;

    @Resource
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;
    @Resource
    private OuterOaConfigInfoManager outerOaConfigInfoManager;
    @Resource
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Resource
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
//    @Resource
//    private OaConnectorOutUserInfoMongoDao oaConnectorOutUserInfoMongoDao;
//    @Resource
//    private OaConnectorOutDepartmentInfoMongoDao oaConnectorOutDepartmentInfoMongoDao;
    @Resource
    private OaConnectorSyncEventDataMongoDao oaConnectorSyncEventDataMongoDao;
    @Resource
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;
    @Resource
    private OuterOaDeptDataManager outerOaDeptDataManager;
    @Resource
    private ContactBindInnerService contactBindInnerService;
    @Resource
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;
    @Resource
    private OrderService orderService;
    @Resource
    private EventBindSendMqManager eventBindSendMqManager;

    @Override
    public Result<Void> initContactsAsync(OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        String traceId = TraceUtil.get();
        Executors.newScheduledThreadPool(1).schedule(()->{
            TraceUtil.initTrace(traceId);
            //保存可见范围
            QyweixinAppVisibleModel qyweixinAppVisibleModel = saveChangeAuthEvent(enterpriseBindEntity.getOutEa(), enterpriseBindEntity.getAppId());
            //初始化数据
            initContacts(enterpriseBindEntity);
        },3 * 1000L, TimeUnit.MILLISECONDS);
        return new Result<>();
    }

    private Result<Void> initContacts(OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        log.info("ContactsServiceImpl.initContactsAsync,enterpriseBindEntity={}", enterpriseBindEntity);
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();

        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        //1.获取应用信息，包括可见范围信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(outEa, appId);
        log.info("ContactsServiceImpl.initContactsAsync,appInfo={}",appInfoResult);
        if(!appInfoResult.isSuccess() || appInfoResult.getData()==null) return new Result<>(appInfoResult.getCode(),appInfoResult.getMsg(),null);

        //2.获取应用可见范围内的所有部门，递归获取
        com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> outDepModelListResult = qyWeixinManager.getDepInfoList(appId, outEa, "");
        log.info("ContactsServiceImpl.initContactsAsync,outDepModelList={}", outDepModelListResult);

        //3.初始化纷享部门并更新部门映射表
        if(outDepModelListResult.isSuccess() && CollectionUtils.isNotEmpty(outDepModelListResult.getData())) {
            initDepAndMapping(ei, enterpriseBindEntity,outDepModelListResult.getData());
        }

        //4.获取应用可见范围内的不在部门内的员工列表，包括单独添加在可见范围内的员工和标签下不在任何部门内的员工
        List<OutEmpModel> empList = getEmpListInAppVisibleRange(appInfoResult.getData(),outEa,appId);

        //5.初始化纷享员工并更新员工映射表
        initEmpAndMapping(ei,enterpriseBindEntity,outDepModelListResult.getData(),empList);

        return new Result<>();
    }

    @Override
    public Result<Void> onAvailableRangeChanged(String appId, String outEa) {
        log.info("ContactsServiceImpl.onAvailableRangeChanged,appId={},outEa={}",appId,outEa);
        //分布式锁
        boolean gotFirstLock = Boolean.FALSE;
        boolean gotSecondLock = Boolean.FALSE;
        RLock rLock = redissonClient.getLock(String.format("qywx_change_contacts_%s", outEa));
        try {
            if (!rLock.isLocked()) {
                gotFirstLock = rLock.tryLock(1, 60 * 5, TimeUnit.SECONDS);
                log.info("ContactsServiceImpl.onAvailableRangeChanged,first gotFirstLock={}",gotFirstLock);
                if(gotFirstLock) {
                    try {
//                        onAvailableRangeChangedLimit(appId, outEa);
                    } finally {
                        rLock.unlock();
                        gotFirstLock = Boolean.FALSE;
                    }
                } else {
                    log.info("ContactsServiceImpl.onAvailableRangeChanged,first lock acquisition failed,outEa={}",outEa);
                }
            } else {
                gotSecondLock = RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_repeat_change_contacts_%s", outEa), outEa, 60 * 2);
                log.info("ContactsServiceImpl.onAvailableRangeChanged,first gotSecondLock={}",gotSecondLock);
                if(gotSecondLock) {
                    long startTime = System.currentTimeMillis();
                    gotFirstLock = rLock.tryLock(60 * 5, 60 * 5, TimeUnit.SECONDS);
                    RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_repeat_change_contacts_%s", outEa), outEa);
                    gotSecondLock = Boolean.FALSE;
                    log.info("ContactsServiceImpl.onAvailableRangeChanged,second gotFirstLock={},cost={}ms",gotFirstLock, System.currentTimeMillis() - startTime);
                    if(gotFirstLock) {
                        try {
//                            onAvailableRangeChangedLimit(appId, outEa);
                        } finally {
                            rLock.unlock();
                            gotFirstLock = Boolean.FALSE;
                        }
                    } else {
                        log.info("ContactsServiceImpl.onAvailableRangeChanged,second lock acquisition failed,outEa={}",outEa);
                    }
                } else {
                    log.info("ContactsServiceImpl.onAvailableRangeChanged,The visible range of operations is too frequent, discard this event,outEa={}",outEa);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            log.info("ContactsServiceImpl.onAvailableRangeChanged,gotFirstLock={},gotSecondLock={}",gotFirstLock, gotSecondLock);
            if(gotFirstLock) {
                rLock.unlock();
            }
            if(gotSecondLock) {
                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_repeat_change_contacts_%s", outEa), outEa);
            }
        }
        return new Result<>();
    }

    public Result<Void> onAvailableRangeChangedLimit(OuterOaEnterpriseBindEntity enterpriseBindEntity) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        String outEa = enterpriseBindEntity.getOutEa();
        String dcId = enterpriseBindEntity.getId();

        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        //1.获取应用信息，包括可见范围信息
        com.facishare.open.qywx.accountinner.result.Result<AppInfo> appInfoResult = qyWeixinManager.getAppInfo(outEa, appId);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,appInfo={}",appInfoResult);
        if(!appInfoResult.isSuccess() || appInfoResult.getData()==null) return new Result<>(appInfoResult.getCode(),appInfoResult.getMsg(),null);

        //2.获取应用可见范围内的所有部门，包括标签下的部门，递归获取
        com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> visibleOutDepModelListResult = qyWeixinManager.getDepInfoList(appId, outEa, "");
        log.info("ContactsServiceImpl.onAvailableRangeChanged,visibleOutDepModelList={}", visibleOutDepModelListResult);

        //3.查询DB里状态正常的企微部门ID列表
        List<String> dbActiveOutDepIdList = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa, appId, null, null).stream()
                .filter(e -> e.getBindStatus().equals(BindStatusEnum.normal))
                .map(OuterOaDepartmentBindEntity::getOutDepId)
                .collect(Collectors.toList());
        log.info("ContactsServiceImpl.onAvailableRangeChanged,dbActiveOutDepIdList={}", dbActiveOutDepIdList);

        //4.计算不在可见范围内的企微部门ID列表
        List<String> notInAvailableRangeOutDepIdList = null;

        if(visibleOutDepModelListResult.isSuccess() && CollectionUtils.isNotEmpty(visibleOutDepModelListResult.getData())) {
            //可见范围内的企微部门ID列表
            List<String> visibleOutDepIdList = visibleOutDepModelListResult.getData().stream()
                    .map(OutDepModel::getId)
                    .collect(Collectors.toList());
            log.info("ContactsServiceImpl.onAvailableRangeChanged,visibleOutDepIdList={}", visibleOutDepIdList);

            //不在可见范围内的企微部门ID列表
            notInAvailableRangeOutDepIdList = ListUtils.removeAll(dbActiveOutDepIdList, visibleOutDepIdList);

            log.info("ContactsServiceImpl.onAvailableRangeChanged,notInAvailableRangeOutDepIdList={}", notInAvailableRangeOutDepIdList);
        } else {
            //可见范围内只有员工，没有部门
            notInAvailableRangeOutDepIdList = dbActiveOutDepIdList;
        }

        //5.获取应用可见范围内的不在部门内的员工列表
        List<OutEmpModel> empList = getEmpListInAppVisibleRange(appInfoResult.getData(),outEa,appId);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,empList={}", empList);

        Map<String,List<String>> excludeDepEmpMap = new HashMap<>();
        excludeDepEmpMap.put(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",new ArrayList<>());
        for(OutEmpModel outEmpModel : empList) {
            //查看绑定关系
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, outEmpModel.getId());
            if(employeeBindEntity!=null) {
                excludeDepEmpMap.get(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"").add(employeeBindEntity.getFsEmpId());
            }
        }

        //7.新增或更新部门，同时新增或更新部门员工
        addDepListInner(enterpriseBindEntity,visibleOutDepModelListResult.getData());

        //6.停用部门下的员工并停用部门
        stopDepListInner(enterpriseBindEntity,notInAvailableRangeOutDepIdList,excludeDepEmpMap);

        //去掉不需要更新的人员
        List<OutEmpModel> updateEmpList = removeEmployeeList(empList, appInfoResult.getData(), appId, outEa);
        log.info("ContactsServiceImpl.onAvailableRangeChanged,updateEmpList={}", updateEmpList);

        //8.创建和更新部门员工，并更新员工绑定表
        createAndUpdateDepEmpList(ei,
                enterpriseBindEntity,
                GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",
                updateEmpList);
        return new Result<>();
    }

    /**
     * 企微标签变更事件
     * 对应企微的 update_tag 事件
     *
     * @return
     */
    private Result<Void> onUpdateTag(OuterOaEnterpriseBindEntity enterpriseBindEntity, OutUpdateTagModel outUpdateTagModel) {
        log.info("ContactsServiceImpl.onUpdateTag,outUpdateTagModel={}",outUpdateTagModel);
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        String outEa = enterpriseBindEntity.getOutEa();

        onAvailableRangeChanged(appId, outEa);
//        if(!(ConfigCenter.TAG_SYNC_CONTACT_OUT_EA.contains(outEa) || ConfigCenter.TAG_SYNC_CONTACT_OUT_EA.contains(Boolean.TRUE.toString()))) {
//
//            return new Result<>();
//        } else {
//            //异步处理，防止堵塞mq
//            Thread thread = new Thread(() -> updateTagEvent(appId, outEa, outUpdateTagModel));
//            thread.start();
//        }
        return new Result<>();
    }

//    private void updateTagEvent(String appId, String outEa, OutUpdateTagModel outUpdateTagModel) {
//        //分布式锁
//        if (RedisLockUtils.tryGetDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa, 60)) {
//            try {
//                log.info("ContactsServiceImpl.updateTagEvent,outUpdateTagModel={}",outUpdateTagModel);
//                //企微推送事件过来时，为避免企微可见范围没有处理完成，先sleep5s
//                Thread.sleep(5000);
//                //更新标签的时候，区分事件维度，单独处理
//                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getAddEmpList())) {
//                    addUserList(appId,outEa,outUpdateTagModel.getAddEmpList());
//                }
//
//                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getRemoveEmpList())) {
//                    //停用员工操作，先查询员工详情，如果能查询到，证明其部门还在可见范围，忽略
//                    List<String> removeEmpList = new LinkedList<>();
//                    for(String empId : outUpdateTagModel.getRemoveEmpList()) {
//                        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> empInfoResult = qyWeixinManager.getUserInfo(appId, outEa, empId);
//                        if(empInfoResult.isSuccess() && ObjectUtils.isNotEmpty(empInfoResult)) {
//                            continue;
//                        }
//                        removeEmpList.add(empId);
//                    }
//                    if(CollectionUtils.isNotEmpty(removeEmpList)) {
//                        stopUserList(appId,outEa,removeEmpList);
//                    }
//                }
//
//                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getAddDepList())) {
//                    //新增部门的时候，要考虑其子部门
//                    addTagDepList(appId,outEa,outUpdateTagModel.getAddDepList());
//                }
//
//                if(CollectionUtils.isNotEmpty(outUpdateTagModel.getRemoveDepList())) {
//                    //标签移除部门时，有几种情况
//                    //该部门、子部门和部门下全部人员都被移除了
//                    //该部门被移除，子部门保留了全部或者一部分，人员移除了全部或者一部分
//                    //该部门被移除，子部门也全部移除，但是保留了部分人员
//                    //该部门被移除，但是父部门仍保留
//                    String fsEa = getFsEa(outEa);
//                    if (StringUtils.isNotEmpty(fsEa)) {
//                        int ei = eieaConverter.enterpriseAccountToId(fsEa);
//                        for (String depId : outUpdateTagModel.getRemoveDepList()) {
//                            stopTagDep(appId, outEa, depId, ei, fsEa);
//                        }
//                    }
//                }
//            } catch (InterruptedException e) {
//                e.printStackTrace();
//            } finally {
//                RedisLockUtils.releaseDistributedLock(redisDataSource.getRedisClient(), String.format("qywx_change_contacts_%s", outEa), outEa);
//            }
//        } else {
//            log.info("ContactsServiceImpl.onUpdateTag,The visible range of operations is too frequent, discard this event,outEa={}",outEa);
//        }
//    }

//    private void addTagDepList(String appId, String outEa, List<String> outDepIdList) {
//        Set<String> outAllDepIdSet = new HashSet<>();
//        for(String depId : outDepIdList) {
//            log.info("ContactsServiceImpl.addTagDepList,depId={},outAllDepIdSet={}",depId, outAllDepIdSet);
//            //去重
//            if(outAllDepIdSet.contains(depId)) {
//                continue;
//            }
//            //查询子部门详情
//            com.facishare.open.qywx.accountinner.result.Result<List<OutDepModel>> outDepModelListResult = qyWeixinManager.getDepInfoList(appId, outEa, depId);
//            if(outDepModelListResult.isSuccess() && CollectionUtils.isNotEmpty(outDepModelListResult.getData())) {
//                Set<String> outDepIdSet = outDepModelListResult.getData().stream()
//                        .map(OutDepModel::getId)
//                        .collect(Collectors.toSet());
//                outAllDepIdSet.addAll(outDepIdSet);
//                addDepListInner(appId, outEa, outDepModelListResult.getData());
//            }
//        }
//    }

//    private Boolean stopTagDep(String appId, String outEa, String depId, Integer ei, String fsEa) {
//        //是否可以查询到企微部门详情
//        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(appId, outEa, depId);
//        if(ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
//            return Boolean.FALSE;
//        }
//        //是否具体绑定关系
//        QyweixinAccountDepartmentMapping depMapping = getDepMappingByOutDepId(outEa, depId, -1, appId);
//        log.info("ContactsServiceImpl.stopTagDep,depMapping={}", depMapping);
//        if(ObjectUtils.isEmpty(depMapping)) {
//            return Boolean.FALSE;
//        }
//        Integer fsDepId = depMapping.getFsDepartmentId();
//        //人员是否全部停用
//        Boolean isStopDepByEmp = stopDepEmployees(appId, outEa, ei, fsEa, fsDepId);
//        log.info("ContactsServiceImpl.stopTagDep,isStopDepByEmp={}", isStopDepByEmp);
//        //子部门是否可以全部停用
//        com.facishare.open.order.contacts.proxy.api.result.Result<List<DepartmentDto>> childrenDepartmentResult =
//                fsDepartmentServiceProxy.getChildrenDepartment(ei, fsDepId);
//        List<DepartmentDto> departmentDtos = childrenDepartmentResult.getData();
//        //是否停用部门
//        boolean flag = isStopDepByEmp;
//
//        if(CollectionUtils.isNotEmpty(departmentDtos)) {
//            //子部门是否停用
//            List<Integer> childrenDepIds = departmentDtos.stream()
//                    .map(DepartmentDto::getDepartmentId)
//                    .collect(Collectors.toList());
//
//            Result<List<QyweixinAccountDepartmentMapping>> childrenDepMappingsResult = qyweixinAccountBindInnerService.queryDepartmentBindV21(SourceTypeEnum.QYWX.getSourceType(),
//                    fsEa,
//                    appId,
//                    -1,
//                    childrenDepIds,
//                    outEa);
//            if(childrenDepMappingsResult.getData().size() < childrenDepIds.size()) {
//                //查到的绑定关系少于子部门数量，不停用部门
//                flag = Boolean.FALSE;
//            }
//            List<QyweixinAccountDepartmentMapping> childrenDepMappings = childrenDepMappingsResult.getData();
//            if(CollectionUtils.isNotEmpty(childrenDepMappings)) {
//                for(QyweixinAccountDepartmentMapping mapping : childrenDepMappings) {
//                    Boolean isStopChildrenDep = stopTagDep(appId, outEa, mapping.getOutDepartmentId(), ei, fsEa);
//                    log.info("ContactsServiceImpl.stopTagDep,isStopChildrenDep={}", isStopChildrenDep);
//                    if(flag && !isStopChildrenDep) {
//                        flag = Boolean.FALSE;
//                    }
//                }
//            }
//        }
//
//        if(flag) {
//            //停用部门
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result
//                    = fsDepartmentServiceProxy.toggle(ei + "", String.valueOf(fsDepId), Boolean.FALSE);
//            if(result.isSuccess()) {
//                //更新数据库状态
//                batchUpdateFsDepBindStatus(fsEa,
//                        Lists.newArrayList(String.valueOf(fsDepId)),
//                        1,
//                        appId,
//                        outEa);
//            }
//        }
//
//        return flag;
//    }


//    private Boolean stopDepEmployees(String appId, String outEa, Integer ei, String fsEa, Integer departmentId) {
//        boolean flag = Boolean.TRUE;
//        //查询部门下的人员
//        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> empListResult = fsEmployeeServiceProxy.listByDepId(ei, String.valueOf(departmentId));
//        if(!empListResult.isSuccess()) {
//            //查询失败就不停用人员，返回不停用部门的信息
//            return Boolean.FALSE;
//        }
//        if(CollectionUtils.isNotEmpty(empListResult.getData())) {
//            //查询人员的绑定关系
//            com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> accountResult =
//                    qyweixinAccountBindService.fsAccountToOutAccountBatchV21(SourceTypeEnum.QYWX.getSourceType(),
//                            appId,
//                            -1,
//                            empListResult.getData().stream()
//                            .map(v -> String.format("E.%S.%S", fsEa, v.getId())).collect(Collectors.toList()),outEa);
//            if(accountResult.getData().size() < empListResult.getData().size()) {
//                //有的人员没有绑定关系，不停用部门
//                flag = Boolean.FALSE;
//            }
//            if(CollectionUtils.isNotEmpty(accountResult.getData())) {
//                for(QyweixinAccountEmployeeMapping mapping : accountResult.getData()) {
//                    //查询人员详情
//                    com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> empInfoResult = qyWeixinManager.getUserInfo(appId, outEa, mapping.getOutAccount());
//                    if(ObjectUtils.isNotEmpty(empInfoResult.getData())) {
//                        if(flag) {
//                            //查询到企微人员信息，不停用部门
//                            flag = Boolean.FALSE;
//                        }
//                        continue;
//                    }
//                    //停用人员
//                    List<String> accountList = Splitter.on(".").splitToList(mapping.getFsAccount());
//                    int fsUserId = Integer.parseInt(accountList.get(2));
//                    if(fsUserId==1000) {
//                        if(flag) {
//                            //管理员（1000）不能停用，不停用部门
//                            flag = Boolean.FALSE;
//                        }
//                        continue;
//                    }
//                    com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
//                            ei + "",
//                            fsUserId + "",
//                            false,
//                            null,
//                            null);
//                    log.info("ContactsServiceImpl.stopDepEmployees,stopResult={}", stopResult);
//                    batchUpdateFsEmpBindStatus(Lists.newArrayList(mapping.getFsAccount()),BindStatusEnum.stop,appId, mapping.getOutEa(), fsEa);
//                }
//            }
//        }
//        return flag;
//    }

    private void repChangeContacts(String outEa, String appId, String changeType, ContactsXml contactsXml) {
        //历史企业兼容逻辑
        Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
        if (CollectionUtils.isEmpty(outerOaEnterpriseBindEntities)) {
            log.info("QyweixinAccountSyncServiceImpl.repChangeContacts,outEa={},appId={}", outEa, appId);
            return;
        }
        if (changeType.equals("update_tag")) {
            log.info("QyweixinAccountSyncServiceImpl.repChangeContacts,outEa={},appId={},changeType={}", outEa, appId, changeType);
            repSyncChangeAuthEvent(appId, outEa);
            return;
        }

        if (!(changeType.equals("create_user") || changeType.equals("update_user"))) {
            log.info("QyweixinAccountSyncServiceImpl.repChangeContacts,outEa={},appId={},changeType={}", outEa, appId, changeType);
            return;
        }


        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> outEmpModelResult = qyWeixinManager.getUserInfo(appId, outEa, contactsXml.getUserID());
        if(!outEmpModelResult.isSuccess()) {
            log.info("QyweixinAccountSyncServiceImpl.repChangeContacts,getAllEmployeeList error,outEa={},appId={}", outEa, appId);
            return;
        }
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : outerOaEnterpriseBindEntities) {
            if (autoBindEmpEnterpriseMap.containsKey(enterpriseBindEntity.getFsEa())) {
                log.info("QyweixinAccountSyncServiceImpl.repChangeContacts,autoBindEmpEnterpriseMap,fsEa={}",enterpriseBindEntity.getFsEa());
                corpManager.autoBindEmpAccount(enterpriseBindEntity, Lists.newArrayList(outEmpModelResult.getData()));
            }
        }
    }

    @Override
    public Result<Void> onChangeContacts(ContactsXml contactsXml, String appId) {
        log.info("ContactsServiceImpl.onChangeContacts,contactsXml={}", contactsXml);
        String outEa = contactsXml.getCorpId();
        String changeType = contactsXml.getChangeType();
        log.info("ContactsServiceImpl.onChangeContacts,changeType={}", changeType);

        //查看企业信息
        List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);

        if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
            if (appId.equals(ConfigCenter.repAppId)) {
                //以前有些靠代开发触发的事件逻辑
                repChangeContacts(outEa, appId, changeType, contactsXml);
            }
            return new Result<>();
        }
        //先保存入库
        QyweixinAppVisibleModel qyweixinAppVisibleModel = saveChangeContactsEvent(outEa, appId, changeType, contactsXml);
        if (ObjectUtils.isNotEmpty(qyweixinAppVisibleModel)) {
            log.info("ContactsServiceImpl.onChangeContacts,qyweixinAppVisibleModel data");
        }

        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
            //规则
            log.info("QyweixinAccountSyncServiceImpl.onChangeContacts,enterpriseBindEntity={}", enterpriseBindEntity);
            String fsEa = enterpriseBindEntity.getFsEa();
            String dcId = enterpriseBindEntity.getId();

            //看配置是否要同步
            OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, dcId);
            SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(configInfoEntity.getConfigInfo(), SettingAccountRulesModel.class);
            if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
                //同步通讯录逻辑
                dealSyncContactsEvent(enterpriseBindEntity, contactsXml);
            } else {
                //反绑定逻辑
                if (settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.auto) {
                    //自动绑定
                    autoBindEmployee(enterpriseBindEntity, qyweixinAppVisibleModel);
                } else {
                    //手动绑定：不做任何处理
                    log.info("QyweixinAccountSyncServiceImpl.syncChangeAuthEvent,manual bind,do nothing,outEa={},appId={}", outEa, appId);
                }

                //这里需要做停用人员的逻辑
                deleteAccountBindUser(enterpriseBindEntity, RemoveEmployeeEventType.RESIGN_EMPLOYEE);
            }
        }
        return new Result<>();
    }


    private QyweixinAppVisibleModel saveChangeContactsEvent(String outEa, String appId, String changeType, ContactsXml contactsXml) {
        //通过outEa和appId找到dcId
        List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntities)) {
            return new QyweixinAppVisibleModel();
        }
        String dcId = outerOaEnterpriseBindEntities.get(0).getId();

        //单个的直接查询
        OuterOaAppInfoEntity repAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, outEa, ConfigCenter.repAppId);
        boolean isRepAppId = Boolean.FALSE;
        if (!appId.equals(ConfigCenter.repAppId) && ObjectUtils.isNotEmpty(repAppInfoEntity)) {
            isRepAppId = Boolean.TRUE;
        }

        QyweixinAppVisibleModel qyweixinAppVisibleModel = new QyweixinAppVisibleModel();
        if ("create_user".contains(changeType)) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> outEmpModelResult = qyWeixinManager.getUserInfo(appId, outEa, contactsXml.getUserID());

            if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) {
                log.info("ContactsServiceImpl.onChangeContacts,outEmpModelResult={}", outEmpModelResult);
                return qyweixinAppVisibleModel;
            }
            QyweixinUserDetailInfoRsp rsp = outEmpModelResult.getData();
            if (isRepAppId) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> outRepEmpModelResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId, outEa, contactsXml.getUserID());
                if (outRepEmpModelResult.isSuccess() && ObjectUtils.isNotEmpty(outRepEmpModelResult.getData()) && !outRepEmpModelResult.getData().getUserid().equals(outRepEmpModelResult.getData().getName())) {
                    rsp.setName(outRepEmpModelResult.getData().getName());
                }
            }

            List<QyweixinUserDetailInfoRsp> qyweixinUserDetailInfoRsps = new LinkedList<>();
            qyweixinUserDetailInfoRsps.add(rsp);
            //插入员工信息表
            List<OuterOaEmployeeDataEntity> employeeDataEntities = new LinkedList<>();
            QywxEmployeeObject qywxEmployeeObject = new QywxEmployeeObject();
            //深拷贝
            BeanUtils.copyProperties(rsp, qywxEmployeeObject);
            List<QywxEmployeeObject> qywxEmployeeObjects = new LinkedList<>();
            qywxEmployeeObjects.add(qywxEmployeeObject);
            OuterOaEmployeeDataEntity employeeDataEntity = new OuterOaEmployeeDataEntity();
            employeeDataEntity.setId(IdGenerator.get());
            employeeDataEntity.setChannel(ChannelEnum.qywx);
            employeeDataEntity.setOutEa(outEa);
            employeeDataEntity.setAppId(appId);
            employeeDataEntity.setOutUserId(rsp.getUserid());
            employeeDataEntity.setOutUserInfo((JSONObject) JSONObject.toJSON(qywxEmployeeObject));
            employeeDataEntity.setOutDeptId(rsp.getMain_department());
            employeeDataEntity.setCreateTime(System.currentTimeMillis());
            employeeDataEntity.setUpdateTime(System.currentTimeMillis());
            employeeDataEntities.add(employeeDataEntity);
            Integer userCreateCount = outerOaEmployeeDataManager.batchUpsert(qywxEmployeeObjects, ChannelEnum.qywx, dcId);
            log.info("ContactsServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,userCreateCount={}", userCreateCount);
            qyweixinAppVisibleModel.setQywxEmployeeObjects(qywxEmployeeObjects);
            qyweixinAppVisibleModel.setQyweixinUserDetailInfoRsps(qyweixinUserDetailInfoRsps);
        } else if ("update_user".equals(changeType)) {  //修改了userid的把fsAccount传过去就不用再调用sync-provider的方法了
            String outUserId = StringUtils.isEmpty(contactsXml.getNewUserID()) ? contactsXml.getUserID() : contactsXml.getNewUserID();
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> outEmpModelResult = qyWeixinManager.getUserInfo(appId, outEa, outUserId);
            if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) {
                log.info("ContactsServiceImpl.onChangeContacts,outEmpModelResult1={}", outEmpModelResult);
                return qyweixinAppVisibleModel;
            }

            QyweixinUserDetailInfoRsp rsp = outEmpModelResult.getData();
            if (isRepAppId) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> outRepEmpModelResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId, outEa, contactsXml.getUserID());
                if (outRepEmpModelResult.isSuccess() && ObjectUtils.isNotEmpty(outRepEmpModelResult.getData()) && !outRepEmpModelResult.getData().getUserid().equals(outRepEmpModelResult.getData().getName())) {
                    rsp.setName(outRepEmpModelResult.getData().getName());
                }
            }
            List<QyweixinUserDetailInfoRsp> qyweixinUserDetailInfoRsps = new LinkedList<>();
            qyweixinUserDetailInfoRsps.add(rsp);
            //插入员工信息表
            List<OuterOaEmployeeDataEntity> employeeDataEntities = new LinkedList<>();
            QywxEmployeeObject qywxEmployeeObject = new QywxEmployeeObject();
            //深拷贝
            BeanUtils.copyProperties(rsp, qywxEmployeeObject);
            List<QywxEmployeeObject> qywxEmployeeObjects = new LinkedList<>();
            qywxEmployeeObjects.add(qywxEmployeeObject);
            OuterOaEmployeeDataEntity employeeDataEntity = new OuterOaEmployeeDataEntity();
            employeeDataEntity.setId(IdGenerator.get());
            employeeDataEntity.setChannel(ChannelEnum.qywx);
            employeeDataEntity.setOutEa(outEa);
            employeeDataEntity.setAppId(appId);
            employeeDataEntity.setOutUserId(rsp.getUserid());
            employeeDataEntity.setOutUserInfo((JSONObject) JSONObject.toJSON(qywxEmployeeObject));
            employeeDataEntity.setOutDeptId(rsp.getMain_department());
            employeeDataEntity.setCreateTime(System.currentTimeMillis());
            employeeDataEntity.setUpdateTime(System.currentTimeMillis());
            employeeDataEntities.add(employeeDataEntity);
            Integer userUpdateCount = outerOaEmployeeDataManager.batchUpsert(qywxEmployeeObjects, ChannelEnum.qywx, dcId);
            log.info("ContactsServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,userUpdateCount={}", userUpdateCount);
            qyweixinAppVisibleModel.setQywxEmployeeObjects(qywxEmployeeObjects);
            qyweixinAppVisibleModel.setQyweixinUserDetailInfoRsps(qyweixinUserDetailInfoRsps);
        } else if ("delete_user".equals(changeType)) {
            Integer userDeleteCount = outerOaEmployeeDataManager.deleteByUserId(ChannelEnum.qywx, outEa, appId, contactsXml.getUserID());
            log.info("ContactsServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,userDeleteCount={}", userDeleteCount);
        } else if ("create_party".contains(changeType)) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> outDeptModelResult = qyWeixinManager.getDepartmentInfo(appId, outEa, contactsXml.getId());
            if(!outDeptModelResult.isSuccess() || ObjectUtils.isEmpty(outDeptModelResult.getData())) {
                log.info("ContactsServiceImpl.onChangeContacts,outDeptModelResult={}", outDeptModelResult);
                return qyweixinAppVisibleModel;
            }
            QyweixinDepartmentRsp rsp = outDeptModelResult.getData().getDepartment();
            if (isRepAppId) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> outRepDeptModelResult = qyWeixinManager.getDepartmentInfo(ConfigCenter.repAppId, outEa, contactsXml.getId());
                if (outRepDeptModelResult.isSuccess() && ObjectUtils.isNotEmpty(outRepDeptModelResult.getData()) && !outRepDeptModelResult.getData().getDepartment().getId().equals(outRepDeptModelResult.getData().getDepartment().getName())) {
                    rsp.setName(outRepDeptModelResult.getData().getDepartment().getName());
                }
            }

//            QywxDeptObject qywxDeptObject = new QywxDeptObject();
            //深拷贝
//            BeanUtils.copyProperties(rsp, qywxDeptObject);
            OuterOaDeptDataEntity deptDataEntity = new OuterOaDeptDataEntity();
            deptDataEntity.setId(IdGenerator.get());
            deptDataEntity.setChannel(ChannelEnum.qywx);
            deptDataEntity.setOutEa(outEa);
            deptDataEntity.setAppId(appId);
            deptDataEntity.setOutDeptId(rsp.getId());
            deptDataEntity.setDeptName(rsp.getName());
            deptDataEntity.setDeptOrder(Long.valueOf(rsp.getOrder()));
            deptDataEntity.setParentDeptId(rsp.getParentid());
            //TODO
//            deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(qywxDeptObject));
            deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(rsp));
            deptDataEntity.setCreateTime(System.currentTimeMillis());
            deptDataEntity.setUpdateTime(System.currentTimeMillis());
            Integer count = outerOaDeptDataManager.batchUpdateOutDeptId(Lists.newArrayList(deptDataEntity));
            log.info("ContactsServiceImpl.saveOrUpdateUserAndDepartmentInfo,department,count={}", count);
        } else if ("update_party".contains(changeType)) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> outDeptModelResult = qyWeixinManager.getDepartmentInfo(appId, outEa, contactsXml.getId());
            if(!outDeptModelResult.isSuccess() || ObjectUtils.isEmpty(outDeptModelResult.getData())) {
                log.info("ContactsServiceImpl.onChangeContacts,outDeptModelResult={}", outDeptModelResult);
                return qyweixinAppVisibleModel;
            }
            QyweixinDepartmentRsp rsp = outDeptModelResult.getData().getDepartment();
            if (isRepAppId) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> outRepDeptModelResult = qyWeixinManager.getDepartmentInfo(ConfigCenter.repAppId, outEa, contactsXml.getId());
                if (outRepDeptModelResult.isSuccess() && ObjectUtils.isNotEmpty(outRepDeptModelResult.getData()) && !outRepDeptModelResult.getData().getDepartment().getId().equals(outRepDeptModelResult.getData().getDepartment().getName())) {
                    rsp.setName(outRepDeptModelResult.getData().getDepartment().getName());
                }
            }
//            QywxDeptObject qywxDeptObject = new QywxDeptObject();
            //深拷贝
//            BeanUtils.copyProperties(rsp, qywxDeptObject);
            OuterOaDeptDataEntity deptDataEntity = new OuterOaDeptDataEntity();
            deptDataEntity.setId(IdGenerator.get());
            deptDataEntity.setChannel(ChannelEnum.qywx);
            deptDataEntity.setOutEa(outEa);
            deptDataEntity.setAppId(appId);
            deptDataEntity.setOutDeptId(rsp.getId());
            deptDataEntity.setDeptName(rsp.getName());
            deptDataEntity.setDeptOrder(Long.valueOf(rsp.getOrder()));
            deptDataEntity.setParentDeptId(rsp.getParentid());
            //todo
//            deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(qywxDeptObject));
            deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(rsp));
            deptDataEntity.setCreateTime(System.currentTimeMillis());
            deptDataEntity.setUpdateTime(System.currentTimeMillis());
            Integer count = outerOaDeptDataManager.batchUpdateOutDeptId(Lists.newArrayList(deptDataEntity));
            log.info("ContactsServiceImpl.saveOrUpdateUserAndDepartmentInfo,department,count={}", count);
        } else if ("delete_party".equals(changeType)) {
            Integer deptDeleteCount = outerOaDeptDataManager.deleteByDeptId(ChannelEnum.qywx, outEa, appId, contactsXml.getId());
            log.info("ContactsServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,deptDeleteCount={}", deptDeleteCount);
        } else if ("update_tag".equals(changeType)) {
            //保存可见范围
            qyweixinAppVisibleModel = saveChangeAuthEvent(outEa, appId);
        }
        return qyweixinAppVisibleModel;
    }

    private void dealSyncContactsEvent(OuterOaEnterpriseBindEntity enterpriseBindEntity, ContactsXml contactsXml) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        String changeType = contactsXml.getChangeType();

        if ("create_user".contains(changeType)) {
            addUser(enterpriseBindEntity, contactsXml.getUserID());
        } else if ("update_user".equals(changeType)) {  //修改了userid的把fsAccount传过去就不用再调用sync-provider的方法了
            updateUser(enterpriseBindEntity, contactsXml.getUserID(), contactsXml.getNewUserID());
        } else if ("delete_user".equals(changeType)) {
            stopUser(enterpriseBindEntity, contactsXml.getUserID());
        } else if ("create_party".contains(changeType)) {
            addDepList(enterpriseBindEntity, Lists.newArrayList(contactsXml.getId()));
        } else if ("update_party".contains(changeType)) {
            updateDepList(enterpriseBindEntity, Lists.newArrayList(contactsXml.getId()));
        } else if ("delete_party".equals(changeType)) {
            stopDepList(enterpriseBindEntity, Lists.newArrayList(contactsXml.getId()));
        } else if ("update_tag".equals(changeType)) {
            //直接更新可见范围
            onAvailableRangeChangedLimit(enterpriseBindEntity);
        }
    }

    /**
     * 获取应用可见范围内所有不在部门内的员工列表，包括标签内不在部门内的员工列表
     *
     * @param appInfo
     * @param outEa
     * @param appId
     * @return
     */
    private List<OutEmpModel> getEmpListInAppVisibleRange(AppInfo appInfo,String outEa,String appId) {
        List<OutEmpModel> userList = new ArrayList<>();
        if(appInfo.getAllow_userinfos()!=null) {
            //获取应用可见范围内的员工信息
            if(CollectionUtils.isNotEmpty(appInfo.getAllow_userinfos().getUser())) {
                List<String> userIdList = appInfo.getAllow_userinfos().getUser().stream()
                        .map(AppInfo.AllowUserInfos.User::getUserid).collect(Collectors.toList());
                for(String userId : userIdList) {
                    com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId, outEa, userId);
                    log.info("ContactsServiceImpl.getUserListInAppVisibleRange,getUserModel,outEmpModelResult={}", outEmpModelResult);
                    if(!outEmpModelResult.isSuccess() || ObjectUtils.isEmpty(outEmpModelResult.getData())) continue;
                    userList.add(outEmpModelResult.getData());
                }
            }
        }

        if(appInfo.getAllow_tags()!=null) {
            //获取应用可见范围内的标签内的员工信息
            if(CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
                for(String tagId : appInfo.getAllow_tags().getTagid()) {
                    com.facishare.open.qywx.accountinner.result.Result<TagDetailModel> tagDetailModelResult = qyWeixinManager.getTagDetail(appId, outEa, tagId);
                    log.info("ContactsServiceImpl.getUserListInAppVisibleRange,getTagDetail,tagDetailModel={}", tagDetailModelResult);
                    if(!tagDetailModelResult.isSuccess() || tagDetailModelResult.getData()==null || CollectionUtils.isEmpty(tagDetailModelResult.getData().getEmpList())) continue;

                    userList.addAll(tagDetailModelResult.getData().getEmpList());
                }
            }
        }

        log.info("ContactsServiceImpl.getUserListInAppVisibleRange,userList={}", JSONObject.toJSONString(userList));
        return userList;
    }

    private void initDepAndMapping(int ei,OuterOaEnterpriseBindEntity enterpriseBindEntity,List<OutDepModel> outDepModelList) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();

        for(OutDepModel outDepModel : outDepModelList) {

            //如果父部门ID为0，说明是企微的根部门
            if(StringUtils.equalsIgnoreCase(outDepModel.getParentId(),qywxRootDepId)) {
                updateRootDepAndMapping(ei,enterpriseBindEntity,outDepModel.getId(),outDepModel.getName());
                continue;
            }

            //创建子部门并入库
            createAndSaveDepMapping(ei,
                    enterpriseBindEntity,
                    outDepModel.getId(),
                    outDepModel.getName(),
                    outDepModel.getParentId());
        }
    }

    private void initEmpAndMapping(int ei,OuterOaEnterpriseBindEntity enterpriseBindEntity,
                                   List<OutDepModel> outDepModelList,
                                   List<OutEmpModel> empList) {
        log.info("ContactsServiceImpl.initEmpAndMapping,empList={}", JSONObject.toJSONString(empList));
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();

        List<OutEmpModel> totalEmpList = new ArrayList<>();
        totalEmpList.addAll(empList);
        //获取可见部门内的所有员工
        for(OutDepModel outDepModel : outDepModelList) {
            com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> depEmpListResult = qyWeixinManager.getDepEmpList(appId, outEa, outDepModel.getId());
            log.info("ContactsServiceImpl.initEmpAndMapping,depEmpList={}", depEmpListResult);
            if(!depEmpListResult.isSuccess() || CollectionUtils.isEmpty(depEmpListResult.getData())) continue;
            totalEmpList.addAll(depEmpListResult.getData());
        }
        log.info("ContactsServiceImpl.initEmpAndMapping,totalEmpList={}", JSONObject.toJSONString(totalEmpList));
        //去重
        totalEmpList = totalEmpList.stream().distinct().collect(Collectors.toList());

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).fsEa(fsEa).fsEmpId("1000").build());
        if(CollectionUtils.isNotEmpty(employeeBindEntities)) {
            //去掉可见范围内的管理员，避免管理员重复创建
            totalEmpList = totalEmpList.stream()
                    .filter((outEmpModel)->!StringUtils.equalsIgnoreCase(outEmpModel.getId(),employeeBindEntities.get(0).getOutEmpId()))
                    .collect(Collectors.toList());
            log.info("ContactsServiceImpl.initEmpAndMapping,remove admin user,totalEmpList={}", JSONObject.toJSONString(totalEmpList));
        }

        for(OutEmpModel outEmpModel : totalEmpList) {
            String fsMainDepId = getFsMainDepId(outEa,fsEa,outEmpModel.getDepIdList(),appId);
            createAndSaveEmpMapping(ei,enterpriseBindEntity, outEmpModel.getId());
        }
    }

    private void updateRootDepAndMapping(int ei,OuterOaEnterpriseBindEntity enterpriseBindEntity,String outDepId,String outDepName) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();

        //把纷享的全公司和企微的根部门映射，更新纷享的全公司名称为企微的根部门名称
        updateDep(ei,GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"",outDepId,outDepName, "0", null);

        OuterOaDepartmentBindEntity depBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepId);

        log.info("ContactsServiceImpl.updateRootDepAndMapping,depBind={}", depBind);
        if(ObjectUtils.isEmpty(depBind)) {
            saveDepMapping(fsEa,GlobalValue.ALL_COMPANY_DEPARTMENT_ID,outEa,outDepId,appId,BindStatusEnum.normal);
            depBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepId);
        } else {
            //更新数据库状态
            batchUpdateFsDepBindStatus(fsEa,
                    Lists.newArrayList(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+""),
                    BindStatusEnum.normal,
                    appId,
                    outEa);
            depBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepId);
        }
        log.info("ContactsServiceImpl.updateRootDepAndMapping,depBind2={}", depBind);
        com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> rootDepEmpListResult = qyWeixinManager.getDepEmpList(appId, outEa, outDepId);
        log.info("ContactsServiceImpl.updateRootDepAndMapping,rootDepEmpList={}", rootDepEmpListResult);
        if(rootDepEmpListResult.isSuccess() && CollectionUtils.isNotEmpty(rootDepEmpListResult.getData()) && depBind!=null) {
            //如果部门绑定关系正常，创建和更新部门员工，并更新员工绑定表
            createAndUpdateDepEmpList(ei,
                    enterpriseBindEntity,
                    depBind.getFsDepId(),
                    rootDepEmpListResult.getData());
        }
    }

    private void saveDepMapping(String fsEa,Integer fsDepId,String outEa,String outDepId,String appId,BindStatusEnum status) {
        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, fsEa, outEa, appId);
        String dcId = enterpriseBindEntity.getId();
        OuterOaDepartmentBindEntity departmentBind = new OuterOaDepartmentBindEntity();
        departmentBind.setChannel(ChannelEnum.qywx);
        departmentBind.setFsEa(fsEa);
        departmentBind.setOutEa(outEa);
        departmentBind.setAppId(appId);
        departmentBind.setDcId(dcId);
        departmentBind.setFsDepId(fsDepId.toString());
        departmentBind.setOutDepId(outDepId);
        departmentBind.setBindStatus(status);
        departmentBind.setCreateTime(System.currentTimeMillis());
        departmentBind.setUpdateTime(System.currentTimeMillis());

        log.info("ContactsServiceImpl.saveDepMapping,departmentBind={}", departmentBind);
        Integer count = outerOaDepartmentBindManager.insert(departmentBind);
        log.info("ContactsServiceImpl.saveDepMapping,count={}", count);
    }

//    private void saveEmpMapping(String fsEa, Integer fsUserId, String outEa, String outUserId, String appId, BindStatusEnum status) {
//        OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.qywx, fsEa, outEa, appId);
//        String dcId = enterpriseBindEntity.getId();
//        OuterOaEmployeeBindEntity arg = new OuterOaEmployeeBindEntity();
//        arg.setChannel(ChannelEnum.qywx);
//        arg.setFsEa(fsEa);
//        arg.setOutEa(outEa);
//        arg.setDcId(dcId);
//        arg.setOutEmpId(outUserId);
//        arg.setFsEmpId(fsUserId+"");
//        arg.setAppId(appId);
//        arg.setBindStatus(status);
//        arg.setCreateTime(System.currentTimeMillis());
//        arg.setUpdateTime(System.currentTimeMillis());
//
//        log.info("ContactsServiceImpl.saveEmpMapping,arg={}", arg);
//        com.facishare.open.qywx.accountbind.result.Result<Boolean> result = qyweixinAccountBindService.bindAccountEmployeeMapping(Lists.newArrayList(arg));
//        log.info("ContactsServiceImpl.saveEmpMapping,result={}", result);
//    }

    private Integer createAndSaveDepMapping(Integer ei,OuterOaEnterpriseBindEntity enterpriseBindEntity,String outDepId, String outDepName, String outParentDepId) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        //查询父部门是否已绑定
        OuterOaDepartmentBindEntity departmentBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outParentDepId);
        log.info("ContactsServiceImpl.createAndSaveDepMapping,outParentDepId={},departmentBind={}",outParentDepId, departmentBind);

        String fsParentDepId = null;
        if(ObjectUtils.isEmpty(departmentBind)) {
            fsParentDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        } else {
            fsParentDepId = departmentBind.getFsDepId();
        }

        FsDeptArg arg = FsDeptArg.builder()
                .ei(ei + "")
                .name(outDepName)
                .code(outDepId)
                .status("0")
                .parentId(Lists.newArrayList(fsParentDepId))
                .build();
        log.info("ContactsServiceImpl.createAndSaveDepMapping,arg={}", JSONObject.toJSONString(arg));
        com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsDepartmentServiceProxy.create(arg);
        log.info("ContactsServiceImpl.createAndSaveDepMapping,result={}", result);
        if(result.isSuccess()) {
            Integer fsDepId = Integer.valueOf(result.getData().getId());
            saveDepMapping(fsEa,fsDepId,outEa,outDepId,appId,BindStatusEnum.normal);
            return fsDepId;
        } else {
            log.info("ContactsServiceImpl.createAndSaveDepMapping,create fs dep failed,result={}", result);
        }
        return null;
    }

    private String getFsMainDepId(String outEa,
                                  String fsEa,
                                  List<String> outDepIdList,
                                  String appId) {
        String outMainDepId = null;
        if(CollectionUtils.isNotEmpty(outDepIdList)) {
            outMainDepId = outDepIdList.get(0);
        }

        String fsMainDepId = null;
        if(StringUtils.isEmpty(outMainDepId)) {
            fsMainDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        } else {
            //查询主部门是否已绑定
            List<OuterOaDepartmentBindEntity> mainDepBindList = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa, null, null, Lists.newArrayList(outMainDepId));
            log.info("ContactsServiceImpl.getFsMainDepId,mainDepBindList={}", mainDepBindList);
            if(CollectionUtils.isNotEmpty(mainDepBindList)) {
                fsMainDepId = mainDepBindList.get(0).getFsDepId();
            } else {
                //首次初始化通讯录，先创建部门，后创建员工，应该不会出现员工主部门映射查询不到的场景，为了保险起见，还是做一下兼容处理
                fsMainDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
            }
        }
        return fsMainDepId;
    }

    /**
     * 创建纷享员工并保存员工绑定关系
     * @param ei
     * @param outUserId
     * @return
     */
    private Integer createAndSaveEmpMapping(Integer ei,
                                            OuterOaEnterpriseBindEntity enterpriseBindEntity,
                                            String outUserId) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        String outEa = enterpriseBindEntity.getOutEa();
        String dcId = enterpriseBindEntity.getId();

        //看下是否需要创建
        List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = outerOaEmployeeBindManager.getEntities(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).fsEa(fsEa).outEa(outEa).outEmpId(outUserId).build());
        if (CollectionUtils.isNotEmpty(outerOaEmployeeBindEntities)) {

            //更新
//            OuterOaEmployeeBindEntity employeeBindEntity = new OuterOaEmployeeBindEntity();
//            employeeBindEntity.setChannel(ChannelEnum.qywx);
//            employeeBindEntity.setFsEa(fsEa);
//            employeeBindEntity.setOutEa(outEa);
//            employeeBindEntity.setDcId(dcId);
//            employeeBindEntity.setOutEmpId(outUserId);
//            employeeBindEntity.setFsEmpId(outerOaEmployeeBindEntities.get(0).getFsEmpId());
//            employeeBindEntity.setAppId(appId);
//            employeeBindEntity.setBindStatus(BindStatusEnum.normal);
//            employeeBindEntity.setCreateTime(System.currentTimeMillis());
//            employeeBindEntity.setUpdateTime(System.currentTimeMillis());
//            updateEmpAndMapping(ei, employeeBindEntity, outUserName, fsMainDepId);
            com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> empUpdateResult =
                    objectDataManager.updateEmpData(enterpriseBindEntity, outUserId);
            if(empUpdateResult.isSuccess()) {
                log.info("ContactsServiceImpl.createAndSaveEmpMapping,updateEmpData success,empUpdateResult={}", empUpdateResult);
            } else {
                log.info("ContactsServiceImpl.createAndSaveEmpMapping,updateEmpData failed,empUpdateResult={}", empUpdateResult);
            }
        } else {
//            FsEmpArg arg = FsEmpArg.builder()
//                    .ei(ei + "")
//                    .name(outUserName)
//                    .fullName(outUserName)
//                    .mainDepartment(Lists.newArrayList(fsMainDepId))
//                    .status("0")
//                    .isActive(true)
//                    .build();
//            log.info("ContactsServiceImpl.createAndSaveEmpMapping,arg={}", arg);
//            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
//                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
//                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
//            objectDataManager.createEmployee(enterpriseBindEntity,outUserId)
//            log.info("ContactsServiceImpl.createAndSaveEmpMapping,result={}", result);
//            if(result.isSuccess()) {
//                Integer fsUserId = Integer.valueOf(result.getData().getId());
//                saveEmpMapping(fsEa,fsUserId,outEa,outUserId,appId,BindStatusEnum.normal);
//                return fsUserId;
//            }
            com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> empCreateResult =
                    objectDataManager.createEmployee(enterpriseBindEntity, outUserId);
            if (empCreateResult.isSuccess()) {
                OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(enterpriseBindEntity.getChannel(), enterpriseBindEntity.getOutEa(),
                        enterpriseBindEntity.getFsEa(), enterpriseBindEntity.getAppId(), outUserId);
                if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                    eventBindSendMqManager.employeeBindChangeEvent(enterpriseBindEntity.getId(), Lists.newArrayList(employeeBindEntity.getId()), EmplyeeBindChangeTypeEnum.employee_bind);
                }
                log.info("ContactsServiceImpl.createAndSaveEmpMapping,createEmpData success,empCreateResult={}", empCreateResult);
            } else {
                log.info("ContactsServiceImpl.createAndSaveEmpMapping,createEmpData failed,empCreateResult={}", empCreateResult);
            }
        }
        return null;
    }

    @Override
    public Result<ContactScopeModel> getContactScopeData(String appId, String outEa) {

        return null;
    }

    @Override
    public Result<Void> addUserList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outUserIdList) {
        log.info("ContactsServiceImpl.addUserList,enterpriseBindEntity={},outUserIdList={}", enterpriseBindEntity,outUserIdList);
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        if(StringUtils.isEmpty(fsEa)) {
            return Result.newInstance(ErrorRefer.OUT_EA_NOT_BIND);
        }
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(String outUserId : outUserIdList) {
            com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userInfoResult = qyWeixinManager.getUserInfo(appId, outEa, outUserId);
            log.info("ContactsServiceImpl.addUserList,userInfoResult={}", userInfoResult);
            if(!userInfoResult.isSuccess() || ObjectUtils.isEmpty(userInfoResult.getData())) continue;

            addOrUpdateUser(ei,enterpriseBindEntity, outUserId);
        }
        return new Result<>();
    }

    /**
     * 添加或更新用户以及用户映射表
     * @param ei
     */
    private void addOrUpdateUser(int ei,OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        //        String fsMainDepId = getFsMainDepId(outEa,fsEa,outEmpModel.getDepIdList(),appId);
        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, outUserId);
        log.info("ContactsServiceImpl.addOrUpdateUser,query user mapping,employeeBindEntity={}", employeeBindEntity);
        //如果员工绑定关系不存在，创建员工并更新员工映射表
        if(employeeBindEntity==null) {
            Integer fsUserId = createAndSaveEmpMapping(ei,enterpriseBindEntity, outUserId);
            log.info("ContactsServiceImpl.addOrUpdateUser,fsUserId={}", fsUserId);
        } else {
            updateEmpAndMapping(ei,enterpriseBindEntity, outUserId);
        }
    }

    @Override
    public Result<Void> stopUserList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outUserIdList) {
        log.info("ContactsServiceImpl.stopUserList,enterpriseBindEntity={},outUserIdList={}", enterpriseBindEntity,outUserIdList);
        for(String outUserId : outUserIdList) {
            stopUser(enterpriseBindEntity,outUserId);
        }
        return new Result<>();
    }

    @Override
    public Result<Void> addDepList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList) {
        List<OutDepModel> outDepModelList = getOutDepModelList(enterpriseBindEntity, outDepIdList);
        return addDepListInner(enterpriseBindEntity,outDepModelList);
    }

    private List<OutDepModel> getOutDepModelList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList) {
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        List<OutDepModel> outDepModelList = new ArrayList<>();
        for(String outDepId : outDepIdList) {
            com.facishare.open.qywx.accountinner.result.Result<OutDepModel> outDepModelResult = qyWeixinManager.getDepInfo(appId,outEa,outDepId);
            log.info("ContactsServiceImpl.getOutDepModelList,outDepModel={}", outDepModelResult);
            if(!outDepModelResult.isSuccess() || ObjectUtils.isEmpty(outDepModelResult.getData())) continue;
            outDepModelList.add(outDepModelResult.getData());
        }
        return outDepModelList;
    }

    private Result<Void> addDepListInner(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<OutDepModel> outDepModelList) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();

        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        for(OutDepModel outDepModel : outDepModelList) {
            log.info("ContactsServiceImpl.addDepListInner,outDepModel={}", outDepModel);

            //如果父部门ID为0，说明是企微的根部门
            if(StringUtils.equalsIgnoreCase(outDepModel.getParentId(),qywxRootDepId)) {
                updateRootDepAndMapping(ei,enterpriseBindEntity,outDepModel.getId(),outDepModel.getName());
                continue;
            }

            //获取当前部门下的员工，不包括子部门下的员工
            com.facishare.open.qywx.accountinner.result.Result<List<OutEmpModel>> depEmpListResult = qyWeixinManager.getDepEmpList(appId, outEa, outDepModel.getId());
            log.info("ContactsServiceImpl.addDepListInner,appId={},outEa={},outDepId={},depEmpList={}", appId,outEa,outDepModel.getId(),depEmpListResult);

            if(!depEmpListResult.isSuccess()) continue;
            List<OutEmpModel> depEmpList = depEmpListResult.getData();
            //查询外部部门是否绑定
            List<OuterOaDepartmentBindEntity> departmentBindManagerEntities = outerOaDepartmentBindManager.getEntities(OuterOaDepartmentBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).fsEa(fsEa).outDepId(outDepModel.getId()).build());
//            OuterOaDepartmentBindEntity departmentBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepModel.getId());
            log.info("ContactsServiceImpl.addDepListInner,departmentBindManagerEntities={}", departmentBindManagerEntities);

            //如果部门绑定关系不存在，新增部门和员工
            if(CollectionUtils.isEmpty(departmentBindManagerEntities)) {
                //新增部门并保存部门绑定关系
                createAndSaveDepMapping(ei,enterpriseBindEntity,outDepModel.getId(),outDepModel.getName(),outDepModel.getParentId());
                String fsMainDepId = getFsMainDepId(outEa,fsEa,Lists.newArrayList(outDepModel.getId()),appId);
                if(CollectionUtils.isNotEmpty(depEmpList)) {
                    //新增员工并保存绑定关系
                    createAndUpdateDepEmpList(ei,
                            enterpriseBindEntity,
                            fsMainDepId,
                            depEmpList);
                }
            } else {
                OuterOaDepartmentBindEntity departmentBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepModel.getId());
                if (ObjectUtils.isEmpty(departmentBind)) {
                    //更新并启用
                    boolean isAllStop = departmentBindManagerEntities.stream().allMatch(entity -> entity.getBindStatus() == BindStatusEnum.stop);
                    saveDepMapping(fsEa,Integer.valueOf(departmentBindManagerEntities.get(0).getFsDepId()),outEa,outDepModel.getId(),appId, isAllStop ? BindStatusEnum.stop :BindStatusEnum.normal);
                    departmentBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepModel.getId());
                }
                if(departmentBind.getBindStatus().equals(BindStatusEnum.normal)) {
                    //更新部门名称
                    updateDepByOutParentDepId(ei, departmentBind.getFsDepId(), outDepModel.getId(), outDepModel.getName(), "0", outDepModel.getParentId(), outEa, appId);
                    if(CollectionUtils.isNotEmpty(depEmpList)) {
                        //如果部门绑定关系正常，创建和更新部门员工，并更新员工绑定表
                        createAndUpdateDepEmpList(ei,
                                enterpriseBindEntity,
                                departmentBind.getFsDepId(),
                                depEmpList);
                    }
                } else {
                    //如果部门绑定关系被停用，启用纷享部门，并更新部门绑定状态
                    Result<Void> result = updateDepByOutParentDepId(ei, departmentBind.getFsDepId(), outDepModel.getId(), outDepModel.getName(), "0", outDepModel.getParentId(), outEa, appId);
                    log.info("ContactsServiceImpl.addDepListInner,enable dep,fsDepId={},result={}", departmentBind.getFsDepId(),result);
                    if(result.isSuccess()) {
                        //更新部门绑定状态
                        batchUpdateFsDepBindStatus(fsEa,
                                Lists.newArrayList(departmentBind.getFsDepId()),
                                BindStatusEnum.normal,
                                appId,
                                outEa);
                        if(CollectionUtils.isNotEmpty(depEmpList)) {
                            createAndUpdateDepEmpList(ei,
                                    enterpriseBindEntity,
                                    departmentBind.getFsDepId(),
                                    depEmpList);
                        }
                    }
                }
            }
        }
        return new Result<>();
    }

    /**
     * 创建和更新部门员工，并更新员工绑定表
     */
    private void createAndUpdateDepEmpList(int ei,OuterOaEnterpriseBindEntity enterpriseBindEntity,String fsMainDepId,List<OutEmpModel> depEmpList) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        for(OutEmpModel outEmpModel : depEmpList) {
            log.info("ContactsServiceImpl.createAndUpdateDepEmpList,outEmpModel={}", outEmpModel);
            OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, outEmpModel.getId());
            log.info("ContactsServiceImpl.createAndUpdateDepEmpList,employeeBindEntity={}", employeeBindEntity);

            if(ObjectUtils.isEmpty(employeeBindEntity)) {
                //如果员工绑定关系不存在，创建纷享员工，并更新员工绑定表
                createAndSaveEmpMapping(ei,
                        enterpriseBindEntity,
                        outEmpModel.getId());
            } else {
                updateEmpAndMapping(ei,enterpriseBindEntity,outEmpModel.getId());
            }
        }
    }

    /**
     * 更新纷享员工信息，并更新映射表
     * 1、更新员工的时候，如果部门被停用了或者没有的话，就不更新主属部门
     * 2、一律不更新角色权限
     * 3、如果是密文的姓名，不用更新
     * @param ei
     */
    private void updateEmpAndMapping(int ei, OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId) {
        com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> empUpdateResult =
                objectDataManager.updateEmpData(enterpriseBindEntity, outUserId);
        log.info("ContactsServiceImpl.updateEmpAndMapping,empUpdateResult={}", empUpdateResult);

//        String fsEmpId = employeeBindEntity.getFsEmpId();
//        FsEmpArg arg = FsEmpArg.builder()
//                .ei(ei + "")
//                .id(fsEmpId)
////                .name(outUserName)
////                .fullName(outUserName)
//                .status("0")
//                //.mainDepartment(Lists.newArrayList(fsMainDepId))
//                .build();
//        if(StringUtils.isNotEmpty(fsMainDepId)
//                && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.ALL_COMPANY_DEPARTMENT_ID.toString())
//                && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.UNALLOCATED_DEPARTMENT_ID.toString())) {
//            arg.setMainDepartment(Lists.newArrayList(fsMainDepId));
//        }
//
//        if(StringUtils.isNotEmpty(outUserName) && !outUserName.contains(ContactsUtils.emp_prefix)) {
//            arg.setName(outUserName);
//            arg.setFullName(outUserName);
//        }
//
//        LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,update,arg={}", arg);
//        com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsEmployeeServiceProxy.update(arg,
//                null,
//                null);
//        LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,update,result={}", result);
//
//        //code=*********代表CRM返回的数据已作废或已删除
//        if(!result.isSuccess() && result.getCode()==*********) {
//            try {
//                //兼容历史客户fsEmpId为字符串的场景，需要把数字ID转换成真实的基于对象的_id字段
//                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> detail = fsEmployeeServiceProxy.detail(ei + "",
//                        fsEmpId);
//                LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,detail,result={}", detail);
//                if(detail.isSuccess()) {
//                    String newFsEmpId = detail.getData().getId();
//                    LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,detail,fsEmpId={},newFsEmpId={}", fsEmpId,newFsEmpId);
//                    arg.setId(newFsEmpId);
//
//                    result = fsEmployeeServiceProxy.update(arg,
//                            null,
//                            null);
//                    LogUtils.info("ContactsServiceImpl.updateEmpAndMapping,update,result2={}", result);
//                }
//            } catch (Exception e) {
//
//            }
//        }
//
//        if(result.isSuccess()) {
//            employeeBindEntity.setBindStatus(BindStatusEnum.normal);
//            employeeBindEntity.setUpdateTime(System.currentTimeMillis());
//            if (StringUtils.isEmpty(employeeBindEntity.getId())) {
//                com.facishare.open.qywx.accountbind.result.Result<Boolean> employeeMapping = qyweixinAccountBindService.bindAccountEmployeeMapping(Lists.newArrayList(employeeBindEntity));
//                log.info("ContactsServiceImpl.updateEmpAndMapping,employeeMapping={}", employeeMapping);
//            } else {
//                corpManager.updateEmployeeBindStatus(employeeBindEntity);
//            }
//        }
    }

    @Override
    public Result<Void> updateDepList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList) {
        List<OutDepModel> outDepModelList = getOutDepModelList(enterpriseBindEntity , outDepIdList);
        return updateDepListInner(enterpriseBindEntity,outDepModelList);
    }

    private Result<Void> updateDepListInner(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<OutDepModel> outDepModelList) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        for(OutDepModel outDepModel : outDepModelList) {
            log.info("ContactsServiceImpl.updateDepList,outDepModel={}",outDepModel);
            //查询部门绑定关系，不区分绑定状态
            OuterOaDepartmentBindEntity departmentBindEntity = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, fsEa, outEa, appId, outDepModel.getId());
            log.info("ContactsServiceImpl.updateDepList,departmentBindEntity={}", departmentBindEntity);
            if(ObjectUtils.isEmpty(departmentBindEntity)) {
                log.info("ContactsServiceImpl.updateDepList,dep not in mapping,appId={},outEa={},outDepId={},",appId,outEa,outDepModel.getId());
                continue;
            }

//            if(StringUtils.equalsIgnoreCase(outDepModel.getId(),outDepModel.getName())) {
//                //如果部门名称是密文，则停止更新部门名称
//                log.info("ContactsServiceImpl.updateDepList,depName={},是密文，停止更新部门名称",outDepModel.getName());
//                continue;
//            }
//            updateDep(ei,depMapping.getFsDepartmentId()+"",outDepModel.getId(),outDepModel.getName(), "0", null);
            updateDepByOutParentDepId(ei,departmentBindEntity.getFsDepId(),outDepModel.getId(),outDepModel.getName(), "0",outDepModel.getParentId(),outEa,appId);
        }
        return new Result<>();
    }

    private Result<Void> updateDepByOutParentDepId(int ei,String fsDepId,String outDepId,String outDepName,String status,String outParentDepId,String outEa,String appId) {
        String ea = eieaConverter.enterpriseIdToAccount(ei);
        //查询父部门是否已绑定或者是否已停用，父部门被停用，需要把父部门启用
        OuterOaDepartmentBindEntity parentDepartmentBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, ea, outEa, appId, outParentDepId);
        log.info("ContactsServiceImpl.updateDepByOutParentDepId,outParentDepId={},parentDepartmentBind={}",outParentDepId, parentDepartmentBind);

        String fsParentDepId = null;
        if(ObjectUtils.isEmpty(parentDepartmentBind)) {
            fsParentDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        } else {
            if(parentDepartmentBind.getBindStatus().equals(BindStatusEnum.stop)) {
                String departmentId = parentDepartmentBind.getFsDepId();
                com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> updateDepResult = fsDepartmentServiceProxy.detail(ei, departmentId);
                if(updateDepResult.isSuccess() && ObjectUtils.isNotEmpty(updateDepResult.getData()) && ObjectUtils.isNotEmpty(updateDepResult.getData().get("dept_parent_path"))) {
                    List<String> departmentIdList = Splitter.on(".").splitToList(updateDepResult.getData().get("dept_parent_path").toString());
                    //过滤掉根部门和待分配部门
                    List<String> updateDepartmentIdList = departmentIdList.stream()
                            .filter(v -> (!v.equals(GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"") && !v.equals(GlobalValue.UNALLOCATED_DEPARTMENT_ID+"")))
                            .collect(Collectors.toList());
                    updateDepartmentIdList.add(departmentId);
                    //避免前面将部门乱序，多次循环调用
                    //无论是企微还是纷享，子部门id不一定比父部门id大
                    List<String> updateFsDepIdList;
                    do {
                        updateFsDepIdList = new LinkedList<>(updateDepartmentIdList);
                        for(String depId : updateFsDepIdList) {
                            //查询数据库，看是否已停用
                            if(!outParentDepId.equals(depId)) {
                                OuterOaDepartmentBindEntity departmentBind = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntityByOutDepId(ChannelEnum.qywx, ea, outEa, appId, depId);
                                if(ObjectUtils.isNotEmpty(departmentBind) && departmentBind.getBindStatus().equals(BindStatusEnum.normal)) {
                                    updateDepartmentIdList.remove(depId);
                                    continue;
                                }
                            }
                            //启用部门
                            //TODO
                            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result
                                    = fsDepartmentServiceProxy.toggle(ei + "", depId, true);
                            if(result.isSuccess()) {
                                //更新数据库状态
                                batchUpdateFsDepBindStatus(ea,
                                        Lists.newArrayList(depId),
                                        BindStatusEnum.normal,
                                        appId,
                                        outEa);
                                updateDepartmentIdList.remove(depId);
                            }
                        }
                        log.info("ContactsServiceImpl.updateDepByOutParentDepId,fsDepIdList.size={},updateFsDepIdList.size={}", updateDepartmentIdList.size(), updateFsDepIdList.size());
                    } while(CollectionUtils.isNotEmpty(updateDepartmentIdList) && updateFsDepIdList.size() != updateDepartmentIdList.size());
                }
            }
            fsParentDepId = parentDepartmentBind.getFsDepId();
        }
        return this.updateDep(ei, fsDepId, outDepId, outDepName, status, fsParentDepId);
    }

    private Result<Void> updateDep(int ei,String fsDepId,String outDepId,String outDepName,String status,String fsParentDepId) {
        //TODO
        FsDeptArg fsDeptArg = FsDeptArg.builder()
                .ei(ei+"")
                .id(fsDepId)
                //.name(outDepId.equals(outDepName) ? null : outDepName)
                .code(outDepId)
                .status(status)
                .parentId(StringUtils.isNotEmpty(fsParentDepId) ? Lists.newArrayList(fsParentDepId) : null)
                .build();
        if(StringUtils.isNotEmpty(outDepName) && !outDepName.contains(ContactsUtils.dep_prefix)) {
            fsDeptArg.setName(outDepName);
        }
        log.info("ContactsServiceImpl.updateDep,batch update dep,fsDeptArg={}", fsDeptArg);
        com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateDepResult = fsDepartmentServiceProxy.update(fsDeptArg);
        log.info("ContactsServiceImpl.updateDep,batch update dep,updateDepResult={}", updateDepResult);
        if(updateDepResult.isSuccess()) {
            return new Result<>();
        } else {
            return new Result<Void>().addError(ErrorRefer.START_DEPARTMENT_ERROR.getCode(), updateDepResult.getMsg(),null);
        }
    }

    @Override
    public Result<Void> stopDepList(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList) {
        log.info("ContactsServiceImpl.stopDepList,outDepIdList={}", outDepIdList);
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<OuterOaDepartmentBindEntity> depMappingList = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa, appId, null, outDepIdList);
        log.info("ContactsServiceImpl.stopDepList,depMappingList={}", depMappingList);

        if(CollectionUtils.isEmpty(depMappingList)) {
            return new Result<>();
        }

        for (OuterOaDepartmentBindEntity depMapping : depMappingList) {
            //停用这个部门
            batchStopFsDep(ei, fsEa, depMapping.getFsDepId(), appId, outEa);
        }
        return new Result<>();
    }

    /**
     * 批量停用部门内部方法
     * @param outDepIdList
     * @param excludeFsDepEmpMap 停用部门时，不需要被停用的纷享员工map
     * @return
     */
    public Result<Void> stopDepListInner(OuterOaEnterpriseBindEntity enterpriseBindEntity, List<String> outDepIdList, Map<String,List<String>> excludeFsDepEmpMap) {
        log.info("ContactsServiceImpl.stopDepList,outDepIdList={},excludeFsDepEmpMap={}",outDepIdList,excludeFsDepEmpMap);
        if(CollectionUtils.isEmpty(outDepIdList)) {
            log.info("ContactsServiceImpl.stopDepList,outDepIdList is empty,return");
            return new Result<>();
        }
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();

        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        List<OuterOaDepartmentBindEntity> depMappingList = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa, appId, null, outDepIdList);
        log.info("ContactsServiceImpl.stopDepList,depMappingList={}", depMappingList);

        if(CollectionUtils.isEmpty(depMappingList)) {
            return new Result<>();
        }

        Map<String, OuterOaDepartmentBindEntity> depMappingMap = depMappingList.stream()
                .collect(Collectors.toMap(OuterOaDepartmentBindEntity::getFsDepId, Function.identity()));

        //查询全公司下所有的部门
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> batchGetAllFsDepResult = fsContactsServiceProxy
                .batchGetAllFsDep(ei, GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        if (!batchGetAllFsDepResult.isSuccess() || CollectionUtils.isEmpty(batchGetAllFsDepResult.getData())) {
            return new Result<Void>().addError(ErrorRefer.START_DEPARTMENT_ERROR.getCode(), batchGetAllFsDepResult.getMsg(),null);
        }
        List<ObjectData> dataList = batchGetAllFsDepResult.getData();
        //反转
        Collections.reverse(dataList);

        //找出所有的子部门
        for (ObjectData deptObjectData : dataList) {
            //查询是否有绑定关系
            if (!depMappingMap.containsKey(deptObjectData.getId())) {
                continue;
            }

            //停用这个部门下的人员
            batchStopFsEmp(ei,enterpriseBindEntity,deptObjectData.getId(),excludeFsDepEmpMap.get(deptObjectData.getId()));
            //停用这个部门
            batchStopFsDep(ei, fsEa, deptObjectData.getId(), appId, outEa);
        }
        return new Result<>();
    }

    @Override
    public Result<Void> addUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, OutEmpModel outEmpModel) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        String outEa = enterpriseBindEntity.getOutEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        String fsMainDepId = GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"";
        //查询用户的部门的绑定状态
        List<OuterOaDepartmentBindEntity> depBindList = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa, appId, null, outEmpModel.getDepIdList());

        log.info("ContactsServiceImpl.addUser,depBindList={}", depBindList);

        if(CollectionUtils.isNotEmpty(depBindList)) {
            //获取主属部门
            fsMainDepId = depBindList.get(0).getFsDepId();
        }
        Integer fsUserId = createAndSaveEmpMapping(ei, enterpriseBindEntity, outEmpModel.getId());
        log.info("ContactsServiceImpl.addUser,create fs emp,fsUserId={}", fsUserId);
        if(fsUserId==null) {
            return Result.newInstance(ErrorRefer.FS_EMP_CREATE_FAILED);
        }
        return new Result<>();
    }

//    //一对多场景，默认获取第一个绑定的纷享企业，如果客户不想
//    private String getFsEa(String outEa) {
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEnterpriseMapping>> enterpriseBind = qyweixinAccountBindService.selectEnterpriseBind(SourceTypeEnum.QYWX.getSourceType(), outEa);
//        log.info("ContactsServiceImpl.getFsEa,query enterprise mapping,enterpriseBind={}", enterpriseBind);
//        if(CollectionUtils.isNotEmpty(enterpriseBind.getData())) {
//            if(enterpriseBind.getData().size()==1) {
//                return enterpriseBind.getData().get(0).getFsEa();
//            }
//            if(enterpriseBind.getData().size()>1) {
//                log.info("ContactsServiceImpl.getFsEa,企微一对多场景，可能有问题");
//                return enterpriseBind.getData().get(0).getFsEa();
//            }
//        }
//        return null;
//    }

    @Override
    public Result<Void> updateUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId, String newOutUserId) {
        log.info("ContactsServiceImpl.updateUserInfo,enterpriseBindEntity={},outUserId={},newUserId={}",enterpriseBindEntity,outUserId,newOutUserId);
        //先看看是否已经绑定
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        String outEa = enterpriseBindEntity.getOutEa();
        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, outUserId);
        log.info("ContactsServiceImpl.updateUserInfo,employeeBindEntity={}",employeeBindEntity);
        if(ObjectUtils.isEmpty(employeeBindEntity)) {
            //重新尝试绑定
            return addUser(enterpriseBindEntity, StringUtils.isNotEmpty(newOutUserId) ? newOutUserId : outUserId);
        }

        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        Integer fsUserId = Integer.valueOf(employeeBindEntity.getFsEmpId());

        //如果客户修改了用户ID，更新用户ID
        if(StringUtils.isNotEmpty(newOutUserId) && StringUtils.equalsIgnoreCase(outUserId,newOutUserId)==false) {
            employeeBindEntity.setOutEmpId(newOutUserId);
            employeeBindEntity.setBindStatus(BindStatusEnum.normal);
            employeeBindEntity.setUpdateTime(System.currentTimeMillis());
            Integer count = outerOaEmployeeBindManager.updateById(employeeBindEntity);
            log.info("ContactsServiceImpl.updateUserInfo,update employee bind,count={}", count);
        }

//        if(StringUtils.equalsIgnoreCase(outEmpModel.getId(),outEmpModel.getName())==false) {
//            String fsMainDepId = null;
//            if(CollectionUtils.isNotEmpty(outEmpModel.getDepIdList())) {
//                //查询用户的部门的绑定状态
//                List<OuterOaDepartmentBindEntity> depBindList = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa, appId, null, outEmpModel.getDepIdList());
//                log.info("ContactsServiceImpl.updateUserInfo,depBindList={}", depBindList);
//                if(CollectionUtils.isNotEmpty(depBindList)) {
//                    fsMainDepId = depBindList.get(0).getFsDepId();
//                }
//            }

//            FsEmpArg arg = FsEmpArg.builder()
//                    .ei(ei + "")
//                    .id(fsUserId+"")
//                    .status("0")
////                    .name(outEmpModel.getName())
////                    .fullName(outEmpModel.getName())
//                    .build();
//            if(StringUtils.isNotEmpty(fsMainDepId)
//                    && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.ALL_COMPANY_DEPARTMENT_ID.toString())
//                    && !StringUtils.equalsIgnoreCase(fsMainDepId, GlobalValue.UNALLOCATED_DEPARTMENT_ID.toString())) {
//                arg.setMainDepartment(Lists.newArrayList(fsMainDepId));
//            }
//
//            if(StringUtils.isNotEmpty(outEmpModel.getName()) && !outEmpModel.getName().contains(ContactsUtils.emp_prefix)) {
//                arg.setName(outEmpModel.getName());
//                arg.setFullName(outEmpModel.getName());
//            }
//
//            log.info("ContactsServiceImpl.updateUserInfo,update emp, arg={}", arg);
//            com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateEmpResult = fsEmployeeServiceProxy.update(arg,
//                    null,
//                    null);
//            log.info("ContactsServiceImpl.updateUserInfo,update emp, result={}", updateEmpResult);

//            com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> empUpdateResult =
//                    objectDataManager.updateEmpData(enterpriseBindEntity, outEmpModel.getId());
//            log.info("ContactsServiceImpl.updateUserInfo,update emp, result={}", empUpdateResult);
//        }

        //启用绑定关系
//        employeeBindEntity.setBindStatus(BindStatusEnum.normal);
//        employeeBindEntity.setUpdateTime(System.currentTimeMillis());
//        Integer count = outerOaEmployeeBindManager.updateById(employeeBindEntity);
//        log.info("ContactsServiceImpl.updateUserInfo,update employee bind,count={}", count);
        com.facishare.open.outer.oa.connector.common.api.result.Result<IncrementUpdateResult> empUpdateResult =
                objectDataManager.updateEmpData(enterpriseBindEntity, StringUtils.isNotEmpty(newOutUserId) ? newOutUserId : outUserId);
        log.info("ContactsServiceImpl.updateUserInfo,update emp, result={}", empUpdateResult);
        return new Result<>();
    }

    @Override
    public Result<Void> stopUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId) {
        log.info("ContactsServiceImpl.stopUser,enterpriseBindEntity={},outUserId={}", enterpriseBindEntity, outUserId);
        String outEa = enterpriseBindEntity.getOutEa();
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        //查找绑定关系
        OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(ChannelEnum.qywx, outEa, fsEa, appId, outUserId);

        if(ObjectUtils.isEmpty(employeeBindEntity)) {
            return Result.newInstance(ErrorRefer.QYWX_EMP_MAPPING_NOT_EXIST);
        }

        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        Integer fsUserId = Integer.valueOf(employeeBindEntity.getFsEmpId());
        if(fsUserId==1000) {
            log.info("ContactsServiceImpl.stopUser,fsUserId={},1000 emp is not stop", fsUserId);
            return Result.newInstance(ErrorRefer.FS_ADMIN_CANNOT_STOP);
        }
        com.facishare.open.outer.oa.connector.common.api.result.Result<Void> removeEmpData = objectDataManager.removeEmpData(enterpriseBindEntity, employeeBindEntity.getOutEmpId(), RemoveEmployeeEventType.RESIGN_EMPLOYEE);
        log.info("ContactsServiceImpl.stopUser,fsEa={},fsUserId={},removeEmpData={}", fsEa,fsUserId,removeEmpData);

//        com.facishare.open.order.contacts.proxy.api.result.Result<Void> stopResult = fsEmployeeServiceProxy.toggle(
//                ei + "",
//                fsUserId + "",
//                false,
//                null,
//                null);
//        log.info("ContactsServiceImpl.stopUser,stopResult={}", stopResult);
//
//        employeeBindEntity.setBindStatus(BindStatusEnum.stop);
//        employeeBindEntity.setUpdateTime(System.currentTimeMillis());
//        Integer count = outerOaEmployeeBindManager.updateById(employeeBindEntity);
//        log.info("ContactsServiceImpl.stopUser,update employee bind,count={}", count);
        return new Result<>();
    }

//    private QyweixinAccountEmployeeMapping getEmpMappingByOutUserId(String outEa, String outUserId,String appId) {
//        appId = convertAppId(appId);
//
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountEmployeeMapping>> result
//                = qyweixinAccountBindService.employeeToOutAccount(SourceTypeEnum.QYWX.getSourceType(),
//                outEa,
//                appId,
//                Lists.newArrayList(outUserId));
//        log.info("ContactsServiceImpl.stopUser,query emp mapping,result={}", result);
//        if(CollectionUtils.isEmpty(result.getData()))
//            return null;
//
//        QyweixinAccountEmployeeMapping employeeMapping = result.getData().get(0);
//        return employeeMapping;
//    }

//    private QyweixinAccountDepartmentMapping getDepMappingByOutDepId(String outEa,String outDepId,int status,String appId) {
//        appId = convertAppId(appId);
//
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> departmentBind = null;
//        //查询企微的根部门是否已经和纷享的根部门绑定
//        departmentBind = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(),
//                outEa,
//                appId,
//                status,
//                Lists.newArrayList(outDepId));
//        if(CollectionUtils.isNotEmpty(departmentBind.getData())) {
//            return departmentBind.getData().get(0);
//        }
//        return null;
//    }

//    private List<QyweixinAccountDepartmentMapping> getDepMappingList(String outEa,List<String> outDepIdList,int status,String appId) {
//        appId = convertAppId(appId);
//
//        com.facishare.open.qywx.accountbind.result.Result<List<QyweixinAccountDepartmentMapping>> departmentBind = null;
//        //查询企微的根部门是否已经和纷享的根部门绑定
//        departmentBind = qyweixinAccountBindService.queryDepartmentBindByOutDepartment(SourceTypeEnum.QYWX.getSourceType(),
//                outEa,
//                appId,
//                status,
//                outDepIdList);
//        if(CollectionUtils.isNotEmpty(departmentBind.getData())) return departmentBind.getData();
//        return new ArrayList<>();
//    }

    private void batchStopFsEmp(int ei, OuterOaEnterpriseBindEntity enterpriseBindEntity, String fsDepId,List<String> excludeFsUserIdList) {
        log.info("ContactsServiceImpl.batchStopFsEmp,enterpriseBindEntity={},fsDepId={},excludeFsUserIdList={}", enterpriseBindEntity,fsDepId,excludeFsUserIdList);
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();

        //只需要查出这个部门下的人员就好，不需要再查出子部门下的人员
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> listResult =
                fsEmployeeServiceProxy.listByDepId(ei,fsDepId);

        if (!listResult.isSuccess() || CollectionUtils.isEmpty(listResult.getData())) {
            return;
        }

        //过滤
        List<String> fsUserIdList = listResult.getData().stream()
                .map(ObjectData::getId).collect(Collectors.toList());
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,ei={},fsDepId={},fsUserIdList={}",ei,fsDepId,fsUserIdList);
        if(CollectionUtils.isNotEmpty(excludeFsUserIdList)) {
            fsUserIdList.removeAll(excludeFsUserIdList);
        }
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,fsUserIdList2={}", fsUserIdList);
        if(fsUserIdList.contains("1000")) {
            //纷享企业管理员不能停用
            fsUserIdList.remove("1000");
            //把管理员移到到全公司下
            fsEmployeeServiceProxy.bulkResetMainDepartment(ei+"", Lists.newArrayList("1000"),
                    GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        }
        LogUtils.info("FsContactsServiceProxyImpl.batchStopFsEmp,fsUserIdList3={}", fsUserIdList);
        if(CollectionUtils.isEmpty(fsUserIdList)) {
            return;
        }

        List<OuterOaEmployeeBindEntity> entitiesByFsEmpIds = outerOaEmployeeBindManager.getEntitiesByFsEmpIds(fsUserIdList, dcId);
        if (CollectionUtils.isEmpty(entitiesByFsEmpIds)) {
            return;
        }

        for (OuterOaEmployeeBindEntity entity : entitiesByFsEmpIds) {
            com.facishare.open.outer.oa.connector.common.api.result.Result<Void> removeEmpData = objectDataManager.removeEmpData(enterpriseBindEntity, entity.getOutEmpId(), RemoveEmployeeEventType.REMOVE_RANGE);
            log.info("ContactsServiceImpl.batchStopFsEmp,fsEa={},fsDepId={},fsUserId={},removeEmpData={}", fsEa,fsDepId,entity.getFsEmpId(),removeEmpData);
        }
    }

    private boolean isParentDep(int ei,String fsDepId) {
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> list = fsDepartmentServiceProxy.list(ei, GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        for(ObjectData dep : list.getData()) {
            String dept_parent_path = dep.getString("dept_parent_path");
            if(StringUtils.containsIgnoreCase(dept_parent_path,fsDepId)) {
                return true;
            }
        }
        return false;
    }

    private Result<Void> batchStopFsDep(int ei, String fsEa, String fsDepId, String appId, String outEa) {
        //得看是否需要真的停用
        List<OuterOaDepartmentBindEntity> outerOaDepartmentBindEntities = outerOaDepartmentBindManager.queryOuterOaDepartmentBindEntities(ChannelEnum.qywx, fsEa, outEa,  null, Lists.newArrayList(fsDepId), null);
        if (CollectionUtils.isEmpty(outerOaDepartmentBindEntities)) {
            return new Result<>();
        }
        if(StringUtils.equalsIgnoreCase(fsDepId,GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"")
                || StringUtils.equalsIgnoreCase(fsDepId,GlobalValue.UNALLOCATED_DEPARTMENT_ID+"")) {
            //只需要停用中间表的绑定关系
            log.info("ContactsServiceImpl.batchStopFsDep,stop dept bind,ei={},fsDepId={}", ei, fsDepId);
            batchUpdateFsDepBindStatus(fsEa, Lists.newArrayList(fsDepId), BindStatusEnum.stop, appId, outEa);
            return new Result<>();
        }
        outerOaDepartmentBindEntities = outerOaDepartmentBindEntities.stream().filter((outerOaDepartmentBindEntity)->outerOaDepartmentBindEntity.getBindStatus() == BindStatusEnum.normal).collect(Collectors.toList());
        //如果存在其他appId，就不停用
        String finalAppId = appId;
        boolean isOtherAppId = outerOaDepartmentBindEntities.stream().anyMatch((outerOaDepartmentBindEntity)->!StringUtils.equalsIgnoreCase(outerOaDepartmentBindEntity.getAppId(), finalAppId));
        if (!isOtherAppId) {
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> result = fsDepartmentServiceProxy.toggle(ei + "",
                    fsDepId,false);
            log.info("ContactsServiceImpl.batchStopFsDep,result={}", result);
            //无论成功还是失败，都要停用中间表的绑定关系
        }

        batchUpdateFsDepBindStatus(fsEa,Lists.newArrayList(fsDepId),BindStatusEnum.stop,appId,outEa);

        return new Result<>();
    }

    private void batchUpdateFsEmpBindStatus(List<String> fsAccountList,
                                            BindStatusEnum status,
                                            String appId,
                                            String outEa,
                                            String fsEa) {
        Integer count = outerOaEmployeeBindManager.updateStatusByFsEmpIds(ChannelEnum.qywx, fsEa, outEa, appId, fsAccountList, status);
        log.info("ContactsServiceImpl.batchUpdateFsEmpBindStatus,update employee bind,count={}", count);
    }

    private void batchUpdateFsDepBindStatus(String fsEa,List<String> fsDepIdList,BindStatusEnum status,String appId, String outEa) {
        Integer count = outerOaDepartmentBindManager.updateStatusByFsDeptIds(ChannelEnum.qywx, fsEa, outEa, appId, fsDepIdList, status);
        log.info("ContactsServiceImpl.batchUpdateFsDepBindStatus,update employee bind,count={}", count);
    }

    /**
     * 把代开发应用的ID转换成非代开发应用的ID
     * @param appId
     * @return
     */
    private String convertAppId(String appId) {
        appId = StringUtils.equalsIgnoreCase(appId,repAppId) ? ConfigCenter.crmAppId:appId;
        return appId;
    }

    /**
     * 选择的人员和人员所属的部门处于可见范围时，应去掉这个人员
     */
    private List<OutEmpModel> removeEmployeeList(List<OutEmpModel> empList, AppInfo appInfo, String appId, String outEa) {
        Set<String> employeeInfoSet = new HashSet<>();
        //获取标签下单独选择的人员列表，是没有部门返回的
        if(ObjectUtils.isNotEmpty(appInfo.getAllow_tags()) && CollectionUtils.isNotEmpty(appInfo.getAllow_tags().getTagid())) {
            //调用部门下的人员列表，是因为人员详情接口，即使部门在标签下，仍然获取不到该人员的所属部门
            Set<String> departmentIdList = new HashSet<>(appInfo.getAllow_partys().getPartyid());
            for(String tagId : appInfo.getAllow_tags().getTagid()) {
                com.facishare.open.qywx.accountinner.result.Result<TagDetailModel> tagDetailModelResult = qyWeixinManager.getTagDetail(appId, outEa, tagId);
                log.info("ContactsServiceImpl.removeEmployeeList,getTagDetail,tagDetailModel={}", tagDetailModelResult);
                if(!tagDetailModelResult.isSuccess() || tagDetailModelResult.getData()==null || CollectionUtils.isEmpty(tagDetailModelResult.getData().getEmpList())) continue;
                departmentIdList.addAll(tagDetailModelResult.getData().getDepList());
            }
            log.info("ContactsServiceImpl.removeEmployeeList,departmentIdList={}", departmentIdList);
            for(String departmentId : departmentIdList) {
                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeList = qyWeixinManager.getDepartmentEmployeeList(appId, outEa, departmentId);
                if(!departmentEmployeeList.isSuccess() || ObjectUtils.isEmpty(departmentEmployeeList.getData()) || CollectionUtils.isEmpty(departmentEmployeeList.getData().getUserlist())) {
                    continue;
                }
                Set<String> departmentEmployeeSet = departmentEmployeeList.getData().getUserlist().stream()
                        .map(QyweixinUserDetailInfoRsp :: getUserid)
                        .collect(Collectors.toSet());
                if(CollectionUtils.isNotEmpty(departmentEmployeeSet)) {
                    employeeInfoSet.addAll(departmentEmployeeSet);
                }
            }
        }
        log.info("ContactsServiceImpl.removeEmployeeList,employeeInfoSet={}", employeeInfoSet);
        //如果人员有所属部门或者在部门下，证明已处理过,去掉
        List<OutEmpModel> updateEmpList = empList.stream()
                .filter(v -> CollectionUtils.isEmpty(v.getDepIdList()))
                .filter(v -> !employeeInfoSet.contains(v.getId()))
                .collect(Collectors.toList());
        return updateEmpList;
    }

    @Override
    public Result<Void> addUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, String outUserId) {
        log.info("ContactsServiceImpl.addUser,enterpriseBindEntity={},outUserId={}", enterpriseBindEntity,outUserId);
        String fsEa = enterpriseBindEntity.getFsEa();
        String appId = enterpriseBindEntity.getAppId();
        String outEa = enterpriseBindEntity.getOutEa();
        int ei = eieaConverter.enterpriseAccountToId(fsEa);

        com.facishare.open.qywx.accountinner.result.Result<OutEmpModel> outEmpModelResult = qyWeixinManager.getUserModel(appId,outEa,outUserId);
        log.info("ContactsServiceImpl.addUser,outEmpModelResultModel={}", outEmpModelResult);
        if(!outEmpModelResult.isSuccess()) {
            if(outEmpModelResult.getCode().equalsIgnoreCase("60011")) {
                return Result.newInstance(ErrorRefer.QYWX_NOT_PRIVILEGE_ERROR);
            }
            return new Result<>(outEmpModelResult.getCode(), outEmpModelResult.getMsg(),null);
        }

        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().dcId(enterpriseBindEntity.getId()).outEmpId(outEmpModelResult.getData().getId()).build());

        log.info("ContactsServiceImpl.addUser,query user mapping,employeeBindEntities={}", employeeBindEntities);
        //如果员工绑定关系不存在，创建员工并更新员工映射表
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
//            String fsMainDepId = getFsMainDepId(outEa,fsEa,outEmpModelResult.getData().getDepIdList(),appId);
//            FsEmpArg arg = FsEmpArg.builder()
//                    .ei(ei + "")
//                    .name(outEmpModelResult.getData().getName())
//                    .fullName(outEmpModelResult.getData().getName())
//                    .mainDepartment(Lists.newArrayList(fsMainDepId))
//                    .status("0")
//                    .isActive(true)
//                    .build();
//            log.info("ContactsServiceImpl.addUser,arg={}", arg);
//            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
//                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode()),
//                    FsEmployeeRoleCodeEnum.SALES.getRoleCode());
//            log.info("ContactsServiceImpl.addUser,result={}", result);
//            if(result.isSuccess()) {
//                Integer fsUserId = Integer.valueOf(result.getData().getId());
//                saveEmpMapping(fsEa,fsUserId,outEa,outUserId,appId,BindStatusEnum.normal);
//                return new Result<>();
//            }
            com.facishare.open.outer.oa.connector.common.api.result.Result<ActionAddResult> empCreateResult =
                    objectDataManager.createEmployee(enterpriseBindEntity, outUserId);
            if(empCreateResult.isSuccess()) {
                OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getEmployeeBindEntity(enterpriseBindEntity.getChannel(), enterpriseBindEntity.getOutEa(),
                        enterpriseBindEntity.getFsEa(), enterpriseBindEntity.getAppId(), outUserId);
                if (ObjectUtils.isNotEmpty(employeeBindEntity)) {
                    eventBindSendMqManager.employeeBindChangeEvent(enterpriseBindEntity.getId(), Lists.newArrayList(employeeBindEntity.getId()), EmplyeeBindChangeTypeEnum.employee_bind);
                }
                return new Result<>();
            }
            if(empCreateResult.getCode() == 44) {
                return Result.newInstance(ErrorRefer.CRM_USER_UPPER_LIMIT_INITED);
            }
            return new Result<>(ErrorRefer.FS_EMP_CREATE_FAILED.getCode(), empCreateResult.getMsg(),null);
        }
        return new Result<>();

    }

    @Override
    public Result<Void> repVisibleRangeChange(String outEa, String appId, TagChangeEventXml tagChangeEventXml, String infoType, String plainMsg) {
        updateTag(outEa, appId, tagChangeEventXml, infoType, plainMsg);
        //通讯录变更事件，在代开发只针对指定企业的账号自动同步，还有账号绑定
        this.changeContacts(outEa, appId, plainMsg);
        return new Result<>();
    }

    public void updateTag(String outEa, String appId, TagChangeEventXml tagChangeEventXml, String infoType, String plainMsg) {
        if(tagChangeEventXml!=null && StringUtils.equalsIgnoreCase(tagChangeEventXml.getChangeType(),"update_tag")) {
            saveAppEvent(outEa, appId, infoType, plainMsg);

//            List<QyweixinAccountEnterpriseMapping> enterpriseMappingList = qyweixinAccountBindInnerService.queryEnterpriseBindByOutEa(corpId);
//            QyweixinAccountEnterpriseMapping enterpriseMapping = enterpriseMappingList.get(0);
//            corpId = enterpriseMapping.getOutEa();
//
//            String fsEa = enterpriseMapping.getFsEa();
//
//            GetFsUserIdsByRestResult fsUserIds = fsManager.getFsUserIdsByRestService(fsEa,
//                    1000);
//
//            List<Integer> userIdList = fsUserIds.getData().stream()
//                    .map(v -> Integer.valueOf(v.substring(v.lastIndexOf(".") + 1)))
//                    .collect(Collectors.toList());
//
//            List<EmployeeDto> employeeInfos = fsManager.getEmployeeInfos(fsEa,
//                    1000, userIdList);
//
//            addTagUserList(tagChangeEventXml,fsEa,corpId,employeeInfos);
////            delTagUserList(tagChangeEventXml,fsEa);
//            addTagDepUserList(tagChangeEventXml,fsEa,corpId,employeeInfos);
////            delTagDepUserList(tagChangeEventXml,fsEa,corpId);
        }
    }

//    private void addTagUserList(TagChangeEventXml tagChangeEventXml,String fsEa,String corpId,List<EmployeeDto> employeeInfos) {
//        List<QyweixinUserDetailInfoRsp> addUserList= new ArrayList<>();
//        if(StringUtils.isNotEmpty(tagChangeEventXml.getAddUserItems())) {
//            List<String> userList = Splitter.on(",").splitToList(tagChangeEventXml.getAddUserItems());
//            log.info("QyweixinGatewayInnerServiceImpl.updateTag,add user,userList={}",userList);
//            for(String userId : userList) {
//                com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId,corpId,userId);
//                if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
//                    addUserList.add(userDetailInfoRspResult.getData());
//                }
//            }
//        }
//        batchUpdateEmployeeBind(fsEa,corpId,employeeInfos,addUserList);
//    }

//    private void addTagDepUserList(TagChangeEventXml tagChangeEventXml,String fsEa,String corpId,List<EmployeeDto> employeeInfos) {
//        List<QyweixinUserDetailInfoRsp> userList= new ArrayList<>();
//        if(StringUtils.isNotEmpty(tagChangeEventXml.getAddPartyItems())) {
//            List<String> depList = Splitter.on(",").splitToList(tagChangeEventXml.getAddPartyItems());
//            log.info("QyweixinGatewayInnerServiceImpl.updateTag,add party,depList={}",depList);
//            for(String depId : depList) {
//                com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentEmployeeListRsp> departmentEmployeeListResult = qyWeixinManager.getDepartmentEmployeeList(ConfigCenter.repAppId,
//                        corpId, depId);
//                if(departmentEmployeeListResult.isSuccess() && departmentEmployeeListResult.getData()!=null && CollectionUtils.isNotEmpty(departmentEmployeeListResult.getData().getUserlist())) {
//                    userList.addAll(departmentEmployeeListResult.getData().getUserlist());
//                }
//            }
//        }
//        batchUpdateEmployeeBind(fsEa,corpId,employeeInfos,userList);
//    }


//    private void batchUpdateEmployeeBind(String fsEa, String corpId, List<EmployeeDto> employeeInfos, List<QyweixinUserDetailInfoRsp> userList) {
//        if(CollectionUtils.isNotEmpty(userList)) {
//            List<QyweixinAccountEmployeeMapping> newMappingList = new ArrayList<>();
//            for(QyweixinUserDetailInfoRsp userDetailInfoRsp : userList) {
//                Result<List<QyweixinAccountEmployeeMapping>> result = qyweixinAccountBindInnerService.getEmployeeMapping(corpId,
//                        userDetailInfoRsp.getUserid(),-1,fsEa);
//                if(CollectionUtils.isNotEmpty(result.getData())) {
//                    result.getData().get(0).setStatus(0);
//                    //更新员工状态
//                    int count = qyweixinAccountBindInnerService.updateQyweixinAccountEmployee(result.getData().get(0));
//                    log.info("QyweixinGatewayInnerServiceImpl.updateTag,count={}",count);
//                } else {
//                    List<EmployeeDto> list = employeeInfos.stream()
//                            .filter(item->StringUtils.equalsIgnoreCase(item.getMobile(),userDetailInfoRsp.getMobile()))
//                            .collect(Collectors.toList());
//                    if(CollectionUtils.isEmpty(list)) continue;
//
//                    QyweixinAccountEmployeeMapping accountEmployeeMapping = new QyweixinAccountEmployeeMapping();
//                    accountEmployeeMapping.setSource("qywx");
//                    accountEmployeeMapping.setFsAccount("E."+fsEa+"."+list.get(0).getEmployeeId());
//                    accountEmployeeMapping.setIsvAccount(userDetailInfoRsp.getUserid());
//                    accountEmployeeMapping.setOutAccount(userDetailInfoRsp.getUserid());//这个后面需要替换掉
//                    //accountEmployeeMapping.setAppId(crmAppId);
//                    accountEmployeeMapping.setOutEa(corpId);
//
//                    newMappingList.add(accountEmployeeMapping);
//                }
//            }
//            if(CollectionUtils.isNotEmpty(newMappingList)) {
//                //批量绑定员工
//                log.info("QyweixinGatewayInnerServiceImpl.updateTag,newMappingList={}",newMappingList);
//                qyweixinAccountBindService.bindAccountEmployeeMapping(newMappingList);
//            }
//
//            //自动绑定账号
//            Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
//            }.getType());
//            if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
//                log.info("QyweixinGatewayInnerServiceImpl.updateTag,autoBindEmpEnterpriseMap,fsEa={}",fsEa);
//                corpManager.autoBindEmpAccount(fsEa, corpId, userList);
//            } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
//                log.info("QyweixinGatewayInnerServiceImpl.updateTag,AUTO_BIND_ACCOUNT_EA,fsEa={}",fsEa);
//                corpManager.autoBindAccountEnterprise2(fsEa, corpId, userList);
//            }
//        }
//    }

    private void saveAppEvent(String outEa, String appId, String infoType, String plainMsg) {
        log.info("QyweixinGatewayInnerServiceImpl.saveAppEvent,outEa={}, appId={}, infoType={}, plainMsg={}", outEa, appId, infoType, plainMsg);

        OaConnectorSyncEventDataDoc dataDoc = new OaConnectorSyncEventDataDoc();
        dataDoc.setId(ObjectId.get());
        dataDoc.setChannel(ChannelEnum.qywx);
        dataDoc.setAppId(appId);
        dataDoc.setOutEa(outEa);
        dataDoc.setEventType(infoType);
        dataDoc.setEvent(plainMsg);
        dataDoc.setStatus(0);
        dataDoc.setCreateTime(System.currentTimeMillis());
        dataDoc.setUpdateTime(System.currentTimeMillis());
        BulkWriteResult bulkWriteResult = oaConnectorSyncEventDataMongoDao.batchReplace(Lists.newArrayList(dataDoc));
        log.info("QyweixinGatewayInnerServiceImpl.saveAppEvent,bulkWriteResult={}", bulkWriteResult);
    }

    /**
     * 代开发应用回调通讯录变更事件
     * @param plainMsg
     */
    private void changeContacts(String outEa, String appId, String plainMsg) {
        Gson gson = new Gson();
        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,plainMsg={}", plainMsg);
        //RepChangeContactXml repChangeContactXml = XmlParser.fromXml(plainMsg, RepChangeContactXml.class);
        RepChangeContactXml repChangeContactXml = XStreamUtils.parseXml(plainMsg, RepChangeContactXml.class);
        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,repChangeContactXml={}", repChangeContactXml);

        com.facishare.open.qywx.accountinner.result.Result<QyweixinUserDetailInfoRsp> userDetailInfoRspResult = null;

        try {
            if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "update_party")) {
                //单独改这个逻辑是因为企微对于部门更新事件有以下几点问题
                //1、父部门在可见范围内，在组织架构，变更子部门的父部门，新的父部门不在可见范围，企微回调事件只有一个当前部门的变更事件，而且还能从企微那里获取到当前部门的数据，无法得知当前部门和当前部门的子部门和相关人员是否在可见范围
                //2、当前部门不在可见范围，在组织架构，变更当前部门的父部门，新的部门不在可见范围，企微回调事件只有当前部门和部门下人员的变更事件，没有当前部门下的子部门变更事件，无法得知当前部门下的子部门是否在可见范围
                saveAppEvent(outEa, appId, repChangeContactXml.getEvent(), plainMsg);
            } else {

                List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
                if(CollectionUtils.isEmpty(enterpriseBindEntities)) {
                    return;
                }

                boolean isHasManual = enterpriseBindEntities.stream()
                        .anyMatch(mapping -> mapping.getBindType().equals(BindTypeEnum.manual));
                if(!isHasManual) {
                    return;
                }

                if(repChangeContactXml!=null && (StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "create_user")
                        || StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "update_user"))) {
                    Long count = outerOaEmployeeDataManager.selectCount(ChannelEnum.qywx, outEa, appId);
                    if(ObjectUtils.isEmpty(count)) {
                        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,init contacts,outEa={}", outEa);
                        saveAppEvent(outEa, appId, repChangeContactXml.getEvent(), plainMsg);
                    } else {
                        userDetailInfoRspResult = qyWeixinManager.getUserInfo(appId, outEa, repChangeContactXml.getUserID());
                        if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                            List<OuterOaEmployeeDataEntity> employeeDataEntities = new LinkedList<>();
                            QyweixinUserDetailInfoRsp rsp = userDetailInfoRspResult.getData();
                            OuterOaEmployeeDataEntity employeeDataEntity = new OuterOaEmployeeDataEntity();
                            employeeDataEntity.setId(IdGenerator.get());
                            employeeDataEntity.setChannel(ChannelEnum.qywx);
                            employeeDataEntity.setOutEa(outEa);
                            employeeDataEntity.setAppId(appId);
                            employeeDataEntity.setOutUserId(rsp.getUserid());
                            employeeDataEntity.setOutUserInfo((JSONObject) JSONObject.toJSON(rsp));
                            employeeDataEntity.setOutDeptId(rsp.getMain_department());
                            employeeDataEntity.setCreateTime(System.currentTimeMillis());
                            employeeDataEntity.setUpdateTime(System.currentTimeMillis());
                            employeeDataEntities.add(employeeDataEntity);
                            Integer updateCount = outerOaEmployeeDataManager.batchUpdateOutUserId(employeeDataEntities);
                            log.info("QyweixinGatewayInnerServiceImpl.changeContacts,user,updateCount={}", updateCount);
                        } else {
                            //更改人员的时候，如果人员不在可见范围，也需要移除
                            if(userDetailInfoRspResult.getCode().equals("60011")) {
                                Integer deleteCount = outerOaEmployeeDataManager.deleteByUserId(ChannelEnum.qywx, outEa, appId, repChangeContactXml.getUserID());
                                log.info("QyweixinGatewayInnerServiceImpl.changeContacts,update user,deleteCount={}", deleteCount);
                            }
                        }
                    }
                }
                if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "delete_user")) {
                    Integer deleteCount = outerOaEmployeeDataManager.deleteByUserId(ChannelEnum.qywx, outEa, appId, repChangeContactXml.getUserID());
                    log.info("QyweixinGatewayInnerServiceImpl.changeContacts,user,deleteCount={}", deleteCount);
                }
            }

            if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "create_party")) {
                Long count = outerOaEmployeeDataManager.selectCount(ChannelEnum.qywx, outEa, appId);
                if(ObjectUtils.isEmpty(count)) {
                    log.info("QyweixinGatewayInnerServiceImpl.changeContacts,init contacts,outEa={}", outEa);
                    saveAppEvent(outEa, appId, repChangeContactXml.getEvent(), plainMsg);
                } else {
                    com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentInfoRsp> departmentInfoResult = qyWeixinManager.getDepartmentInfo(appId, outEa, repChangeContactXml.getId());
                    if(departmentInfoResult.isSuccess() && ObjectUtils.isNotEmpty(departmentInfoResult.getData())) {
                        List<OuterOaDeptDataEntity> deptDataEntities = new LinkedList<>();
                        OuterOaDeptDataEntity deptDataEntity = new OuterOaDeptDataEntity();
                        deptDataEntity.setId(IdGenerator.get());
                        deptDataEntity.setChannel(ChannelEnum.qywx);
                        deptDataEntity.setOutEa(outEa);
                        deptDataEntity.setAppId(appId);
                        deptDataEntity.setOutDeptId(departmentInfoResult.getData().getDepartment().getId());
                        deptDataEntity.setDeptName(departmentInfoResult.getData().getDepartment().getName());
                        deptDataEntity.setDeptOrder(Long.valueOf(departmentInfoResult.getData().getDepartment().getOrder()));
                        deptDataEntity.setParentDeptId(departmentInfoResult.getData().getDepartment().getParentid());
                        deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(departmentInfoResult.getData().getDepartment()));
                        deptDataEntity.setCreateTime(System.currentTimeMillis());
                        deptDataEntity.setUpdateTime(System.currentTimeMillis());
                        deptDataEntities.add(deptDataEntity);
                        Integer updateCount = outerOaDeptDataManager.batchUpdateOutDeptId(deptDataEntities);
                        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,department,updateCount={}", updateCount);
                    }
                }
            }

            if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "delete_party")) {
                Integer deleteCount = outerOaDeptDataManager.deleteByDeptId(ChannelEnum.qywx, outEa, appId, repChangeContactXml.getId());
                log.info("QyweixinGatewayInnerServiceImpl.changeContacts,department,deleteCount={}", deleteCount);
            }
        } catch (Exception e) {
            log.info("QyweixinGatewayInnerServiceImpl.changeContacts,change contacts error,plainMsg={}", plainMsg);
        }

        //区别于其他
        if(repChangeContactXml!=null && StringUtils.equalsIgnoreCase(repChangeContactXml.getChangeType(), "create_user")) {
            //有名称和id，直接绑定
            List<OuterOaEnterpriseBindEntity> enterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
            if (CollectionUtils.isEmpty(enterpriseBindEntities)) {
                return;
            }
            for (OuterOaEnterpriseBindEntity enterpriseBindEntity : enterpriseBindEntities) {
                String fsEa = enterpriseBindEntity.getFsEa();
                Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
                }.getType());
                if(autoBindEmpEnterpriseMap.containsKey(fsEa)) {
                    List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = new LinkedList<>();
                    if(ObjectUtils.isEmpty(userDetailInfoRspResult)) {
                        userDetailInfoRspResult = qyWeixinManager.getUserInfo(ConfigCenter.repAppId, outEa, repChangeContactXml.getUserID());
                    }
                    if(userDetailInfoRspResult.isSuccess() && ObjectUtils.isNotEmpty(userDetailInfoRspResult.getData())) {
                        allEmployeeListVisible.add(userDetailInfoRspResult.getData());
                        log.info("QyweixinGatewayInnerServiceImpl.changeContacts,fsEa={},allEmployeeListVisible={}", fsEa, allEmployeeListVisible);
                        corpManager.autoBindEmpAccount(enterpriseBindEntity, allEmployeeListVisible);
                    }
                } else if(ConfigCenter.AUTO_BIND_ACCOUNT_EA.contains(fsEa)) {
                    List<QyweixinUserDetailInfoRsp> allEmployeeListVisible = new LinkedList<>();
                    QyweixinUserDetailInfoRsp userDetailInfoRsp = QyweixinUserDetailInfoRsp.builder()
                            .name(repChangeContactXml.getName())
                            .userid(repChangeContactXml.getUserID())
                            .corpid(repChangeContactXml.getToUserName())
                            .build();
                    allEmployeeListVisible.add(userDetailInfoRsp);
                    log.info("QyweixinGatewayInnerServiceImpl.changeContacts,fsEa={},allEmployeeListVisible1={}", fsEa, allEmployeeListVisible);
                    corpManager.autoBindAccountEnterprise2(enterpriseBindEntity, allEmployeeListVisible);
                }
            }
        }
    }


//    @Override
//    public Result<Void> syncOuterContacts(OuterOaEnterpriseBindEntity enterpriseBindEntity, EnterpriseManualAccountSyncInfo accountSyncInfo, OutEmpModel outEmpModel) {
//        if (enterpriseBindEntity.getBindType().equals(BindTypeEnum.manual) && ObjectUtils.isEmpty(accountSyncInfo)) {
//            OuterOaConfigInfoEntity configInfo = outerOaConfigInfoManager.queryConnectorConfigInfoByDcId(enterpriseBindEntity.getId(), OuterOaConfigInfoTypeEnum.MANUAL_ACCOUNT_SYNC);
//            if (ObjectUtils.isEmpty(configInfo)) {
//                return Result.newInstance(ErrorRefer.QUERRY_EMPTY);
//            }
//            accountSyncInfo = JSON.parseObject(configInfo.getConfigInfo(), EnterpriseManualAccountSyncInfo.class);
//        }
//
//        //这个不需要往下走了
//        if (enterpriseBindEntity.getBindType().equals(BindTypeEnum.manual)
//                && accountSyncInfo.getAccountSyncType().equals(EnterpriseConfigAccountSyncTypeEnum.accountBind)
//                && accountSyncInfo.getAccountBindInfo().getAccountBindType().equals(BindTypeEnum.manual)) {
//            return new Result<>();
//        }
//
//        //自动账号绑定
//        if (enterpriseBindEntity.getBindType().equals(BindTypeEnum.manual)
//                && accountSyncInfo.getAccountSyncType().equals(EnterpriseConfigAccountSyncTypeEnum.accountBind)
//                && accountSyncInfo.getAccountBindInfo().getAccountBindType().equals(BindTypeEnum.auto)) {
//
//            return new Result<>();
//        }

        //同步组织架构

//        return null;
//    }

    //组织架构变更
    //可见范围变更
    //针对外部人员和部门相关事件
    @Override
    public Result<Void> syncOuterContacts(String outEa, String appId, String eventType, String plainMsg) {
        if (eventType.equals("change_auth")) {
            //保存可见范围
            QyweixinAppVisibleModel qyweixinAppVisibleModel = saveChangeAuthEvent(outEa, appId);
            //可见范围同步逻辑
            syncChangeAuthEvent(outEa, appId, qyweixinAppVisibleModel);
        } else if (eventType.equals("change_contact")) {
            syncChangeContactEvent(outEa, appId, plainMsg);
        }
        return new Result<>();
    }

    @Override
    public Result<Void> syncOuterContactsInfo(String outEa, String appId) {
        saveChangeAuthEvent(outEa, appId);
        return new Result<>();
    }

    private QyweixinAppVisibleModel saveChangeAuthEvent(String outEa, String appId) {
        //通过outEa和appId找到dcId
        List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
        if (ObjectUtils.isEmpty(outerOaEnterpriseBindEntities)) {
            return new QyweixinAppVisibleModel();
        }
        String dcId = outerOaEnterpriseBindEntities.get(0).getId();
        //处理特殊逻辑，存在有crmAppId和和repAppId的，我们就再调一次repAppId的数据，存库，替换，这样比起循环调快很多
        OuterOaAppInfoEntity repAppInfoEntity = outerOaAppInfoManager.getEntity(ChannelEnum.qywx, outEa, ConfigCenter.repAppId);
        QyweixinAppVisibleModel qyweixinRepAppVisibleModel = null;
        if (!appId.equals(ConfigCenter.repAppId) && ObjectUtils.isNotEmpty(repAppInfoEntity)) {
            qyweixinRepAppVisibleModel = new QyweixinAppVisibleModel();
            //不存储rep的数据，直接查询
            com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentListRsp> departmentInfoListResult = qyWeixinManager.getDepartmentInfoList(ConfigCenter.repAppId, outEa, null);
            if (departmentInfoListResult.isSuccess()) {
                qyweixinRepAppVisibleModel.setQyweixinDepartmentRsps(departmentInfoListResult.getData().getDepartment());
            }
            Result<List<QyweixinUserDetailInfoRsp>> allEmployeeLisResult = contactBindInnerService.getAllEmployeeList(outEa, ConfigCenter.repAppId, "1");
            if (allEmployeeLisResult.isSuccess()) {
                qyweixinRepAppVisibleModel.setQyweixinUserDetailInfoRsps(allEmployeeLisResult.getData());
            }
        }

        QyweixinAppVisibleModel qyweixinAppVisibleModel = new QyweixinAppVisibleModel();
        //查可见范围，同步到data表
        com.facishare.open.qywx.accountinner.result.Result<QyweixinDepartmentListRsp> departmentInfoListResult = qyWeixinManager.getDepartmentInfoList(appId, outEa, null);
        if(departmentInfoListResult.isSuccess()) {
            List<OuterOaDeptDataEntity> deptDataEntities = new LinkedList<>();
            if (ObjectUtils.isNotEmpty(departmentInfoListResult.getData()) && CollectionUtils.isNotEmpty(departmentInfoListResult.getData().getDepartment())) {
                qyweixinAppVisibleModel.setQyweixinDepartmentRsps(departmentInfoListResult.getData().getDepartment());
                Map<String, QyweixinDepartmentRsp> departmentMap = null;
                if (qyweixinRepAppVisibleModel != null) {
                    departmentMap = CollectionUtils.isNotEmpty(qyweixinRepAppVisibleModel.getQyweixinDepartmentRsps()) ? qyweixinRepAppVisibleModel.getQyweixinDepartmentRsps().stream()
                            .collect(Collectors.toMap(
                                    QyweixinDepartmentRsp::getId, // Key: id
                                    department -> department,     // Value: QyweixinDepartmentRsp 对象
                                    (existing, replacement) -> existing // 去重规则：保留第一个出现的对象
                            )) : new HashMap<>();
                }
                for (QyweixinDepartmentRsp rsp : departmentInfoListResult.getData().getDepartment()) {
                    if (departmentMap != null && departmentMap.containsKey(rsp.getId()) && StringUtils.isNotEmpty(departmentMap.get(rsp.getId()).getName()) && !departmentMap.get(rsp.getId()).getName().equals(rsp.getName())) {
                        rsp.setName(departmentMap.get(rsp.getId()).getName());
                    }
//                    QywxDeptObject qywxDeptObject = new QywxDeptObject();
                    //深拷贝
//                    BeanUtils.copyProperties(rsp, qywxDeptObject);
                    OuterOaDeptDataEntity deptDataEntity = new OuterOaDeptDataEntity();
                    deptDataEntity.setId(IdGenerator.get());
                    deptDataEntity.setChannel(ChannelEnum.qywx);
                    deptDataEntity.setOutEa(outEa);
                    deptDataEntity.setAppId(appId);
                    deptDataEntity.setOutDeptId(rsp.getId());
                    deptDataEntity.setDeptName(rsp.getName());
                    deptDataEntity.setDeptOrder(Long.valueOf(rsp.getOrder()));
                    deptDataEntity.setParentDeptId(rsp.getParentid());
                    //TODO
//                    deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(qywxDeptObject));
                    deptDataEntity.setOutDeptInfo((JSONObject) JSONObject.toJSON(rsp));
                    deptDataEntity.setCreateTime(System.currentTimeMillis());
                    deptDataEntity.setUpdateTime(System.currentTimeMillis());
                    deptDataEntities.add(deptDataEntity);
                }
                Integer updateCount = outerOaDeptDataManager.batchUpdateOutDeptId(deptDataEntities);
                log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,department,updateCount={}", updateCount);
                Set<String> outDeptList = deptDataEntities.stream().map(OuterOaDeptDataEntity::getOutDeptId).collect(Collectors.toSet());
                Integer deleteCount = outerOaDeptDataManager.deleteInvisibleDepts(outEa, ChannelEnum.qywx, appId, outDeptList);
                log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,department,deleteCount={}", deleteCount);
            }
        }

        //获取人员详情，查询范围默认为1
        Result<List<QyweixinUserDetailInfoRsp>> allEmployeeLisResult = contactBindInnerService.getAllEmployeeList(outEa, appId, "1");
        if(allEmployeeLisResult.isSuccess()) {
            List<OuterOaEmployeeDataEntity> employeeDataEntities = new LinkedList<>();
            List<QywxEmployeeObject> qywxEmployeeObjects = new LinkedList<>();
            if(CollectionUtils.isNotEmpty(allEmployeeLisResult.getData())) {
                qyweixinAppVisibleModel.setQyweixinUserDetailInfoRsps(allEmployeeLisResult.getData());
                Map<String, QyweixinUserDetailInfoRsp> employeeMap = null;
                if (qyweixinRepAppVisibleModel != null) {
                    employeeMap = CollectionUtils.isNotEmpty(qyweixinRepAppVisibleModel.getQyweixinUserDetailInfoRsps()) ? qyweixinRepAppVisibleModel.getQyweixinUserDetailInfoRsps().stream()
                            .collect(Collectors.toMap(
                                    QyweixinUserDetailInfoRsp::getUserid, // Key: id
                                    employee -> employee,     // Value: QyweixinDepartmentRsp 对象
                                    (existing, replacement) -> existing // 去重规则：保留第一个出现的对象
                            )) : new HashMap<>();
                }
                for(QyweixinUserDetailInfoRsp rsp : allEmployeeLisResult.getData()) {
                    if(employeeMap != null && employeeMap.containsKey(rsp.getUserid()) && !employeeMap.get(rsp.getUserid()).getName().equals(rsp.getName())) {
                        rsp.setName(employeeMap.get(rsp.getUserid()).getName());
                    }
                    QywxEmployeeObject qywxEmployeeObject = new QywxEmployeeObject();
                    //深拷贝
                    BeanUtils.copyProperties(rsp, qywxEmployeeObject);
                    qywxEmployeeObjects.add(qywxEmployeeObject);
                    OuterOaEmployeeDataEntity employeeDataEntity = new OuterOaEmployeeDataEntity();
                    employeeDataEntity.setId(IdGenerator.get());
                    employeeDataEntity.setChannel(ChannelEnum.qywx);
                    employeeDataEntity.setOutEa(outEa);
                    employeeDataEntity.setAppId(appId);
                    employeeDataEntity.setOutUserId(rsp.getUserid());
                    employeeDataEntity.setOutUserInfo((JSONObject) JSONObject.toJSON(qywxEmployeeObject));
                    employeeDataEntity.setOutDeptId(rsp.getMain_department());
                    employeeDataEntity.setCreateTime(System.currentTimeMillis());
                    employeeDataEntity.setUpdateTime(System.currentTimeMillis());
                    employeeDataEntities.add(employeeDataEntity);
                }
                qyweixinAppVisibleModel.setQywxEmployeeObjects(qywxEmployeeObjects);
                Integer updateCount = outerOaEmployeeDataManager.batchUpsert(qywxEmployeeObjects, ChannelEnum.qywx, dcId);
                log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,updateCount={}", updateCount);
            }

            Set<String> outEmpList = employeeDataEntities.stream().map(OuterOaEmployeeDataEntity::getOutUserId).collect(Collectors.toSet());
            Integer deleteCount = outerOaEmployeeDataManager.deleteInvisibleUsers(outEa, ChannelEnum.qywx, appId, outEmpList);
            log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,user,deleteCount={}", deleteCount);
        }
        return qyweixinAppVisibleModel;
    }

    private void repSyncChangeAuthEvent(String outEa, String appId) {
        //历史企业兼容逻辑
        Map<String, Map<String, String>> autoBindEmpEnterpriseMap = new Gson().fromJson(ConfigCenter.EXTATTR_AUTO_BIND_EMP_ENTERPRISE, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());
        List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, null);
        if (CollectionUtils.isEmpty(outerOaEnterpriseBindEntities)) {
            return;
        }
        Result<List<QyweixinUserDetailInfoRsp>> allEmployeeLisResult = contactBindInnerService.getAllEmployeeList(outEa, appId, "1");
        if(!allEmployeeLisResult.isSuccess()) {
            log.info("QyweixinAccountSyncServiceImpl.saveOrUpdateUserAndDepartmentInfo,getAllEmployeeList error,outEa={},appId={}", outEa, appId);
            return;
        }
        for (OuterOaEnterpriseBindEntity enterpriseBindEntity : outerOaEnterpriseBindEntities) {
            if (autoBindEmpEnterpriseMap.containsKey(enterpriseBindEntity.getFsEa())) {
                log.info("QyweixinAccountSyncServiceImpl.updateTag,autoBindEmpEnterpriseMap,fsEa={}",enterpriseBindEntity.getFsEa());
                corpManager.autoBindEmpAccount(enterpriseBindEntity, allEmployeeLisResult.getData());
            }
        }
    }

    private void syncChangeAuthEvent(String outEa, String appId, QyweixinAppVisibleModel qyweixinAppVisibleModel) {
        List<OuterOaEnterpriseBindEntity> outerOaEnterpriseBindEntities = outerOaEnterpriseBindManager.getEntitiesByOutEa(ChannelEnum.qywx, outEa, appId);
        if(CollectionUtils.isEmpty(outerOaEnterpriseBindEntities)) {
            log.info("QyweixinAccountSyncServiceImpl.syncChangeAuthEvent,outerOaEnterpriseBindEntities is empty,outEa={},appId={}", outEa, appId);
            if (appId.equals(ConfigCenter.repAppId)) {
                //以前有些靠代开发触发的事件逻辑
                repSyncChangeAuthEvent(outEa, appId);
            }
            return;
        }
        for(OuterOaEnterpriseBindEntity enterpriseBindEntity : outerOaEnterpriseBindEntities) {
            log.info("QyweixinAccountSyncServiceImpl.syncChangeAuthEvent,enterpriseBindEntity={}", enterpriseBindEntity);
            String fsEa = enterpriseBindEntity.getFsEa();
            String dcId = enterpriseBindEntity.getId();


            //看配置是否要同步
            OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.SETTING_BIND_RULES, dcId);
            SettingAccountRulesModel settingAccountRulesModel = JSON.parseObject(configInfoEntity.getConfigInfo(), SettingAccountRulesModel.class);
            if (settingAccountRulesModel.getSyncTypeEnum() == EnterpriseConfigAccountSyncTypeEnum.accountSync) {
                //同步组织架构和账号绑定
                onAvailableRangeChangedLimit(enterpriseBindEntity);
            } else {
                //反绑定逻辑
                if (settingAccountRulesModel.getBindTypeEnum() == BindTypeEnum.auto) {
                    //自动绑定
                    autoBindEmployee(enterpriseBindEntity, qyweixinAppVisibleModel);
                } else {
                    //手动绑定：不做任何处理
                    log.info("QyweixinAccountSyncServiceImpl.syncChangeAuthEvent,manual bind,do nothing,outEa={},appId={}", outEa, appId);
                }
                //这里需要做停用人员的逻辑
                deleteAccountBindUser(enterpriseBindEntity, RemoveEmployeeEventType.REMOVE_RANGE);
            }
        }
    }

    private void autoBindEmployee(OuterOaEnterpriseBindEntity enterpriseBindEntity, QyweixinAppVisibleModel qyweixinAppVisibleModel) {
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();
        String dcId = enterpriseBindEntity.getId();
        //查看是否设置了映射规则
        OuterOaConfigInfoEntity configInfoEntity = outerOaConfigInfoManager.getEntityByDataCenterId(OuterOaConfigInfoTypeEnum.EMPLOYEE_UNIQUE_IDENTITY, dcId);
        SystemFieldMappingResult systemFieldMappingResult = JSON.parseObject(configInfoEntity.getConfigInfo(), SystemFieldMappingResult.class);
        if (ObjectUtils.isEmpty(systemFieldMappingResult) || CollectionUtils.isEmpty(systemFieldMappingResult.getItemFieldMappings())) {
            log.info("ContactsServiceImpl.autoBindEmployee,systemFieldMappingResult is empty,dcId={}", dcId);
            return;
        }

        List<SystemFieldMappingResult.ItemFieldMapping> itemFieldMappings = systemFieldMappingResult.getItemFieldMappings();
        //设置了一个字段，用来判断是否是处理自动绑定的唯一规则
        itemFieldMappings = itemFieldMappings.stream().filter(SystemFieldMappingResult.ItemFieldMapping::getMatchUnique).collect(Collectors.toList());
        log.info("ContactsServiceImpl.autoBindEmployee,itemFieldMappings={}", JSON.toJSONString(itemFieldMappings));
        
        // 获取企业微信员工列表
        List<QywxEmployeeObject> qywxEmployeeObjects = qyweixinAppVisibleModel.getQywxEmployeeObjects();
        if (CollectionUtils.isEmpty(qywxEmployeeObjects)) {
            log.info("ContactsServiceImpl.autoBindEmployee,qywxEmployeeObjects is empty,dcId={}", dcId);
            return;
        }
        
        // 获取纷享销客员工列表
        int ei = eieaConverter.enterpriseAccountToId(fsEa);
        com.facishare.open.order.contacts.proxy.api.result.Result<List<ObjectData>> fsEmployeeResult = 
                fsEmployeeServiceProxy.listAll(ei, GlobalValue.ALL_COMPANY_DEPARTMENT_ID+"");
        if (!fsEmployeeResult.isSuccess() || CollectionUtils.isEmpty(fsEmployeeResult.getData())) {
            log.info("ContactsServiceImpl.autoBindEmployee,fsEmployeeResult is empty,ei={}", ei);
            return;
        }
        List<ObjectData> fsEmployees = fsEmployeeResult.getData();
        
        // 已绑定的员工ID列表，避免重复绑定
        List<OuterOaEmployeeBindEntity> existBinds = outerOaEmployeeBindManager.getEntitiesByNotPage(
                OuterOaEmployeeBindParams.builder()
                        .channel(ChannelEnum.qywx)
                        .outEa(outEa)
                        .fsEa(fsEa)
                        .appId(appId)
                        .build());
        Set<String> boundFsEmpIds = new HashSet<>();
        Set<String> boundOutEmpIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(existBinds)) {
            boundFsEmpIds = existBinds.stream()
                    .map(OuterOaEmployeeBindEntity::getFsEmpId)
                    .collect(Collectors.toSet());
            boundOutEmpIds = existBinds.stream()
                    .map(OuterOaEmployeeBindEntity::getOutEmpId)
                    .collect(Collectors.toSet());
        }
        
        // 开始匹配并绑定
        List<OuterOaEmployeeBindEntity> newBinds = new ArrayList<>();
        for (QywxEmployeeObject qywxEmployee : qywxEmployeeObjects) {
            // 已绑定的员工跳过
            if (boundOutEmpIds.contains(qywxEmployee.getUserid())) {
                continue;
            }
            
            for (ObjectData fsEmployee : fsEmployees) {
                // 已绑定的员工跳过
                if (boundFsEmpIds.contains(fsEmployee.getId())) {
                    continue;
                }
                
                // 根据映射规则匹配员工
                boolean isMatch = true;
                for (SystemFieldMappingResult.ItemFieldMapping mapping : itemFieldMappings) {
                    String outerFieldName = mapping.getOuterOAFieldApiName();
                    String crmFieldName = mapping.getCrmFieldApiName();
                    
                    // 获取企业微信字段值
                    Object outerValue = getFieldValueFromQywxEmployee(qywxEmployee, outerFieldName);
                    // 获取纷享销客字段值
                    Object crmValue = fsEmployee.get(crmFieldName);
                    
                    // 如果任一字段值为空，则跳过此字段比较
                    if (outerValue == null || crmValue == null) {
                        isMatch = false;
                        continue;
                    }
                    
                    // 比较字段值是否匹配
                    if (!outerValue.toString().equals(crmValue.toString())) {
                        isMatch = false;
                        break;
                    }
                }
                
                // 如果所有字段都匹配，则创建绑定关系
                if (isMatch) {
                    OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
                    bindEntity.setId(IdGenerator.get());
                    bindEntity.setChannel(ChannelEnum.qywx);
                    bindEntity.setFsEa(fsEa);
                    bindEntity.setOutEa(outEa);
                    bindEntity.setAppId(appId);
                    bindEntity.setDcId(dcId);
                    bindEntity.setFsEmpId(fsEmployee.getId());
                    bindEntity.setOutEmpId(qywxEmployee.getUserid());
                    bindEntity.setBindStatus(BindStatusEnum.normal);
                    bindEntity.setCreateTime(System.currentTimeMillis());
                    bindEntity.setUpdateTime(System.currentTimeMillis());
                    
                    newBinds.add(bindEntity);
                    
                    // 更新已绑定集合，避免一个纷享员工绑定多个企业微信员工
                    boundFsEmpIds.add(fsEmployee.getId());
                    boundOutEmpIds.add(qywxEmployee.getUserid());
                    
                    log.info("ContactsServiceImpl.autoBindEmployee,match success,fsEmpId={},outEmpId={}", 
                            fsEmployee.getId(), qywxEmployee.getUserid());
                    break;
                }
            }
        }
        
        // 批量保存绑定关系
        if (CollectionUtils.isNotEmpty(newBinds)) {
            Integer count = outerOaEmployeeBindManager.batchUpsert(newBinds);
            log.info("ContactsServiceImpl.autoBindEmployee,batchInsert count={}", count);

            eventBindSendMqManager.employeeBindChangeEvent(enterpriseBindEntity.getId(), newBinds.stream().map(OuterOaEmployeeBindEntity::getId).collect(Collectors.toList()), EmplyeeBindChangeTypeEnum.employee_bind);
        }
    }

    /**
     * 根据字段名从企业微信员工对象中获取字段值，使用反射实现
     * @param qywxEmployee 企业微信员工对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Object getFieldValueFromQywxEmployee(QywxEmployeeObject qywxEmployee, String fieldName) {
        try {
//            // 处理扩展属性特殊情况
//            if (fieldName.startsWith("extattr.")) {
//                String attrName = fieldName.substring("extattr.".length());
//                if (qywxEmployee.getExtattr() != null && qywxEmployee.getExtattr().getAttrs() != null) {
//                    for (QywxEmployeeObject.AttrsInfo attr : qywxEmployee.getExtattr().getAttrs()) {
//                        if (attr.getName().equals(attrName)) {
//                            return attr.getValue();
//                        }
//                    }
//                }
//                return null;
//            }
            
            // 处理嵌套属性
            if (fieldName.contains(".")) {
                String[] fieldParts = fieldName.split("\\.");
                Object currentObj = qywxEmployee;
                
                for (int i = 0; i < fieldParts.length; i++) {
                    if (currentObj == null) {
                        return null;
                    }
                    
                    String currentField = fieldParts[i];
                    Class<?> currentClass = currentObj.getClass();
                    
                    try {
                        Field field = currentClass.getDeclaredField(currentField);
                        field.setAccessible(true);
                        currentObj = field.get(currentObj);
                    } catch (NoSuchFieldException e) {
                        // 尝试使用getter方法
                        String getterMethodName = "get" + currentField.substring(0, 1).toUpperCase() + currentField.substring(1);
                        try {
                            Method getterMethod = currentClass.getMethod(getterMethodName);
                            currentObj = getterMethod.invoke(currentObj);
                        } catch (NoSuchMethodException ex) {
                            log.error("ContactsServiceImpl.getFieldValueFromQywxEmployee: No field or getter found for {}", currentField);
                            return null;
                        }
                    }
                }
                
                return currentObj;
            }
            
            // 处理普通属性
            try {
                Field field = qywxEmployee.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                return field.get(qywxEmployee);
            } catch (NoSuchFieldException e) {
                // 尝试使用getter方法
                String getterMethodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                try {
                    Method getterMethod = qywxEmployee.getClass().getMethod(getterMethodName);
                    return getterMethod.invoke(qywxEmployee);
                } catch (NoSuchMethodException ex) {
                    // 尝试is开头的getter（用于boolean类型）
                    getterMethodName = "is" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                    try {
                        Method isMethod = qywxEmployee.getClass().getMethod(getterMethodName);
                        return isMethod.invoke(qywxEmployee);
                    } catch (NoSuchMethodException exc) {
                        log.error("ContactsServiceImpl.getFieldValueFromQywxEmployee: No field or getter found for {}", fieldName);
                        return null;
                    }
                }
            }
        } catch (Exception e) {
            log.error("ContactsServiceImpl.getFieldValueFromQywxEmployee error,fieldName={},error={}", 
                    fieldName, e.getMessage(), e);
            return null;
        }
    }

    private void deleteAccountBindUser(OuterOaEnterpriseBindEntity enterpriseBindEntity, RemoveEmployeeEventType removeEmployeeEventType) {
        log.info("ContactsServiceImpl.deleteAccountBindUser,enterpriseBindEntity={},removeEmployeeEventType={}",
                enterpriseBindEntity, removeEmployeeEventType);
        String fsEa = enterpriseBindEntity.getFsEa();
        String outEa = enterpriseBindEntity.getOutEa();
        String appId = enterpriseBindEntity.getAppId();

        //查询已绑定的人员
        List<OuterOaEmployeeBindEntity> existBinds = outerOaEmployeeBindManager.getEntitiesByNotPage(
                OuterOaEmployeeBindParams.builder()
                        .channel(ChannelEnum.qywx)
                        .outEa(outEa)
                        .fsEa(fsEa)
                        .appId(appId)
                        .build());

        if (CollectionUtils.isEmpty(existBinds)) {
            return;
        }

        //用已绑定的人员查询绑定信息表，如果不存在，就需要处理
        List<OuterOaEmployeeDataEntity> employeeDataManagerEntities = outerOaEmployeeDataManager.getEntities(OuterOaEmployeeDataParams.builder().channel(ChannelEnum.qywx).outEa(outEa).appId(appId).build());
        //转成map
        Map<String, OuterOaEmployeeDataEntity> employeeDataManagerEntityMap = employeeDataManagerEntities.stream()
                .collect(Collectors.toMap(OuterOaEmployeeDataEntity::getOutUserId, Function.identity()));

        for (OuterOaEmployeeBindEntity existBind : existBinds) {
            log.info("ContactsServiceImpl.deleteAccountBindUser,outEa={},appId={},existBind={}", outEa, appId, existBind);
            if (employeeDataManagerEntityMap.containsKey(existBind.getOutEmpId())) {
                continue;
            }

            //停用
            com.facishare.open.outer.oa.connector.common.api.result.Result<Void> removeEmpData = objectDataManager.removeEmpData(enterpriseBindEntity,
                    existBind.getOutEmpId(),
                    removeEmployeeEventType);
            log.info("ContactsServiceImpl.deleteAccountBindUser,outEa={},appId={},existBind={},removeEmpData={}", outEa, appId, existBind, removeEmpData);
        }
    }

    private void syncChangeContactEvent(String outEa, String appId, String plainMsg) {
        ContactsXml contactsInfo = XStreamUtils.parseXml(plainMsg, ContactsXml.class);
        String changeType = contactsInfo.getChangeType();
        log.info("ContactsServiceImpl.syncChangeContactEvent,outEa={},appId={},changeType={}", outEa, appId, changeType);
        //通讯录变化
        if("create_user".contains(changeType) || "update_user".equals(changeType) || "delete_user".equals(changeType)) {
            corpManager.sendChangeContactsMsg(outEa, appId, contactsInfo, changeType);
        }

        //逻辑处理
        onChangeContacts(contactsInfo, appId);
    }

    // 保存企业绑定关系，并且做一次初始化操作
    @Override
    public Result<Void> syncOuterAppInitInfo(String outEa, String appId, String fsEa, QyweixinGetPermenantCodeRsp corpAuthInfo) {
        Integer ei = eieaConverter.enterpriseAccountToId(fsEa);
        String outUserId = corpAuthInfo.getAuth_user_info().getUserid();
        String outUserName = "U-FSQYWX-" + corpAuthInfo.getAuth_user_info().getName();
        //绑定企业
        corpManager.enterpriseBind(outEa, fsEa, appId, BindStatusEnum.normal);

        //绑定人员，给管理员权限
        List<OuterOaEmployeeBindEntity> employeeBindEntities = outerOaEmployeeBindManager.getEntitiesByNotPage(OuterOaEmployeeBindParams.builder().channel(ChannelEnum.qywx).outEa(outEa).fsEa(fsEa).outEmpId(outUserId).build());
        if(CollectionUtils.isEmpty(employeeBindEntities)) {
            //创建员工
            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .name(outUserName)
                    .fullName(outUserName)
                    .status("0")
                    .isActive(true)
                    .build();
            log.info("ContactsServiceImpl.syncOuterAppInitInfo,arg={}", arg);
            com.facishare.open.order.contacts.proxy.api.result.Result<ObjectData> result = fsEmployeeServiceProxy.create(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode(), FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode()),
                    FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
            log.info("ContactsServiceImpl.syncOuterAppInitInfo,result={}", result);
            if(result.isSuccess()) {
                OuterOaEmployeeBindEntity entity = OuterOaEmployeeBindEntity.builder()
                        .id(IdGenerator.get())
                        .channel(ChannelEnum.qywx)
                        .outEa(outEa)
                        .fsEa(fsEa)
                        .appId(appId)
                        .outEmpId(outUserId)
                        .fsEmpId(result.getData().getId())
                        .bindStatus(BindStatusEnum.normal)
                        .build();
                Integer count = outerOaEmployeeBindManager.insert(entity);
                log.info("ContactsServiceImpl.syncOuterAppInitInfo,insert employee bind entity,count={}", count);
            }
        } else {
            FsEmpArg arg = FsEmpArg.builder()
                    .ei(ei + "")
                    .id(employeeBindEntities.get(0).getFsEmpId())
                    .status("0")
                    .build();
            //更新纷享员工负责人
            com.facishare.open.order.contacts.proxy.api.result.Result<Void> updateResult = fsEmployeeServiceProxy.update(arg,
                    Lists.newArrayList(FsEmployeeRoleCodeEnum.SALES.getRoleCode(), FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode()),
                    FsEmployeeRoleCodeEnum.CRM_MANAGER.getRoleCode());
            LogUtils.info("ContactsServiceImpl.syncOuterAppInitInfo,update employee leader,updateResult={}", updateResult);
        }

        //下一个订单
        Result<QyweixinEnterpriseOrder> result = corpManager.initCorpEvent(corpAuthInfo, appId, null);
        log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,initCorpEvent.result={}",result);
        Result<Void> crmOrder = orderService.createCrmOrder(fsEa, result.getData());
        log.info("QyweixinOpenEnterpriseHandlerTemplate.getAuthInfoDoInitCorp,createCrmOrder.result={}",crmOrder);

        //初始化可见范围
        syncOuterContacts(outEa, appId, "change_auth", null);
        return null;
    }
}
