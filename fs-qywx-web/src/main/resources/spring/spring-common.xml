<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:p="http://www.springframework.org/schema/p" xmlns:task="http://www.springframework.org/schema/task"
       xmlns:c="http://www.springframework.org/schema/c"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://code.alibabatech.com/schema/dubbo http://code.alibabatech.com/schema/dubbo/dubbo.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task.xsd">

    <!-- 消费方应用名，用于计算依赖关系，不是匹配条件，不要与提供方一样 -->
    <dubbo:application id="fsOpenQywxApplication" name="fs-qywx-web" />
    <!-- 使用multicast广播注册中心暴露发现服务地址 -->
    <dubbo:registry id="fsOpenQywxRegistry" address="${dubbo.registry.address}" file="${dubbo.registry.file}" />
    <dubbo:protocol id="dubbo" name="dubbo" port="${duboo.port}"  />
    <dubbo:consumer id="fsOpenQywxConsumer"   registry="fsOpenQywxRegistry"
                    init="false"    check="false" timeout="300000"
                    retries="2"   filter="tracerpc" />

    <dubbo:service interface="com.facishare.open.qywx.accountbind.service.QyweixinAccountBindService"
                     ref="qyweixinAccountBindService"
                     timeout="20000"
                     protocol="dubbo"
                     version="1.0"
                     group="${oaDubboConfigGroup}"
                     />
    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.QyweixinAccountBindInnerService"
                     ref="qyweixinAccountBindInnerService"
                     timeout="20000"
                     protocol="dubbo"
                     version="1.0"
                     group="${oaDubboConfigGroup}"
                     />

<!-- yunzhijia数据源 -->
    <bean id="fsOpenQywxDB" class="com.github.mybatis.spring.DynamicDataSource">
        <property name="configName" value="fs-open-qywx-db"/>
    </bean>
    <!-- define the SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="fsOpenQywxDB"/>
        <property name="typeAliasesPackage" value="com.facishare.open.qywx.accountsync.model.qyweixin.bo,com.facishare.open.qywx.accountbind.model,com.facishare.open.qywx.web.entity.entity"/>
        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>
    </bean>
    <!-- scan for mapper and let them be autowired -->
    <bean id="dbConfig" class="com.github.mybatis.spring.ScannerConfigurer">
        <property name="basePackage" value="com.facishare.open.qywx.web.db.dao"/>
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

<!--    &lt;!&ndash; 日志上报数据源 &ndash;&gt;-->
<!--    <bean id="fsClickHouseDB" class="com.github.mybatis.spring.DynamicDataSource">-->
<!--        <property name="configName" value="eye-clickhouse-db"/>-->
<!--    </bean>-->
<!--    &lt;!&ndash; define the SqlSessionFactory &ndash;&gt;-->
<!--    <bean id="clickHouseSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">-->
<!--        <property name="dataSource" ref="fsClickHouseDB"/>-->
<!--        <property name="typeAliasesPackage" value="com.facishare.open.qywx.web.entity.chEntity"/>-->
<!--        <property name="configLocation" value="classpath:spring/mybatis-config.xml"/>-->
<!--    </bean>-->
<!--    &lt;!&ndash; scan for mapper and let them be autowired &ndash;&gt;-->
<!--    <bean id="clickHouseDbConfig" class="com.github.mybatis.spring.ScannerConfigurer">-->
<!--        <property name="basePackage" value="com.facishare.open.qywx.web.db.chDao"/>-->
<!--        <property name="sqlSessionFactoryBeanName" value="clickHouseSqlSessionFactory"/>-->
<!--    </bean>-->

    <import resource="classpath:fs-paas-dao-support.xml"/>
    <import resource="classpath:META-INF/fs-spring-rest-plugin.xml"/>
    <import resource="classpath:/spring/spring-job.xml"/>
    <import resource="classpath:otherrest/otherrest.xml"/>

    <!--fs-orgainzation-adapter-api -->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-adapter-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-adapter-api-dubbo-rest-host-config.xml"/>

    <!--fs-orgainzation-api -->
    <import resource="classpath:spring/fs-organization-api-dubbo-rest-client.xml"/>
    <!--默认fs-organization-provider服务地址，支持client方指定provider指定地址-->
    <import resource="classpath:spring/fs-organization-api-dubbo-rest-client-host-config.xml"/>


    <import resource="classpath:spring/license-client.xml"/>

    <!-- 纷享内部调用组织架构服务 非元数据团队建议使用该配置 -->
    <import resource="classpath:spring/fs-organization-api-rest-client.xml"/>

    <import resource="classpath:spring/spring-order-contacts-common.xml"/>

    <dubbo:service interface="com.facishare.open.qywx.accountsync.service.QyweixinAccountSyncService"
                   ref="qyweixinAccountSyncService"
                   protocol="dubbo"
                   timeout="300000"
                   version="2.0"
                   group="${oaDubboConfigGroup}"
                   />
    <dubbo:service interface="com.facishare.open.qywx.accountsync.service.SuperAdminService"
                   ref="superAdminServiceImpl"
                   protocol="dubbo"
                   timeout="300000"
                   version="2.0"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service  interface="com.facishare.open.qywx.accountinner.service.QyweixinGatewayInnerService"
                    ref="qyweixinGatewayInnerService"
                    protocol="dubbo"
                    timeout="300000"
                    version="1.0"
                    retries="0"
                    group="${oaDubboConfigGroup}"
                    />

    <dubbo:service   interface="com.facishare.open.qywx.accountinner.service.ContactBindInnerService"
                     ref="contactBindInnerService"
                     protocol="dubbo"
                     timeout="300000"
                     version="1.0"
                     retries="0"
                     group="${oaDubboConfigGroup}"
                     />

    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.ToolsService"
                   ref="toolsService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service interface="com.facishare.open.qywx.accountsync.service.FileUploadService"
                   ref="fileUploadService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.ExternalContactsService"
                   ref="externalContactsService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service interface="com.facishare.open.qywx.accountinner.service.NotificationService"
                   ref="notificationService"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:reference id="authService" interface="com.facishare.open.oauth.service.AuthService" protocol="dubbo" timeout="20000" version="1.1"/>

    <dubbo:reference id="permissionService" interface="com.facishare.organization.adapter.api.permission.service.PermissionService" version="5.7"/>


    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="fsOpenQywxDB" />
    </bean>

    <bean id="outEventDataChangeMQSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="out-event-data-change-section"/>
    </bean>

    <bean id="userChangeEventSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="qywx-cooked-event-provider-section"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="out-event-data-change-consume-section"/>
        <constructor-arg name="messageListener" ref="outEventDataChangeListener"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="oaconnector-event-data-change-consume-section"/>
        <constructor-arg name="messageListener" ref="oaconnectorEventDataChangeListener"/>
    </bean>

    <bean class="com.fxiaoke.rocketmq.consumer.AutoConfMQPushConsumer"
          init-method="start" destroy-method="shutdown">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX_TODO_SECTION"/>
        <constructor-arg name="messageListener" ref="externalMessageListener"/>
    </bean>

    <!-- rest接口 -->
    <bean id="restServiceProxy" class="com.facishare.rest.proxy.RestServiceProxyFactory" init-method="init"
          p:configName="fs-online-consult-core-rest"/>

    <import resource="classpath:META-INF/fs-spring-dubbo-plugin.xml"/>

    <!--redisson-->
    <bean id="redissonClient" class="com.fxiaoke.common.redisson.RedissonFactoryBean">
        <property name="configName" value="fs-open-qywx-redis-config"/>
    </bean>

    <context:component-scan base-package="com.facishare.open.order.contacts.proxy,com.facishare.open.qywx,com.facishare.open.outer.oa"/>
<!--qywx-account-sync-provider end-->
<!--qywx-event-handler-web start-->

    <dubbo:reference id="employeeProviderService"
                     interface="com.facishare.organization.api.service.EmployeeProviderService"
                     version="5.7"/>

    <dubbo:reference id="activeSessionAuthorizeService"
                     interface="com.facishare.asm.api.service.ActiveSessionAuthorizeService"/>

    <dubbo:reference id="enterpriseEditionService"
                     interface="com.facishare.uc.api.service.EnterpriseEditionService"
                     protocol="dubbo"
                     retries="0"/>

    <!--aop打印log-->
    <bean id="logAspect" class="com.facishare.open.qywx.web.aop.LogAspect"/>
    <!--监控crm接口-->
    <bean id="crmRateLimiterAspect" class="com.facishare.open.order.contacts.proxy.aop.CrmRateLimiterAspect"/>
    <aop:config>
        <aop:aspect ref="controllerI18NAspect">
            <aop:pointcut id="controllerI18N"
                          expression="(execution(* com.facishare.open.qywx.web.controller.outer.*.*(..)))"/>
            <aop:around pointcut-ref="controllerI18N" method="around"/>
        </aop:aspect>
        <aop:aspect id="logMonitor" ref="logAspect">
            <aop:pointcut id="monitor"
                          expression="(execution(* com.facishare.open.qywx.web.controller.*.*(..))
                           or execution(* com.facishare.open.qywx.web.service.*.*(..))
                           or execution(* com.facishare.open.qywx.accountinner.service.*.*(..))
                           or execution(* com.facishare.open.qywx.accountinner.service.*.*(..))
                           or execution(* com.facishare.open.qywx.accountsync.service.*.*(..))
                           or execution(* com.facishare.open.qywx.save.service.*.*(..))
                           or execution(* com.facishare.open.qywx.messagesend.service.*.*(..))
                           )"/>
            <aop:around pointcut-ref="monitor" method="around"/>
        </aop:aspect>
        <aop:aspect ref="crmRateLimiterAspect">
            <aop:pointcut id="crmRateLimiter"
                          expression="(execution(* com.facishare.converter.EIEAConverter.*(..)) or
                      execution(* com.facishare.uc.api.service.EnterpriseEditionService.*(..)) or
                      execution(* com.fxiaoke.crmrestapi.service.ObjectService.*(..)) or
                      execution(* com.fxiaoke.crmrestapi.service.ObjectDescribeService.*(..)) or
                      execution(* com.facishare.stone.sdk.StoneProxyApi.*(..)) or
                      execution(* com.facishare.fsi.proxy.service.NFileStorageService.*(..)) or
                      execution(* com.fxiaoke.crmrestapi.service.ObjectDataService.*(..)) or
                      execution(* com.facishare.organization.adapter.api.service.DepartmentService.*(..)) or
                      execution(* com.facishare.organization.adapter.api.service.EmployeeService.*(..)) or
                      execution(* com.facishare.open.oauth.service.AuthService.*(..)) or
                      execution(* com.facishare.organization.api.service.EmployeeProviderService.*(..)) or
                      execution(* com.fxiaoke.otherrestapi.openmessage.service.SendMessageService.*(..)) or
                      execution(* com.fxiaoke.crmrestapi.service.MetadataControllerService.*(..))
            )"/>
            <aop:around pointcut-ref="crmRateLimiter" method="around"/>
        </aop:aspect>
        <aop:aspect ref="exceptionAspect">
            <aop:after-throwing method="onException"
                                throwing="e"
                                pointcut="(execution(* com.facishare.open.qywx..*.*(..)))"/>
        </aop:aspect>
    </aop:config>

    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="defaultEncoding" value="UTF-8"/>
        <property name="maxUploadSize" value="104857600"/>
        <property name="maxInMemorySize" value="40960"/>
    </bean>


    <bean id="enterpriseWechatEventMQSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="fs-enterprise-wechat-web-provider-section"/>
    </bean>

    <bean id="conversionChangeMessageSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="CLOUD-CONVERSION-CHANGE-EVENT-SECTION"/>
    </bean>

    <bean id="hitKeyWordMessageSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="QYWX-HIT-KEY-WORD-EVENT-SECTION"/>
    </bean>

    <bean id="enterpriseAccountBindSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="FS_CLOUD_CORP_BIND_SECTION"/>
    </bean>

    <bean id="employeeAccountBindSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="CLOUD-EMPLOYEE-BIND-PROVIDER-SECTION"/>
    </bean>

    <bean id="qywxEventNotifyMQSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="fs-open-cloud-provider-section"/>
    </bean>

    <bean id="accountSyncDataSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="FS_CLOUD_SYNC_DATA_SECTION"/>
    </bean>

    <bean id="oaconnectorEventDataChangeMQSender" class="com.facishare.open.qywx.web.notify.AutoConfRocketMQProducer"
          init-method="start">
        <constructor-arg name="configName" value="fs-open-qywx-mq-cms"/>
        <constructor-arg name="sectionNames" value="oaconnector-event-data-change-provider-section"/>
    </bean>

    <!-- message save mongo-->
    <bean id="saveCloudMessageMongoStore" name="saveCloudMessageMongoStore" class="com.github.mongo.support.MongoDataStoreFactoryBean">
        <property name="configName" value="fs-open-qywx-app-config"/>
        <property name="sectionNames" value="qywxMongo"/>
    </bean>

    <bean id="aFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.AFileStorageService"/>
    </bean>

    <bean id="fileManager" class="com.facishare.open.qywx.accountsync.excel.FileManager"/>

    <!--qywx-event-handler-web end-->
<!--qywx-message-save-provider start-->

    <context:annotation-config/>
    <task:annotation-driven executor="taskExecutor"/>
    <import resource="classpath:spring/fs-fsc-rest-client.xml"/>
    <import resource="dubbo-provider.xml"/>
    <import resource="dubbo-consumer.xml"/>
    <import resource="spring-job.xml"/>
    <import resource="classpath:spring/fs-qixin-rest-client.xml"/>
    <import resource="classpath:/spring/spring-mq.xml"/>

    <!--id生成器-->
    <import resource="classpath:spring/vesta-service-property-factory-bean.xml"/>

    <import resource="classpath*:mongo/mongo-store.xml"/>

    <import resource="classpath:spring/db-proxy-spring.xml"/>
    <import resource="classpath:spring/db-application.xml"/>

    <bean id="stoneProxyApi" class="com.facishare.restful.client.FRestApiProxyFactoryBean">
        <property name="type" value="com.facishare.stone.sdk.StoneProxyApi"/>
    </bean>


    <bean id="globalConfigService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiServiceProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.GlobalConfigService"/>
    </bean>
    <bean id="fsiWarehouseProxyFactory" class="com.facishare.fsi.proxy.FsiWarehouseProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>
    <bean id="fsiServiceProxyFactory" class="com.facishare.fsi.proxy.FsiServiceProxyFactory" init-method="init">
        <property name="configKey" value="fs-qixin-fsi-proxy"/>
    </bean>

    <bean id="nFileStorageService" class="com.facishare.fsi.proxy.FsiServiceProxyFactoryBean">
        <property name="factory" ref="fsiWarehouseProxyFactory"/>
        <property name="type" value="com.facishare.fsi.proxy.service.NFileStorageService"/>
    </bean>

    <bean id="messageService" class="com.facishare.dubbo.plugin.client.EISupportDubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.qixin.api.service.MessageService"/>
        <property name="serverHostProfile" ref="qixinApiHostProfile"/>
    </bean>


    <!-- 蜂眼监控 -->
    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <!-- 异步线程池 -->
    <bean id="taskExecutor" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor">
        <!-- 核心线程数  -->
        <property name="corePoolSize" value="1"/>
        <!-- 最大线程数 -->
        <property name="maxPoolSize" value="32"/>
        <!-- 队列最大长度 >=mainExecutor.maxSize -->
        <property name="queueCapacity" value="1800"/>
        <!-- 线程池维护线程所允许的空闲时间 -->
        <property name="keepAliveSeconds" value="300"/>
        <!--允许核心线程超时销毁-->
        <property name="allowCoreThreadTimeOut" value="true"/>
        <!-- 线程池对拒绝任务(无线程可用)的处理策略 -->
        <property name="rejectedExecutionHandler">
            <bean class="java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy"/>
        </property>
    </bean>
    <!--qywx-message-save-provider end-->

    <dubbo:service interface="com.facishare.open.qywx.messagesend.service.QYWeixinMessageSendService"
                   ref="qYWeixinMessageSendService"
                   protocol="dubbo"
                   timeout="20000"
                   version="1.0"
                   retries="0"
                   group="${oaDubboConfigGroup}"
                   />

    <dubbo:service interface="com.facishare.open.outer.oa.connector.common.api.outerInterface.OuterAbstractSettingService"
                   ref="qywxOAConnectManager"
                   protocol="dubbo"
                   timeout="30000"
                   version="1.0"
                   retries="1"/>

    <bean id="redisDataSource" class="com.facishare.open.order.contacts.proxy.api.datasource.RedisDataSource">
        <property name="jedisCmd" ref="publishRedis"/>
    </bean>
    <bean id="publishRedis" class="com.github.jedis.support.JedisFactoryBean" scope="singleton"
          p:configName="fs-open-qywx-redis-config"/>

    <bean class="com.fxiaoke.metrics.MetricsConfiguration"/>

    <!-- 启用@AspectJ注解 -->
    <aop:aspectj-autoproxy/>

    <import resource="classpath:spring/ei-ea-converter.xml"/>

    <import resource="classpath:fs-plat-privilege-api-rest-client.xml"/>

    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-provider.xml"/>

    <bean id="todoRestClient" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-qixin-objgroup-rest-proxy-config" init-method="init"/>

    <import resource="classpath:META-INF/spring/objgroup-common.xml"/>

    <bean id="employeeDataService" class="com.facishare.qixin.common.remote.EmployeeDataServiceImpl"/>


</beans>