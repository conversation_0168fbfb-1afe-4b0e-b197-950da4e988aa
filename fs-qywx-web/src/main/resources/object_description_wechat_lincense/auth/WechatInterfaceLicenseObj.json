[
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj",
        "name": "查看列表",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||View",
        "name": "查看详情",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Add",
        "name": "新建",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Clone",
        "name": "复制",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Edit",
        "name": "编辑",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Relate",
        "name": "关联",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Abolish",
        "name": "作废",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Recover",
        "name": "恢复",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Delete",
        "name": "删除",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Import",
        "name": "导入",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Export",
        "name": "导出",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ChangeOwner",
        "name": "更换负责人",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||EditTeamMember",
        "name": "编辑团队成员",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||StartBPM",
        "name": "发起流程",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ViewEntireBPM",
        "name": "查看完整流程",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||StopBPM",
        "name": "终止业务流程",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ChangeBPMApprover",
        "name": "更换流程处理人",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Print",
        "name": "打印",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||IntelligentForm",
        "name": "智能表单",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Lock",
        "name": "锁定",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||Unlock",
        "name": "解锁",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ModifyLog_Recover",
        "name": "修改记录恢复",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||StageMoveTo",
        "name": "阶段推进",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ChangeStageCandidateIds",
        "name": "更换阶段任务处理人",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||PictureAnnexDownload",
        "name": "图片及附件下载",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||StageBackTo",
        "name": "阶段回退",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||StageReactivation",
        "name": "阶段重新激活",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ViewApprovalConfig",
        "name": "查看审批配置",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ViewApprovalInstanceLog",
        "name": "查看审批意见",
        "parentCode": "00000000000000000000000000000000"
    },
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCode": "WechatInterfaceLicenseObj||ViewBPMInstanceLog",
        "name": "查看业务流流程日志",
        "parentCode": "00000000000000000000000000000000"
    }
]



[
    {
        "appId": "CRM",
        "tenantId": "-100",
        "functionCodes": [
            "WechatInterfaceLicenseObj",
            "WechatInterfaceLicenseObj||View",
            "WechatInterfaceLicenseObj||Add",
            "WechatInterfaceLicenseObj||Clone",
            "WechatInterfaceLicenseObj||Edit",
            "WechatInterfaceLicenseObj||Relate",
            "WechatInterfaceLicenseObj||Abolish",
            "WechatInterfaceLicenseObj||Recover",
            "WechatInterfaceLicenseObj||Delete",
            "WechatInterfaceLicenseObj||Import",
            "WechatInterfaceLicenseObj||Export",
            "WechatInterfaceLicenseObj||ChangeOwner",
            "WechatInterfaceLicenseObj||EditTeamMember",
            "WechatInterfaceLicenseObj||StartBPM",
            "WechatInterfaceLicenseObj||ViewEntireBPM",
            "WechatInterfaceLicenseObj||StopBPM",
            "WechatInterfaceLicenseObj||ChangeBPMApprover",
            "WechatInterfaceLicenseObj||Print",
            "WechatInterfaceLicenseObj||IntelligentForm",
            "WechatInterfaceLicenseObj||Lock",
            "WechatInterfaceLicenseObj||Unlock",
            "WechatInterfaceLicenseObj||ModifyLog_Recover",
            "WechatInterfaceLicenseObj||StageMoveTo",
            "WechatInterfaceLicenseObj||ChangeStageCandidateIds",
            "WechatInterfaceLicenseObj||PictureAnnexDownload",
            "WechatInterfaceLicenseObj||StageBackTo",
            "WechatInterfaceLicenseObj||StageReactivation",
            "WechatInterfaceLicenseObj||ViewApprovalConfig",
            "WechatInterfaceLicenseObj||ViewApprovalInstanceLog",
            "WechatInterfaceLicenseObj||ViewBPMInstanceLog"
        ],
        "roleCode": "00000000000000000000000000000006"
    }
]