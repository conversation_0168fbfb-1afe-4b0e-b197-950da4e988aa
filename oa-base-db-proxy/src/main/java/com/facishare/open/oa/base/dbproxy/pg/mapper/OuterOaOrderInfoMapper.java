package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaOrderInfoEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Mapper 接口 - 订单信息
 */
@Mapper
public interface OuterOaOrderInfoMapper extends BaseMapper2<OuterOaOrderInfoEntity> {
    /**
     * 插入或更新订单信息
     * 如果订单ID已存在则更新，不存在则插入
     *
     * @param entity 订单信息实体
     * @return 影响的行数
     */
    @Insert("INSERT INTO outer_oa_order_info (" +
            "id, channel, order_id, order_type, app_id, " +
            "paid_out_ea, begin_time, end_time, order_info, " +
            "create_time, update_time" +
            ") VALUES (" +
            "#{entity.id}, #{entity.channel}, #{entity.orderId}, " +
            "#{entity.orderType}, #{entity.appId}, #{entity.paidOutEa}, " +
            "#{entity.beginTime}, #{entity.endTime}, #{entity.orderInfo}, " +
            "#{entity.createTime}, #{entity.updateTime}" +
            ") ON CONFLICT (order_id) DO UPDATE SET " +
            "id = #{entity.id}, " +
            "channel = #{entity.channel}, " +
            "order_type = #{entity.orderType}, " +
            "app_id = #{entity.appId}, " +
            "paid_out_ea = #{entity.paidOutEa}, " +
            "begin_time = #{entity.beginTime}, " +
            "end_time = #{entity.endTime}, " +
            "order_info = #{entity.orderInfo}, " +
            "update_time = #{entity.updateTime}")
    int upsertInfo(@Param("entity") OuterOaOrderInfoEntity entity);

/**
     * 根据订单ID查询订单信息
     *
     * @param orderId 订单ID
     * @return 订单信息实体
     */
    @Select("SELECT id, channel, order_id, order_type, app_id, " +
            "paid_out_ea, begin_time, end_time, order_info, " +
            "create_time, update_time " +
            "FROM outer_oa_order_info " +
            "WHERE order_id = #{orderId}")
    OuterOaOrderInfoEntity getByOrderId(@Param("orderId") String orderId);

}
