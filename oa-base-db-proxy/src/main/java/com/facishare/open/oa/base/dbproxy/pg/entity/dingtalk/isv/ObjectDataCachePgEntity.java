package com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 对象数据缓存实体类 - PostgreSQL
 */
@Data
@TableName("dingtalk_isv_object_data_cache")
public class ObjectDataCachePgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 对象数据编码
     */
    private String dataCode;

    /**
     * CRM对象数据ID
     */
    private String objectDataId;

    /**
     * 钉钉企业ID
     */
    private String corpId;

    /**
     * 企业ei
     */
    private Integer ei;

    /**
     * 钉钉应用ID
     */
    private Long appId;

    /**
     * 对象apiName
     */
    private String objectApiName;

    /**
     * 对象json数据
     */
    private String jsonData;

    /**
     * 是否已经同步到钉钉连接器
     */
    private Boolean synced;

    /**
     * 是否同步失败
     */
    private Boolean syncFailed;

    /**
     * 同步失败的原因
     */
    private String failedReason;

    /**
     * 数据同步方向 0:crm->钉钉连接器 1:钉钉连接器->crm
     */
    private Integer syncDirection;

    /**
     * 数据是否被作废或删除
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Timestamp createTime;

    /**
     * 更新时间
     */
    private Timestamp updateTime;
}