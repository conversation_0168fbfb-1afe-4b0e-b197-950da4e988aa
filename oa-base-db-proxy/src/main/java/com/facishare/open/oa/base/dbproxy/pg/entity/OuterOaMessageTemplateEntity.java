package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaMessageTemplateStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaMessageTemplateEntity
 * 消息模板实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("outer_oa_message_template")
public class OuterOaMessageTemplateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 渠道
     */
    private ChannelEnum channel;

    /**
     * 外部企业ea
     */
    private String outEa;

    /**
     * 消息实例模板ID
     */
    private String templateId;

    /**
     * 状态
     */
    private OuterOaMessageTemplateStatusEnum status;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 修改时间（long类型时间戳）
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
}
