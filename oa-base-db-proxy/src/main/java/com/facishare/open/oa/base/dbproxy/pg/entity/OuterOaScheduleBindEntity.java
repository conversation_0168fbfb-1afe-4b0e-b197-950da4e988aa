package com.facishare.open.oa.base.dbproxy.pg.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaScheduleBindStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * OuterOaScheduleBindEntity
 * 日程绑定实体类
 */
@Data
@TableName("outer_oa_schedule_bind")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OuterOaScheduleBindEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 渠道标识
     */
    private ChannelEnum channel;

    /**
     * 数据中心ID
     */
    private String dcId;

    /**
     * 纷享企业标识
     */
    private String fsEa;

    /**
     * 外部企业标识
     */
    private String outEa;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 纷享日程ID
     */
    private String fsScheduleId;

    /**
     * 外部日程ID
     */
    private String outScheduleId;

    /**
     * 推送的外部人员ID
     */
    private String outUserId;

    /**
     * 状态
     */
    private OuterOaScheduleBindStatusEnum status;

    /**
     * 创建时间（long类型时间戳）
     */
    private Long createTime;

    /**
     * 更新时间（long类型时间戳）
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateTime;
}
