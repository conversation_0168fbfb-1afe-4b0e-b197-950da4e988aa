package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaMessageBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaMessageBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaMessageBindParams;
import com.fxiaoke.api.IdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * Manager 类 - 消息绑定
 */
@Slf4j
@Component
// IgnoreI18nFile
public class OuterOaMessageBindManager {

    @Resource
    private OuterOaMessageBindMapper outerOaMessageBindMapper;

    public Integer insert(OuterOaMessageBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return outerOaMessageBindMapper.insert(entity);
    }

    public Integer updateById(OuterOaMessageBindEntity entity) {
        return outerOaMessageBindMapper.updateById(entity);
    }

    /**
     * 根据参数查询消息绑定信息列表
     * 
     * @param params 查询参数
     * @return 消息绑定实体列表
     */
    public List<OuterOaMessageBindEntity> getEntities(OuterOaMessageBindParams params) {
        LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaMessageBindEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaMessageBindEntity::getDcId, params.getDcId());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaMessageBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getSourceId())) {
            wrapper.eq(OuterOaMessageBindEntity::getSourceId, params.getSourceId());
        }
        if (StringUtils.isNotEmpty(params.getTaskId())) {
            wrapper.eq(OuterOaMessageBindEntity::getTaskId, params.getTaskId());
        }
        if (params.getMessageType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getMessageType, params.getMessageType());
        }
        if (params.getEventType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getEventType, params.getEventType());
        }
        if (params.getStatus() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getStatus, params.getStatus());
        }

        return outerOaMessageBindMapper.selectList(wrapper);
    }

    /**
     * 根据时间范围查询消息绑定信息列表
     *
     * @param params 基本查询参数
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime 结束时间（毫秒时间戳）
     * @return 符合条件的消息绑定实体列表，按创建时间降序排序
     */
    public List<OuterOaMessageBindEntity> getEntitiesByTimeRange(OuterOaMessageBindParams params, Long startTime, Long endTime) {
        log.info("getEntitiesByTimeRange, params: {}, startTime: {}, endTime: {}", params, startTime, endTime);
        
        LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 设置基本查询条件
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaMessageBindEntity::getAppId, params.getAppId());
        }
        if (params.getMessageType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getMessageType, params.getMessageType());
        }
        if (params.getEventType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getEventType, params.getEventType());
        }
        if (StringUtils.isNotEmpty(params.getSourceId())) {
            wrapper.eq(OuterOaMessageBindEntity::getSourceId, params.getSourceId());
        }
        if (StringUtils.isNotEmpty(params.getTaskId())) {
            wrapper.eq(OuterOaMessageBindEntity::getTaskId, params.getTaskId());
        }
        if (params.getStatus() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getStatus, params.getStatus());
        }

        // 设置时间范围条件
        if (startTime != null) {
            wrapper.ge(OuterOaMessageBindEntity::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(OuterOaMessageBindEntity::getCreateTime, endTime);
        }

        // 按创建时间降序排序
        wrapper.orderByDesc(OuterOaMessageBindEntity::getCreateTime);
        
        List<OuterOaMessageBindEntity> result = outerOaMessageBindMapper.selectList(wrapper);
        log.info("getEntitiesByTimeRange result size: {}", result != null ? result.size() : 0);
        
        return result;
    }

    public OuterOaMessageBindEntity getEntity(OuterOaMessageBindParams params) {
        LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getId())) {
            wrapper.eq(OuterOaMessageBindEntity::getId, params.getId());
        }
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getDcId())) {
            wrapper.eq(OuterOaMessageBindEntity::getDcId, params.getDcId());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaMessageBindEntity::getAppId, params.getAppId());
        }
        if (StringUtils.isNotEmpty(params.getSourceId())) {
            wrapper.eq(OuterOaMessageBindEntity::getSourceId, params.getSourceId());
        }
        if (StringUtils.isNotEmpty(params.getTaskId())) {
            wrapper.eq(OuterOaMessageBindEntity::getTaskId, params.getTaskId());
        }
        if (params.getMessageType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getMessageType, params.getMessageType());
        }
        if (params.getEventType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getEventType, params.getEventType());
        }
        if (params.getStatus() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getStatus, params.getStatus());
        }
        wrapper.last("limit 1");
        return outerOaMessageBindMapper.selectOne(wrapper);
    }

    /**
     * 批量upsert消息绑定信息（基于PostgreSQL的ON CONFLICT语法）
     *
     * @param entities 待更新的实体列表
     * @return 受影响的行数
     */
    public Integer batchUpsertInfos(List<OuterOaMessageBindEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }

        // 为没有ID的实体生成ID
        entities.forEach(entity -> {
            if (StringUtils.isEmpty(entity.getId())) {
                entity.setId(IdGenerator.get());
            }
        });

        try {
            return outerOaMessageBindMapper.batchUpsertInfos(entities);
        } catch (Exception e) {
            log.error("批量upsert消息绑定信息失败", e);
            throw new RuntimeException("批量upsert消息绑定信息失败", e);
        }
    }

    /**
     * 批量查询消息绑定信息 使用fsEa、outEa、sourceId、taskId和outUserId作为查询条件
     *
     * @param entities 包含查询条件的实体列表
     * @return 查询到的实体列表
     */
    public List<OuterOaMessageBindEntity> batchQuery(List<OuterOaMessageBindEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return new ArrayList<>();
        }

        List<OuterOaMessageBindEntity> result = new ArrayList<>();
        for (OuterOaMessageBindEntity entity : entities) {
            if (entity == null || StringUtils.isEmpty(entity.getFsEa()) || StringUtils.isEmpty(entity.getOutEa())
                    || StringUtils.isEmpty(entity.getSourceId()) || StringUtils.isEmpty(entity.getTaskId())
                    || StringUtils.isEmpty(entity.getOutUserId())) {
                continue;
            }

            LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OuterOaMessageBindEntity::getFsEa, entity.getFsEa())
                    .eq(OuterOaMessageBindEntity::getOutEa, entity.getOutEa())
                    .eq(OuterOaMessageBindEntity::getSourceId, entity.getSourceId())
                    .eq(OuterOaMessageBindEntity::getTaskId, entity.getTaskId())
                    .eq(OuterOaMessageBindEntity::getOutUserId, entity.getOutUserId());

            OuterOaMessageBindEntity existingEntity = outerOaMessageBindMapper.selectOne(wrapper);
            if (existingEntity != null) {
                result.add(existingEntity);
            }
        }

        return result;
    }

    /**
     * 根据时间范围分页查询消息绑定信息列表
     *
     * @param params 基本查询参数
     * @param startTime 开始时间（毫秒时间戳）
     * @param endTime 结束时间（毫秒时间戳）
     * @param pageNum 页码，从1开始
     * @param pageSize 每页大小
     * @return 分页查询结果
     */
    public Page<OuterOaMessageBindEntity> getEntitiesByTimeRangePage(OuterOaMessageBindParams params, Long startTime, Long endTime, Integer pageNum, Integer pageSize) {
        log.info("getEntitiesByTimeRangePage, params: {}, startTime: {}, endTime: {}, pageNum: {}, pageSize: {}", 
                params, startTime, endTime, pageNum, pageSize);
        
        // 创建分页对象，注意页码从1开始
        Page<OuterOaMessageBindEntity> page = new Page<>(pageNum, pageSize);
        
        LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
        
        // 设置基本查询条件
        if (params.getChannel() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getChannel, params.getChannel());
        }
        if (StringUtils.isNotEmpty(params.getFsEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getFsEa, params.getFsEa());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(OuterOaMessageBindEntity::getOutEa, params.getOutEa());
        }
        if (StringUtils.isNotEmpty(params.getAppId())) {
            wrapper.eq(OuterOaMessageBindEntity::getAppId, params.getAppId());
        }
        if (params.getMessageType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getMessageType, params.getMessageType());
        }
        if (params.getEventType() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getEventType, params.getEventType());
        }
        if (StringUtils.isNotEmpty(params.getSourceId())) {
            wrapper.eq(OuterOaMessageBindEntity::getSourceId, params.getSourceId());
        }
        if (StringUtils.isNotEmpty(params.getTaskId())) {
            wrapper.eq(OuterOaMessageBindEntity::getTaskId, params.getTaskId());
        }
        if (params.getStatus() != null) {
            wrapper.eq(OuterOaMessageBindEntity::getStatus, params.getStatus());
        }

        // 设置时间范围条件
        if (startTime != null) {
            wrapper.ge(OuterOaMessageBindEntity::getCreateTime, startTime);
        }
        if (endTime != null) {
            wrapper.le(OuterOaMessageBindEntity::getCreateTime, endTime);
        }

        // 按创建时间降序排序
        wrapper.orderByDesc(OuterOaMessageBindEntity::getCreateTime);
        
        // 执行分页查询
        Page<OuterOaMessageBindEntity> result = outerOaMessageBindMapper.selectPage(page, wrapper);
        log.info("getEntitiesByTimeRangePage result: total={}, current={}, size={}, records.size={}", 
                result.getTotal(), result.getCurrent(), result.getSize(), result.getRecords().size());
        
        return result;
    }

    //通过taskIds和dcId获取绑定关系
    public List<OuterOaMessageBindEntity> getEntitiesByTaskIdsAndDcId(List<String> taskIds, String dcId) {
        if (CollectionUtils.isEmpty(taskIds) || StringUtils.isEmpty(dcId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<OuterOaMessageBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OuterOaMessageBindEntity::getTaskId, taskIds)
                .eq(OuterOaMessageBindEntity::getDcId, dcId);

        return outerOaMessageBindMapper.selectList(wrapper);
    }
}
