package com.facishare.open.oa.base.dbproxy.ch.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.ch.entity.BizLogOaconnectoropendataDistBo;
import com.facishare.open.oa.base.dbproxy.ch.mapper.BizLogOaconnectoropendataDistMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class BizLogOaconnectoropendataDistManager {

    @Resource
    private BizLogOaconnectoropendataDistMapper bizLogOaconnectoropendataDistMapper;

    public List<BizLogOaconnectoropendataDistBo> findEnterpriseCreateError(String channelId, String dataTypeId, String corpId) {
        LambdaQueryWrapper<BizLogOaconnectoropendataDistBo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BizLogOaconnectoropendataDistBo::getChannelId, channelId);
        wrapper.eq(BizLogOaconnectoropendataDistBo::getDataTypeId, dataTypeId);
        wrapper.eq(BizLogOaconnectoropendataDistBo::getCorpId, corpId);
        return bizLogOaconnectoropendataDistMapper.selectList(wrapper);
    }
}
