package com.facishare.open.oa.base.dbproxy.mongo.store;

import com.facishare.open.oa.base.dbproxy.mongo.dao.FsUserInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.FsUserInfoDoc;
import com.github.autoconf.ConfigFactory;
import com.github.mongo.support.DatastoreExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.IndexModel;
import com.mongodb.client.model.IndexOptions;
import com.mongodb.client.model.Indexes;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.codecs.configuration.CodecRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 纷享人员详情 mongo store
 * <AUTHOR>
 * @date 2023/12/06
 */
@Repository
@Slf4j
public class FsUserInfoMongoStore {

    @Getter
    private final DatastoreExt store;

    private final static String CollectionPrefix = "fs_user_info";
    private final String dbName;
    private final Set<String> collectionCache = Sets.newConcurrentHashSet();

    private static class SingleCodecHolder {
        //需要让自定义的codec放前面
        private static final CodecRegistry codecRegistry = CodecRegistries.fromRegistries(
                MongoClientSettings.getDefaultCodecRegistry(),
                CodecRegistries.fromProviders(PojoCodecProvider.builder()
                        .register(FsUserInfoDoc.class)
                        .automatic(true).build()));
    }


    public FsUserInfoMongoStore(@Qualifier("oaBaseMongoStore") DatastoreExt store) {
        this.store = store;
        this.dbName = ConfigFactory.getInstance().getConfig("fs-feishu-config")
                .get("mongo.dbName", "fs-open-qywx");
        store.getMongo().getDatabase(dbName)
                .listCollectionNames().iterator().forEachRemaining(v -> {
                    if (v.startsWith(CollectionPrefix)) {
                        collectionCache.add(v);
                    }
                });
    }

    private String getCollectionName() {
        return CollectionPrefix;
    }

    /**
     * 创建集合，检查索引
     * 这里不移除索引，另外使用批量接口移除
     * 也不根据名称更新索引，同样，更新索引需要走批量接口更新
     */
    public synchronized MongoCollection<FsUserInfoDoc> getOrCreateCollection() {
        //dbName会从配置文件的mongo.servers解析
        String collectionName = getCollectionName();
        MongoCollection<FsUserInfoDoc> collection = getDatabase()
                .withCodecRegistry(SingleCodecHolder.codecRegistry)
                .getCollection(collectionName, FsUserInfoDoc.class);
        if (!collectionCache.add(collectionName)) {
            return collection;
        }

        List<IndexModel> indexList = Lists.newArrayList();
        //过期自动清理时间,365天
//        Bson expireTimeBson = Indexes.descending(OaConnectorOutUserInfoMongoDao.oa_createTime);
//        indexList.add(new IndexModel(expireTimeBson, new IndexOptions()
//                .name("index_expire_time")
//                .expireAfter(365L, TimeUnit.DAYS)
//                .background(true)));

        //根据fsEa字段创建索引,索引名称=index_fsEa
        Bson fsEaBson = Indexes.compoundIndex(
                Indexes.ascending(FsUserInfoMongoDao.fs_fsEa));
        indexList.add(new IndexModel(fsEaBson, new IndexOptions()
                .name("index_fsEa")
                .background(true)));

        Bson fsEaFsUserIdBson = Indexes.compoundIndex(
                Indexes.ascending(FsUserInfoMongoDao.fs_fsEa),
                Indexes.ascending(FsUserInfoMongoDao.fs_fsUserId));
        indexList.add(new IndexModel(fsEaFsUserIdBson, new IndexOptions()
                .name("index_fsEa_fsUserId")
                .background(true)));

        Bson fsEaStatusBson = Indexes.compoundIndex(
                Indexes.ascending(FsUserInfoMongoDao.fs_fsEa),
                Indexes.ascending(FsUserInfoMongoDao.fs_fsStatus));
        indexList.add(new IndexModel(fsEaStatusBson, new IndexOptions()
                .name("index_fsEa_status")
                .background(true)));

        Bson fsEaFsUserIdStatusBson = Indexes.compoundIndex(
                Indexes.ascending(FsUserInfoMongoDao.fs_fsEa),
                Indexes.ascending(FsUserInfoMongoDao.fs_fsUserId),
                Indexes.ascending(FsUserInfoMongoDao.fs_fsStatus));
        indexList.add(new IndexModel(fsEaFsUserIdStatusBson, new IndexOptions()
                .name("index_fsEa_fsUserId_status")
                .background(true)));

        List<String> created = collection.createIndexes(indexList);
        log.info("FsUserInfoMongoStore.getOrCreateCollection created indexes: {}, wanted: {}, created: {}", created, indexList, created);

        return collection;
    }

    public MongoDatabase getDatabase() {
        return store.getMongo().getDatabase(dbName);
    }
}
