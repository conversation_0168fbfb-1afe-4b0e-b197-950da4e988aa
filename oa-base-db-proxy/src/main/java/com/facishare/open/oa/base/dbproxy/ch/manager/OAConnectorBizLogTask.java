package com.facishare.open.oa.base.dbproxy.ch.manager;

import com.facishare.open.order.contacts.proxy.api.utils.TraceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class OAConnectorBizLogTask {
    @Resource
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;
    private List<OAConnectorOpenDataModel> dataList = new ArrayList<>();

    private Timer timer = new Timer();
    private TimerTask timerTask = null;

    public void add(OAConnectorOpenDataModel model) {
        dataList.add(model);
        if(timerTask==null) {
            timerTask = new TimerTask() {
                @Override
                public void run() {
                    try {
                        String traceId = TraceUtils.getTraceId();
                        if(StringUtils.isEmpty(traceId)) {
                            TraceUtils.initTraceId(UUID.randomUUID().toString());
                        }
                        log.info("OAConnectorBizLogTask.run,begin");
                        log.info("OAConnectorBizLogTask.run,dataList.size={}",dataList.size());
                        if(CollectionUtils.isNotEmpty(dataList)) {
                            List<OAConnectorOpenDataModel> list = new ArrayList<>(dataList.size());
                            list.addAll(dataList);
                            dataList.clear();
                            log.info("OAConnectorBizLogTask.run,list.size={}",list.size());

                            for(OAConnectorOpenDataModel model : list) {
                                log.info("OAConnectorBizLogTask.run,model={}",model);
                                oaConnectorOpenDataManager.sendNow(model);
                            }
                            list.clear();
                        }
                        log.info("OAConnectorBizLogTask.run,end");
                        if(CollectionUtils.isEmpty(dataList)) {
                            timerTask = null;
                        }
                    } catch (Exception e) {
                        log.info("OAConnectorBizLogTask,run,exception={}",e.getMessage(),e);
                    }
                }
            };
            timer.schedule(timerTask,0,60 * 1000);
        }
    }
}
