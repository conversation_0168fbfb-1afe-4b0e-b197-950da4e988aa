package com.facishare.open.oa.base.dbproxy.pg.mapper.dingtalkisv;

import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv.CrmSyncObjPgEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.BaseMapper2;
import com.facishare.open.oa.base.dbproxy.pg.arg.NeedSyncDataArg;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * CRM对象同步信息Mapper - PostgreSQL
 */
public interface CrmSyncObjPgMapper extends BaseMapper2<CrmSyncObjPgEntity> {

    @Select("<script>" +
            "select * from dingtalk_isv_crm_sync_obj_info where is_init=1" +
            "<if test='needSyncDataArg.dingApiName!=null'>" +
            "and ding_api_name=#{needSyncDataArg.dingApiName}" +
            "</if>"+
            "<if test='needSyncDataArg.crmApiName!=null'>" +
            "and crm_api_name=#{needSyncDataArg.crmApiName}" +
            "</if>"+
            "<if test='needSyncDataArg.ei!=null'>" +
            "and ei=#{needSyncDataArg.ei}" +
            "</if>" +
            "<if test='needSyncDataArg.direction!=null'>" +
            "and direction=#{needSyncDataArg.direction}" +
            "</if>" +
            "limit #{page},#{size}" +
            "</script>")
    List<CrmSyncObjPgEntity> conditionNeedSynData(@Param("needSyncDataArg") NeedSyncDataArg needSyncDataArg, @Param("page") Integer page, @Param("size") Integer size);

    @Insert("<script>" +
            "INSERT INTO dingtalk_isv_crm_sync_obj_info" +
            "(corp_id,ei,ding_api_name,crm_api_name,direction,is_init,last_sync_time)" +
            "values" +
            "<foreach collection=\"objList\"  item=\"item\" separator=\",\" index=\"index\">" +
            "(#{item.corpId},#{item.ei},#{item.dingApiName},#{item.crmApiName},#{item.direction},#{item.isInit},#{item.lastSyncTime})" +
            "</foreach>" +
            " ON CONFLICT DO NOTHING" +
            "</script>")
    Integer batchSaveData(@Param("objList") List<CrmSyncObjPgEntity> objEntityList);

    @Update("<script>" +
            "update dingtalk_isv_crm_sync_obj_info set is_init=#{needSyncDataArg.isInit} where ei=#{needSyncDataArg.ei} " +
            "and ding_api_name=#{needSyncDataArg.dingApiName} and crm_api_name=#{needSyncDataArg.crmApiName} " +
            "</script>")
    Integer updateInit(@Param("needSyncDataArg") NeedSyncDataArg needSyncDataArg);

    @Select("<script>" +
            "select count(*) from dingtalk_isv_crm_sync_obj_info where corp_id=#{dingCorpId}" +
            "</script>")
    Integer queryIsSyncData(@Param("dingCorpId") String dingCorpId);

    @Update("<script>" +
            "update dingtalk_isv_crm_sync_obj_info set last_sync_time=#{needSyncDataArg.lastSyncTime} where corp_id=#{needSyncDataArg.corpId} " +
            "and ding_api_name=#{needSyncDataArg.dingApiName} and crm_api_name=#{needSyncDataArg.crmApiName} and direction=#{needSyncDataArg.direction}" +
            "</script>")
    Integer updateLastSyncTIme(@Param("needSyncDataArg") NeedSyncDataArg needSyncDataArg);

    @Select("select * from dingtalk_isv_crm_sync_obj_info where ei=#{ei} and id > #{id} order by id asc limit #{limit}")
    List<CrmSyncObjPgEntity> pageById(@Param("ei") Integer ei, @Param("id") Long id, @Param("limit") Integer limit);
} 