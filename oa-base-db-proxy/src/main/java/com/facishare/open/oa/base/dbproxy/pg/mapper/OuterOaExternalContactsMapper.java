package com.facishare.open.oa.base.dbproxy.pg.mapper;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaExternalContactsEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * Mapper 接口 - 外部联系人绑定
 */
@Mapper
public interface OuterOaExternalContactsMapper extends BaseMapper2<OuterOaExternalContactsEntity> {
    @Update("<script>" +
            "INSERT INTO outer_oa_external_contacts " +
            "(id, channel, out_ea, out_user_id, external_user_id, external_name, avatar, external_contacts_info, create_time, update_time) " +
            "VALUES " +
            "<foreach collection='list' item='item' separator=','> " +
            "    ( " +
            "    #{item.id}, #{item.channel}, #{item.outEa}, " +
            "    #{item.outUserId}, #{item.externalUserId}, #{item.externalName}, #{item.avatar}, " +
            "    #{item.externalContactsInfo}, #{item.createTime}, #{item.updateTime} " +
            "    ) " +
            "</foreach> " +
            "ON CONFLICT (channel, out_ea, out_user_id, external_user_id) " +
            "DO UPDATE SET " +
            "    external_name = EXCLUDED.external_name, " +
            "    avatar = EXCLUDED.avatar, " +
            "    external_contacts_info = EXCLUDED.external_contacts_info, " +
            "    update_time = EXCLUDED.update_time " +
            "</script>")
    Integer batchUpsertInfos(@Param("list") List<OuterOaExternalContactsEntity> entities);
}
