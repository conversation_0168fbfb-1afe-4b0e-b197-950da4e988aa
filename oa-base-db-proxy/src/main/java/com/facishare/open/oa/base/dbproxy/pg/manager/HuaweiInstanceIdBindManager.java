package com.facishare.open.oa.base.dbproxy.pg.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.oa.base.dbproxy.pg.entity.HuaweiInstanceIdBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.HuaweiInstanceIdBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.params.HuaweiInstanceIdBindParams;
import com.fxiaoke.api.IdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Manager 类 - 华为实例ID绑定
 */
@Component
public class HuaweiInstanceIdBindManager {

    @Resource
    private HuaweiInstanceIdBindMapper huaweiInstanceIdBindMapper;

    /**
     * 插入数据
     */
    public Integer insert(HuaweiInstanceIdBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            entity.setId(IdGenerator.get());
        }
        return huaweiInstanceIdBindMapper.insert(entity);
    }

    /**
     * 根据ID更新数据
     */
    public Integer updateById(HuaweiInstanceIdBindEntity entity) {
        if (StringUtils.isEmpty(entity.getId())) {
            return 0;
        }
        return huaweiInstanceIdBindMapper.updateById(entity);
    }

    /**
     * 根据实例ID和外部企业ea查询绑定信息
     */
    public HuaweiInstanceIdBindEntity getEntity(String instanceId, String outEa) {
        LambdaQueryWrapper<HuaweiInstanceIdBindEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(HuaweiInstanceIdBindEntity::getInstanceId, instanceId);
        wrapper.eq(HuaweiInstanceIdBindEntity::getOutEa, outEa);
        return huaweiInstanceIdBindMapper.selectOne(wrapper);
    }

    /**
     * 根据参数查询绑定信息列表
     */
    public List<HuaweiInstanceIdBindEntity> getEntities(HuaweiInstanceIdBindParams params) {
        LambdaQueryWrapper<HuaweiInstanceIdBindEntity> wrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(params.getInstanceId())) {
            wrapper.eq(HuaweiInstanceIdBindEntity::getInstanceId, params.getInstanceId());
        }
        if (StringUtils.isNotEmpty(params.getOutEa())) {
            wrapper.eq(HuaweiInstanceIdBindEntity::getOutEa, params.getOutEa());
        }
        return huaweiInstanceIdBindMapper.selectList(wrapper);
    }
} 