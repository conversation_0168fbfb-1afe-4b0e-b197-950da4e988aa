package com.facishare.open.oa.base.dbproxy.manager;

import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaDepartmentBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.AccountTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FieldTypeEnum;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Spliterator;

/**
 * 支持字段数据，根据类型转换
 */
@Component
public class FieldTypeConvertManager {

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;
    @Autowired
    private OuterOaDepartmentBindManager outerOaDepartmentBindManager;
    @Autowired
    private ObjectDataManager objectDataManager;


    public Object convert(String dataCenterId,Object sourceValue, FieldTypeEnum fieldTypeEnum,String fsEa){
        switch (fieldTypeEnum){
            case employee:
                OuterOaEmployeeBindEntity oaEmployeeBindEntity = outerOaEmployeeBindManager.getEntitiesByDcId(dataCenterId, null, String.valueOf(sourceValue));
                return oaEmployeeBindEntity == null ? null : Lists.newArrayList(oaEmployeeBindEntity.getFsEmpId());
            case department:
                String departId=null;
                if (sourceValue instanceof List){
                    List<String> deptIds = (List<String>) sourceValue;
                    departId=deptIds.get(0);
                }else{
                     departId=String.valueOf(sourceValue);
                }
                OuterOaDepartmentBindEntity deptEntityByDcId = outerOaDepartmentBindManager.getDeptEntityByDcId(dataCenterId, null, String.valueOf(departId));
                return deptEntityByDcId == null ? null : Lists.newArrayList(deptEntityByDcId.getFsDepId());


            case vice_departments:
               String viceDeptIds=null;
                if (sourceValue instanceof  List){
                    List<String> sourceList = (List<String>) sourceValue;
                    viceDeptIds=sourceList.get(0);
                }else{
                    viceDeptIds=String.valueOf(sourceValue);
                }
                OuterOaDepartmentBindEntity deptEntity = outerOaDepartmentBindManager.getDeptEntityByDcId(dataCenterId, null, String.valueOf(viceDeptIds));
                if(ObjectUtils.isNotEmpty(deptEntity)){
                    ObjectData objectData = objectDataManager.getObjectData(AccountTypeEnum.DEPT_BIND.getCode(), String.valueOf(deptEntity.getFsDepId()), fsEa);
                    if(ObjectUtils.isNotEmpty(objectData)){
                        Object deptParentPath = objectData.get("dept_parent_path");//获取对应上级部门
                        if(ObjectUtils.isNotEmpty(deptParentPath)){
                            List<String> viceDetIds = Splitter.on(".").splitToList(String.valueOf(deptParentPath));
                            return viceDetIds;
                        }
                    }
                }
                return null;

            default:
                return sourceValue;
        }
    }

}
