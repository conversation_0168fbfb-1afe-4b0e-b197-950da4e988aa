package com.facishare.open.erpdss.outer.oa.connector.base.outer.event.order;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import lombok.extern.slf4j.Slf4j;

/**
 * 保存订单事件处理器模板
 * 企业开通后，外部系统新增订单，需要实现这个模板类
 * <AUTHOR>
 * @date 2024-08-19
 */

@Slf4j
public abstract class SaveOrderHandlerTemplate extends HandlerTemplate {
    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        log.info("SaveOrderHandlerTemplate.execute,context={}",context);

        saveOrderAndAddCrmOrder(context);

        log.info("SaveOrderHandlerTemplate.execute,createAndSaveOrder,execute={}",context);


        return context.getResult();
    }

    /**
     * 保存外部订单数据并新建CRM订单
     * @param context
     */
    public abstract void saveOrderAndAddCrmOrder(MethodContext context);
}
