package com.facishare.open.erpdss.outer.oa.connector.base.inner.listener;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;

/**
 * 纷享组织架构变更监听器模板
 * <AUTHOR>
 * @date 2024-08-20
 */

public abstract class FsOrganizationChangedListenerTemplate {
    /**
     * 人员变更
     * @param context
     */
    public abstract void onEmployeeChanged(MethodContext context);

    /**
     * 部门变更
     * @param context
     */
    public abstract void onDepartmentChanged(MethodContext context);
}
