package com.facishare.open.erpdss.outer.oa.connector.base.inner.contacts.emp;

import com.facishare.open.erpdss.outer.oa.connector.base.HandlerTemplate;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.field_mapping.EmpAndDepFieldMappingTemplate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 新增纷享员工
 * <AUTHOR>
 * @date 2024-08-20
 */

@Slf4j
public abstract class AddFsEmpHandlerTemplate extends HandlerTemplate {
    @Resource
    private EmpAndDepFieldMappingTemplate empAndDepFieldMappingTemplate;

    @Override
    public TemplateResult execute(Object data) {
        MethodContext context = MethodContext.newInstance(data);

        if(empAndDepFieldMappingTemplate!=null) {
            empAndDepFieldMappingTemplate.empFieldMapping(context);
            log.info("AddFsEmpHandlerTemplate.execute,empFieldMapping,context={}",context);
            if(context.isError()) {
                return context.getResult();
            }
        }

        addEmp(context);
        log.info("AddFsEmpHandlerTemplate.execute,addEmp,context={}",context);

        return context.getResult();
    }

    /**
     * 新增员工
     * @param context
     */
    public abstract void addEmp(MethodContext context);
}
