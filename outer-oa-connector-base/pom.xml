<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-open-feishu-gateway</artifactId>
        <groupId>com.facishare.open</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>outer-oa-connector-base</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aspects</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.3.4</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>jedis-spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>config-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>spring-support</artifactId>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dubbo</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.javassist</groupId>
                    <artifactId>javassist</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--升级版本-->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.javassist</groupId>
            <artifactId>javassist</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>4.1.7.RELEASE</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>logconfig-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>metrics-oss</artifactId>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-rocketmq-support</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare.open</groupId>
            <artifactId>fs-oauth-base-api</artifactId>
            <version>0.0.31-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-crm-rest-api</artifactId>
            <version>2.0.8_erpdss-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-other-rest-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-paas-auth-rest-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-organization-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-dubbo-rest-plugin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-enterpriserelation-rest-api2</artifactId>
            <version>2.1.1-SNAPSHOT</version>
        </dependency>

        <!-- 消息通知和代办服务-->
        <dependency>
            <groupId>com.fxiaoke.msg</groupId>
            <artifactId>fs-message-external-platform-api</artifactId>
            <version>2.0.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.colin-lee</groupId>
            <artifactId>rpc-trace</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>biz-log-client</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-eye-j4log</artifactId>
                    <groupId>com.fxiaoke</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>java-utils</artifactId>
                    <groupId>com.fxiaoke.common</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>biz-log-proto</artifactId>
            <version>2.0.19</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>biz-log-client</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-redisson-support</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!--xxl-job框架-->
        <dependency>
            <groupId>com.fxiaoke.common</groupId>
            <artifactId>fs-job-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fxiaoke</groupId>
            <artifactId>fs-other-rest-api</artifactId>
            <version>1.0.6-SNAPSHOT</version>
        </dependency>

        <!--spock单元测试-->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <version>1.3-groovy-2.4</version>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-fsi-proxy</artifactId>
            <version>3.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-war-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <warName>outer-oa-connector-base</warName>-->
<!--                </configuration>-->
<!--            </plugin>-->
        </plugins>

    </build>

</project>
