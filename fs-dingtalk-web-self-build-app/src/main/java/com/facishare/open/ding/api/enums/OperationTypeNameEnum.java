package com.facishare.open.ding.api.enums;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2018/8/7 17:49
 */
public enum  OperationTypeNameEnum {
    /** 无操作 **/
    UNDO(0,"无操作"),

    /** 新增 **/
    ADD(1,"新增"),

    /** 修改 **/
    UPDATE(2,"修改"),

    /** 作废 **/
    INVALID(3,"作废"),

    /** 上架 **/
    ON_SHELF(4,"上架"),

    /** 下架 **/
    OFF_SHELF(5,"下架"),

    /** 删除 **/
    DELETE(6,"删除"),

    /** 新增产品单位 */
    ADD_PRODUCT_UNIT(7,"新增产品单位"),
    ;

    private int type;

    private String operation;

    OperationTypeNameEnum(int type,String operation) {
        this.type = type;
        this.operation = operation;
    }

    public int getType() {
        return type;
    }

    public static boolean isInvalid(Integer type) {
        if (type != null) {
            for (OperationTypeNameEnum operationTypeNameEnum : OperationTypeNameEnum.values()) {
                if (operationTypeNameEnum.getType() == type) {
                    return false;
                }
            }
        }
        return true;
    }

    public static String getOperationByType(int type) {
        for (OperationTypeNameEnum e : OperationTypeNameEnum.values()) {
            if (e.type == type) {
                return e.operation;
            }
        }
        return null;
    }
}
