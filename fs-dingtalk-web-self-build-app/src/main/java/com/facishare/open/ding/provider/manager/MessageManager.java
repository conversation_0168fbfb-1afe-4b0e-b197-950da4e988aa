package com.facishare.open.ding.provider.manager;

import com.facishare.open.ding.common.utils.UidUtil;
import com.facishare.open.msg.constant.MessageSendTypeEnum;
import com.facishare.open.msg.constant.MessageTypeEnum;
import com.facishare.open.msg.model.SendTextMessageVO;
import com.facishare.open.msg.result.MessageResult;
import com.facishare.open.msg.service.SendMessageService;
import com.github.autoconf.spring.reloadable.ReloadableProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by system on 2018/5/16.
 */
@Service
@Slf4j
public class MessageManager {

    @Autowired
    private AuthManager authManager;

    @Autowired
    private SendMessageService sendMessageService;

    //暂时未使用
    @ReloadableProperty("dingtalk.appId")
    private String appId;

    /**
     * 仅给应用管理员推送消息提示
     * @param enterpriseAccount
     * @param content
     */
    public void sendPromptMessage(String enterpriseAccount, String content) {
        List<Integer> adminIds = authManager.getAppAdmins(enterpriseAccount);
        if (CollectionUtils.isEmpty(adminIds)) {
            log.warn("couldn't find adminIds, enterpriseAccount={} content={}", enterpriseAccount, content);
            return;
        }

        sendMessage(enterpriseAccount, adminIds, content);
    }

    /**
     * 发送文本消息
     * @param enterpriseAccount
     * @param toUserList
     * @param content
     */
    private void sendMessage(String enterpriseAccount, List<Integer> toUserList, String content) {
        SendTextMessageVO vo = new SendTextMessageVO();
        vo.setPostId(UidUtil.getUid());
        vo.setAppId(appId);
        vo.setEnterpriseAccount(enterpriseAccount);
        vo.setToUserList(toUserList);
        vo.setType(MessageTypeEnum.TEXT);
        vo.setContent(content);

        // 批量推送
        MessageResult result = sendMessageService.sendTextMessage(vo, MessageSendTypeEnum.THIRD_PARTY_PUSH);
        if (!result.isSuccess()) {
            log.warn("sendMessage failed, arg={} result={}", vo, result);
        }
    }

}
