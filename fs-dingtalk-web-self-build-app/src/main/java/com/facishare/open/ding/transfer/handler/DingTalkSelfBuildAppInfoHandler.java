package com.facishare.open.ding.transfer.handler;

import com.alibaba.fastjson2.JSON;
import com.facishare.open.ding.provider.dao.DingEnterpriseDao;
import com.facishare.open.ding.provider.entity.DingEnterprise;
import com.facishare.open.ding.provider.utils.SecurityUtil;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaAppInfoEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaAppInfoManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaAppInfoMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.OuterOaAppInfoTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.params.DingtalkAppInfoParams;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 钉钉自建应用信息迁移处理器
 * <AUTHOR>
 * @date 2024/2/21 15:46:25
 */
@Component
public class DingTalkSelfBuildAppInfoHandler extends DingTalkSelfBuildHandler<DingEnterprise, OuterOaAppInfoEntity> {

    @Autowired
    private DingEnterpriseDao dingEnterpriseDao;

    @Autowired
    private OuterOaAppInfoMapper outerOaAppInfoMapper;

    @Autowired
    private OuterOaAppInfoManager outerOaAppInfoManager;

    @Override
    protected void update(int enterpriseId, DingEnterprise sourceData, OuterOaAppInfoEntity targetData) {
        final OuterOaAppInfoEntity entity = convert2AppInfoEntity(sourceData, targetData);
        outerOaAppInfoManager.upsert(entity);
    }

    private OuterOaAppInfoEntity convert2AppInfoEntity(DingEnterprise sourceData, OuterOaAppInfoEntity targetData) {
        if (Objects.nonNull(targetData) && checkDataEquals(sourceData, targetData)) {
            return null;
        }

        final DingtalkAppInfoParams appInfo = new DingtalkAppInfoParams();
        appInfo.setAgentId(sourceData.getAgentId());
        appInfo.setAppSecret(sourceData.getAppSecret());
        appInfo.setRedirectAppId(sourceData.getRedirectAppId());
        appInfo.setRedirectAppSecret(sourceData.getRedirectAppSecret());
        appInfo.setToken(sourceData.getToken());
        appInfo.setClientIp(sourceData.getClientIp());

        final OuterOaAppInfoEntity entity = new OuterOaAppInfoEntity();
        entity.setId(Objects.isNull(targetData) ? IdGenerator.get() : targetData.getId());
        entity.setChannel(ChannelEnum.dingding);
        entity.setOutEa(sourceData.getDingCorpId());
        entity.setAppId(SecurityUtil.decryptStr(sourceData.getAppKey()));
        entity.setAppType(OuterOaAppInfoTypeEnum.selfBuild);
        entity.setAppInfo(JSON.toJSONString(appInfo));
        entity.setStatus(OuterOaAppInfoStatusEnum.normal);
        entity.setCreateTime(Objects.isNull(targetData) ? sourceData.getCreateTime().getTime() : targetData.getCreateTime());
        entity.setUpdateTime(Objects.isNull(targetData) ? sourceData.getUpdateTime().getTime() : targetData.getUpdateTime());

        return entity;
    }

    @Override
    protected boolean checkDataEquals(DingEnterprise sourceData, OuterOaAppInfoEntity targetData) {
        if (targetData == null) {
            return false;
        }

        // First check basic fields
        if (!Objects.equals(targetData.getOutEa(), sourceData.getDingCorpId()) ||
                !Objects.equals(targetData.getAppType(), OuterOaAppInfoTypeEnum.selfBuild) ||
                !Objects.equals(targetData.getStatus(), OuterOaAppInfoStatusEnum.normal)) {
            return false;
        }

        // Now check appInfo fields after basic fields pass
        DingtalkAppInfoParams targetAppInfo = JSON.parseObject(targetData.getAppInfo(), DingtalkAppInfoParams.class);
        return checkEquals(sourceData, targetAppInfo);
    }

    private static boolean checkEquals(DingEnterprise sourceData, DingtalkAppInfoParams targetAppInfo) {
        if (targetAppInfo == null) {
            return false;
        }

        return Objects.equals(sourceData.getAgentId(), targetAppInfo.getAgentId()) &&
                Objects.equals(sourceData.getAppSecret(), targetAppInfo.getAppSecret()) &&
                Objects.equals(sourceData.getRedirectAppId(), targetAppInfo.getRedirectAppId()) &&
                Objects.equals(sourceData.getRedirectAppSecret(), targetAppInfo.getRedirectAppSecret()) &&
                Objects.equals(sourceData.getToken(), targetAppInfo.getToken()) &&
                Objects.equals(sourceData.getClientIp(), targetAppInfo.getClientIp());
    }

    @Override
    protected OuterOaAppInfoEntity getTargetData(int enterpriseId, DingEnterprise k) {
        if (StringUtils.isEmpty(k.getAppKey())) {
            return null;
        }
        return outerOaAppInfoMapper.getByAppIdAndChannel(ChannelEnum.dingding, OuterOaAppInfoTypeEnum.selfBuild, k.getAppKey());
    }

    @Override
    protected List<DingEnterprise> getSourceDataPage(Integer enterpriseId, DingEnterprise maxId) {
        if (Objects.nonNull(maxId)) {
            return new ArrayList<>();
        }
        final DingEnterprise byEI = dingEnterpriseDao.findByEI(enterpriseId);
        return byEI == null ? Lists.newArrayList() : Lists.newArrayList(decryptDingEnterprise(byEI));
    }

    private DingEnterprise decryptDingEnterprise(DingEnterprise dingEnterprise) {
        if (ObjectUtils.isEmpty(dingEnterprise)) {
            return dingEnterprise;
        }
        DingEnterprise copyDingEnterprise = new DingEnterprise();
        BeanUtils.copyProperties(dingEnterprise, copyDingEnterprise);
        copyDingEnterprise.setAppKey(SecurityUtil.decryptStr(dingEnterprise.getAppKey()));
        copyDingEnterprise.setAppSecret(SecurityUtil.decryptStr(dingEnterprise.getAppSecret()));
        copyDingEnterprise.setRedirectAppId(SecurityUtil.decryptStr(dingEnterprise.getRedirectAppId()));
        copyDingEnterprise.setRedirectAppSecret(SecurityUtil.decryptStr(dingEnterprise.getRedirectAppSecret()));
        copyDingEnterprise.setToken(SecurityUtil.decryptStr(dingEnterprise.getToken()));
        return copyDingEnterprise;
    }
} 