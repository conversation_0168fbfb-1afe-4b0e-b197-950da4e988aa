package com.facishare.open.ding.template.message;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.ExternalOaTodoService;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.DealTodoContextArg;
import com.facishare.open.ding.provider.constants.OAMessageTag;
import com.facishare.open.ding.provider.enums.DingTodoTypeEnum;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import com.facishare.open.ding.web.config.ConfigCenter;
import com.facishare.open.ding.web.utils.ObjectMapperUtils;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.msg.SendMsgHandlerTemplate;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.FunctionMsgTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.function.FunctionMsgBase;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024/12/12 15:20
 * @desc
 */
@Component
@Slf4j
public class DingSelfDealTodoTemplate extends SendMsgHandlerTemplate {
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;
    @Autowired
    private DingtalkManager dingtalkManager;
    @Autowired
    private ExternalOaTodoService externalOaTodoService;
    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;


    @Override
    protected void filterMsg(MethodContext context) {
        DealTodoContextArg dealTodoContextArg = context.getData();
        DealTodoArg dealTodoArg = dealTodoContextArg.getDealTodoArg();
        //TODO 多应用设计
        final DingEnterpriseResult dingEnterpriseResult = dealTodoContextArg.getDingEnterpriseResult();
        if(dingEnterpriseResult ==null){
            TemplateResult templateResult= TemplateResult.newErrorData(dingEnterpriseResult);
            context.setResult(templateResult);
        }
        String tenantId=String.valueOf(dealTodoArg.getEi());
        String functionName = ConfigCenter.functionMaps.get(tenantId);
        if(StringUtils.isBlank(functionName)){
            return;
        }
        FunctionMsgBase functionMsgBase=new FunctionMsgBase();
        functionMsgBase.setChannel(ChannelEnum.dingding.getEnumName());
        functionMsgBase.setFsEa(dingEnterpriseResult.getEa());
        functionMsgBase.setAppId(dingEnterpriseResult.getAppKey());
        functionMsgBase.setType(FunctionMsgTypeEnum.crmExternalMsgPush.getType());
        functionMsgBase.setOutEa(dingEnterpriseResult.getDingCorpId());
        functionMsgBase.setDataCenterId(outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, dingEnterpriseResult.getEa(), dingEnterpriseResult.getAppKey()));
        functionMsgBase.setEventType(OAMessageTag.DEAL_TO_DO_TAG);
        functionMsgBase.setData(JSONObject.toJSONString(dealTodoArg));
        Map<String, Object> objectMap = ObjectMapperUtils.objectToMap(functionMsgBase);
        Result<List<DingTodoTypeEnum>> booleanResult = dingtalkManager.filterMsg(functionName, objectMap, dealTodoArg.getEi());
        if(!booleanResult.isSuccess()){
            TemplateResult templateResult= TemplateResult.newErrorData(booleanResult.getData());
            context.setResult(templateResult);
        }
        if (CollectionUtils.isNotEmpty(booleanResult.getData())) {
            dealTodoContextArg.setNeedSupportTypes(booleanResult.getData());
        }
    }

    @Override
    public void buildMsg(MethodContext context) {

    }

    @Override
    public void sendMsg(MethodContext context) {
        DealTodoContextArg dealTodoContextArg = context.getData();
        DealTodoArg dealTodoArg=dealTodoContextArg.getDealTodoArg();
        //TODO 多应用设计
        final DingEnterpriseResult data = dealTodoContextArg.getDingEnterpriseResult();
        if(data ==null){
            TemplateResult templateResult= TemplateResult.newErrorData(data);
            context.setResult(templateResult);
        }
        List<DingTodoTypeEnum> dingTodoTypeEnums=dealTodoContextArg.getNeedSupportTypes();
        for (DingTodoTypeEnum dingTodoTypeEnum : dingTodoTypeEnums) {
            if(dingTodoTypeEnum.equals(DingTodoTypeEnum.DING_TODO)){
                dingtalkManager.dealTodo(dealTodoArg, data);
            }else {
                externalOaTodoService.dealTodo(dealTodoArg, data);
            }
        }

    }




}
