package com.facishare.open.ding.web.controller;

//import com.facishare.open.k3.cloud.api.enums.BusinessLogsStatusEnum;
import com.facishare.open.ding.api.result.BusinessLogsListResult;
import com.facishare.open.ding.api.service.AuthService;
import com.facishare.open.ding.common.result.PageObject;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.web.arg.BusinessLogsListArg;
import com.facishare.open.ding.web.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

//import com.facishare.open.k3.cloud.api.service.BusinessLogsService;
//import com.facishare.open.k3.cloud.api.vo.BusinessLogsListVo;

/**
 * Created by system on 2018/4/4.
 */
@Slf4j
@RestController
@RequestMapping("/businessLogs")
public class BusinessLogsController extends BaseController{

    @Autowired
    private AuthService authService;

//    @Autowired
//    private BusinessLogsService businessLogsService;

    @RequestMapping(value = "/list")
    public Result<PageObject<BusinessLogsListResult>> list(BusinessLogsListArg arg) {
        String fsUserAccount = arg.getFsUserId();
        Result<Boolean> appAdminResult = authService.isAppAdmin(fsUserAccount);
        if (!appAdminResult.isSuccess()) {
            return Result.newError(appAdminResult.getErrorCode(), appAdminResult.getErrorMessage());
        }

        if (!appAdminResult.getData()) {
            return Result.newError(ResultCode.NOT_APP_MANAGER);
        }

        if (arg.isInvalidPageNo()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        if (arg.isInvalidPageSize()) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        /*if (BusinessLogsStatusEnum.isInvalid(arg.getStatus())) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }*/

//        BusinessLogsListVo vo = BeanUtil.copy(arg, BusinessLogsListVo.class);
//        return businessLogsService.list(vo);
        return new Result();
    }

}
