package com.facishare.open.ding.api.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Created by system on 2018/4/12.
 */
@Data
public class AccountListResult implements Serializable {

    /** KIS带手机号的职员列表 **/
    private List<Account> accounts;

    @Data
    public static class Account implements Serializable {

        /** KIS职员内码ID **/
        private String itemId;

        /** 职员名称 **/
        private String fullName;

        /** 职员性别：1、男；0，女 **/
        private Integer gender;

        /** 职员手机号 **/
        private String mobile;

        /** 职员所在部门内码ID **/
        private Integer departmentId;

        /** 禁用状态 0使用中 1禁用 **/
        private Integer status;

    }

}
