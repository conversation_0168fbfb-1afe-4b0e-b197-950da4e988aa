package com.facishare.open.ding.transfer.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.provider.dao.DingMappingEmployeeDao;
import com.facishare.open.ding.provider.entity.DingMappingEmployee;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.postgresql.util.PSQLException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 钉钉自建应用员工绑定数据迁移处理器 - PG
 * <AUTHOR>
 * @date 2024/2/21 15:46:25
 */
@Component
@Slf4j
public class DingTalkSelfBuildEmployeeHandler extends DingTalkSelfBuildHandler<DingMappingEmployee, List<OuterOaEmployeeBindEntity>> {

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private DingMappingEmployeeDao dingMappingEmployeeDao;

    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Override
    protected void transferAfter(int enterpriseId) throws Throwable {
        appAuthEntityCache.invalidate(enterpriseId);
    }

    @Override
    protected void update(int enterpriseId, DingMappingEmployee sourceData, List<OuterOaEmployeeBindEntity> targetData) {
        final Map<String, OuterOaEmployeeBindEntity> collect = targetData.stream().collect(Collectors.toMap(OuterOaEmployeeBindEntity::getAppId, Function.identity()));
        final List<OuterOaEnterpriseBindEntity> list = appAuthEntityCache.get(sourceData.getEi());
        final List<OuterOaEmployeeBindEntity> collect1 = list.stream().map(entity -> {
                    return convert2OuterOaEmployeeBindEntity(sourceData, collect.get(entity.getAppId()), entity);
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        try {
            outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(collect1));
        } catch (Exception e) {
            if (e.getMessage().contains("duplicate key value violates unique constraint ")) {
                log.warn("DingTalkSelfBuildEmployeeHandler update error", e);
            }
        }
    }

    private OuterOaEmployeeBindEntity convert2OuterOaEmployeeBindEntity(DingMappingEmployee sourceData, OuterOaEmployeeBindEntity target, OuterOaEnterpriseBindEntity entity) {
        final String fsEmpId = String.valueOf(sourceData.getEmployeeId());
        final String outEmpId = sourceData.getDingEmployeeId();
        if (checkEquals(target, outEmpId)) {
            return null;
        }

        final OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
        bindEntity.setId(Objects.isNull(target) ? IdGenerator.get() : target.getId());
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(eieaConverter.enterpriseIdToAccount(sourceData.getEi()));
        bindEntity.setOutEa(entity.getOutEa());
        bindEntity.setAppId(entity.getAppId());
        bindEntity.setDcId(entity.getId());
        bindEntity.setFsEmpId(fsEmpId);
        bindEntity.setOutEmpId(outEmpId);
        bindEntity.setBindStatus(BindStatusEnum.convertDingtalkEmployeeBindStatus(sourceData.getBindStatus()));
        bindEntity.setCreateTime(Objects.isNull(target) ? sourceData.getCreateTime().getTime() : target.getCreateTime());
        bindEntity.setUpdateTime(Objects.isNull(target) ? sourceData.getUpdateTime().getTime() : target.getUpdateTime());

        return bindEntity;
    }

    @Override
    protected boolean checkDataEquals(DingMappingEmployee sourceData, List<OuterOaEmployeeBindEntity> targetData) throws Throwable {
        final Set<String> appIds;
        if (CollectionUtils.isEmpty(targetData) || targetData.size() < (appIds = appAuthEntityCache.get(sourceData.getEi()).stream().map(OuterOaEnterpriseBindEntity::getAppId).collect(Collectors.toSet())).size()) {
            return false;
        }

        final Set<String> newAppIds = targetData.stream().map(OuterOaEmployeeBindEntity::getAppId).collect(Collectors.toSet());
        if (!newAppIds.containsAll(appIds)) {
            return false;
        }

        final String empId = sourceData.getDingEmployeeId();
        return targetData.stream().allMatch(entity ->
                checkEquals(entity, empId));
    }

    private static boolean checkEquals(OuterOaEmployeeBindEntity entity, String outEmpId) {
        return Objects.nonNull(entity) && Objects.equals(entity.getBindStatus(), BindStatusEnum.normal) &&
                Objects.equals(entity.getOutEmpId(), outEmpId);
    }

    @Override
    protected List<OuterOaEmployeeBindEntity> getTargetData(int enterpriseId, DingMappingEmployee k) {
        final OuterOaEmployeeBindEntity entity = new OuterOaEmployeeBindEntity();
        entity.setChannel(ChannelEnum.dingding);
        entity.setFsEa(eieaConverter.enterpriseIdToAccount(enterpriseId));
        entity.setFsEmpId(String.valueOf(k.getEmployeeId()));
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> lambdaQueryWrapper = Wrappers.lambdaQuery(entity);
        return outerOaEmployeeBindMapper.selectList(lambdaQueryWrapper);
    }



    @Override
    protected List<DingMappingEmployee> getSourceDataPage(Integer enterpriseId, DingMappingEmployee maxId) {
        Long id = Objects.isNull(maxId) ? null : maxId.getId();
        final List<DingMappingEmployee> dingMappingEmployees = dingMappingEmployeeDao.pageNormalById(enterpriseId, id, 1000);
        return dingMappingEmployees;
    }

    @Override
    public int getThreadNum() {
        return 10;
    }
}