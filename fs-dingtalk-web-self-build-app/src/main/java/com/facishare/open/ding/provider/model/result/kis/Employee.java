package com.facishare.open.ding.provider.model.result.kis;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * KIS职员信息
 * Created by system on 2018/5/18.
 */
@Data
public class Employee {

    /** KIS职员内码ID **/
    @SerializedName(value = "ItemID")
    private String itemId;

    /** 职员名称 **/
    @SerializedName(value = "FullName")
    private String fullName;

    /** 职员性别：1、男；0，女 **/
    @SerializedName(value = "Gender")
    private Integer gender;

    /** 职员手机号 **/
    @SerializedName(value = "Mobile")
    private String mobile;

    /** 职员所在部门内码ID **/
    @SerializedName(value = "DeptID")
    private Integer departmentId;

    /** 禁用状态 0使用中 1禁用 **/
    @SerializedName(value = "Status")
    private Integer status;

}
