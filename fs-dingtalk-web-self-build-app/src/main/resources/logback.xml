<configuration scan="false" debug="false">

    <appender name="INFO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${catalina.home}/logs/fs-ding-web.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <FileNamePattern>${catalina.home}/logs/fs-ding-web-%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
            <maxHistory>30</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
                java.lang.Thread,
                javassist,
                sun.reflect,
                org.springframework,
                org.apache,
                org.eclipse.jetty,
                $Proxy,
                java.net,
                java.io,
                javax.servlet,
                org.junit,
                com.mysql,
                com.sun,
                org.mybatis.spring,
                cglib,
                CGLIB,
                java.util.concurrent,
                okhttp,
                org.jboss,
                }%n
            </pattern>
        </encoder>
    </appender>

    <!-- 异步输出日志避免阻塞服务 -->
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>512</queueSize>
        <appender-ref ref="INFO_FILE"/>
    </appender>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%n</pattern>
        </encoder>
    </appender>

    <logger name="com.facishare" level="INFO" additivity="false">
        <appender-ref ref="ASYNC" />
        <!--<appender-ref ref="CONSOLE" />-->
    </logger>

    <logger name="com.github.trace.listener" level="WARN" additivity="false">
        <appender-ref ref="ASYNC" />
        <!--<appender-ref ref="CONSOLE"/>-->
    </logger>
    <logger name="com.alibaba.dubbo" level="INFO" additivity="false">
        <appender-ref ref="ASYNC" />
        <!--<appender-ref ref="CONSOLE"/>-->
    </logger>

    <!-- 限制第三方jar  日志输出  -->
    <logger name="druid.sql" level="WARN"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.apache" level="WARN"/>
    <logger name="com.alibaba" level="WARN"/>
    <logger name="com.github" level="WARN"/>
    <logger name="com.fxiaoke" level="WARN"/>
    <logger name="com.facishare.fcp" level="WARN"/>

    <root level="INFO">
        <appender-ref ref="ASYNC"/>
    </root>

    <conversionRule conversionWord="msg" converterClass="com.fxiaoke.metrics.logback.MaskMessageConverter"/>
</configuration>
