package service;

import base.BaseAbstractTest;
import com.facishare.open.ding.api.result.BindFxEaResult;
import com.facishare.open.ding.api.result.BindFxUserResult;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.service.ExternalMessageService;
import com.facishare.open.ding.api.service.ExternalOaTodoService;
import com.facishare.open.ding.api.service.ObjectMappingService;
import com.facishare.open.ding.api.vo.DingTaskVo;
import com.facishare.open.ding.api.vo.EmployeeVo;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.arg.SendTextCardContextArg;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingTodoManager;
import com.facishare.open.ding.provider.manager.DingtalkManager;
import com.facishare.open.ding.provider.service.ObjectMappingServiceImpl;
import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import com.fxiaoke.message.extrnal.platform.model.arg.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2019-09-04 15:47
 */
@Slf4j
public class SyncMessageServiceTest extends BaseAbstractTest {
    @Autowired
    private ExternalMessageService messageService;
    @Autowired
    private ObjectMappingService objectMappingService;
    @Autowired
    private ObjectMappingServiceImpl objectMappingServices;
    @Autowired
    private ExternalOaTodoService externalOaTodoService;
    @Autowired
    private DingTodoManager dingTodoManager;
    @Autowired
    private DingtalkManager dingtalkManager;
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Test
    public void testSyncMessage(){
//        SendTextCardMessageArg arg = new SendTextCardMessageArg();
//        arg.setEi(83384);
//        arg.setEa("83384");
//        arg.setReceiverIds(Lists.newArrayList(1000, 1016));
//        arg.setMessageContent("钉钉bi文件");
//        arg.setTitle("钉钉bi文件1");
//        arg.setUrl("ava-bi-message?id=BI_64101b00e3c8c10001346bb3&diagram=1&from=subscription&dataType=2");
//        arg.setGenerateUrlType(4);
//        messageService.sendTextCardMessage(arg);


        SendTextCardMessageArg arg = new SendTextCardMessageArg();
        arg.setEi(83384);
        arg.setEa("83384");
        arg.setReceiverIds(Lists.newArrayList(1000, 1016));
        arg.setMessageContent("业务流程结束提醒：业务流程: 问题解决类【工单】业务流程  2024-05-08 16:19 已结束。");
        arg.setTitle("团队成员变更");
        arg.setAppId("test_app_id");
        Map<String, String> extraDataMap = new HashMap<>();
        extraDataMap.put("filePath", "N_202304_04_13617dee9fd24105b7b388fcd81ca0f5.xlsx");
        arg.setExtraDataMap(extraDataMap);
        arg.setForm(Lists.newArrayList(new KeyValueItem("流程主题", "zongxin chen"), new KeyValueItem("姓名", "王一三"), new KeyValueItem("这是什么", "发士大夫士大夫")));
        arg.setUrl("https://www.ceshi112.com/dps/preview/bypath?path=N_202304_04_13617dee9fd24105b7b388fcd81ca0f5.xlsx&showHeader=1");
        arg.setGenerateUrlType(5);

        SendTextCardContextArg sendTextCardContextArg = new SendTextCardContextArg();
        sendTextCardContextArg.setSendTextCardMessageArg(arg);
        messageService.sendTextCardMessage(sendTextCardContextArg);

        DingTaskVo dingTaskVo = new DingTaskVo();
        dingTaskVo.setEi(83384);
        dingTaskVo.setEmployeeId(1000);
        dingTaskVo.setSourceId("test_source_id");
        dingTaskVo.setTaskId("test_task_id");
        dingTaskVo.setStatus(1);
        dingTaskVo.setCreatorEmployeeId(1000);
        dingTaskVo.setDataCenterId("test_dc_id");
        dingTaskVo.setMessageType("DING_TODO");
        dingTaskVo.setDingEmployeeId("test_ding_emp_id");
        int update = dingTodoManager.insertSource(dingTaskVo, "test_app_id");
    }

    @Test
    public void sendTextMessage(){
        SendTextMessageArg arg = new SendTextMessageArg();
        arg.setGenerateUrlType(1);
        arg.setEa("83384");
        arg.setEi(83384);
        arg.setMessageContent("直接发送文本消息1");
        arg.setReceiverChannelData("{\"appId\":\"crmNotify1\"}");
        arg.setReceiverIds(Lists.newArrayList(1000));
        messageService.sendTextMessage(arg);
    }

    @Test
    public void testMessage(){
        SendTextCardMessageArg arg=null;
        arg= Optional.ofNullable(arg).orElseGet(()->new SendTextCardMessageArg());
    }


    @Test
    public void testGetBindFxEmp(){
        Result<List<BindFxUserResult>> result = objectMappingService.getBindEiAndUser(0,5, null);

    }

    @Test
    public void testGetBindFxEa(){
        Result<List<BindFxEaResult>> result = objectMappingService.getBindEa(0,5, null);

    }

    @Test
    public void testNew(){
        Result s = objectMappingServices.initDingEmployee(83384, 1006, null);
        log.info(s.getErrorDescription());
        log.info(s.getErrorMessage());
    }

    @Test
    public void testCreate(){
        EmployeeVo vo = new EmployeeVo();
        vo.setName("18845112075");
        vo.setMobile("18845112075");
        vo.setDingEmployeeId("manager1076");
        vo.setUpdateBy(1000);
        vo.setEi(83384);

        Result s = objectMappingServices.createFxEmployee(vo, null);
    }

    @Test
    public void testCreateToDo(){
        CreateTodoArg arg = new CreateTodoArg();
        arg.setEa("83384");
        arg.setEi(83384);
        arg.setContent("代办任务1000061");
        arg.setTitle("待处理的CRM审批流程");
        arg.setSourceId("0047");
        List<Integer> list = new LinkedList<>();
//        list.add(1044);
//        list.add(1000);
        list.add(1042);
//        list.add(1009);
        //list.add(1000);
       // list.add(1010);
        arg.setReceiverIds(list);
        arg.setForm(Lists.newArrayList(new KeyValueItem("流程主题", "zongxin chen"), new KeyValueItem("姓名", "王一三"), new KeyValueItem("不能出现在标题中", "不能出现在标题中")));
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseInfoByEi(83384, null);

        dingtalkManager.createTodo(arg, enterpriseResult.getData());
//        dingtalkManager.createTodo(arg);
//        dingtalkManager.createTodo(arg);
    }

    @Test
    public void testSQL(){
        int ei = 83384;
        List<Integer> list = new LinkedList<>();
        list.add(1006);
        List<String> strings = dingTodoManager.queryUserIds(ei, list, "test_app_id");
        System.out.println(strings);
    }

    @Test
    public void testUpdateSQL(){

        DingTaskVo dingTaskVo = new DingTaskVo();
        dingTaskVo.setEi(83384);
        dingTaskVo.setEmployeeId(1006);
        dingTaskVo.setSourceId("123456");
        dingTaskVo.setEmployeeSourceId("1234561006");
        dingTaskVo.setCreatorEmployeeId(1006);
        dingTaskVo.setTaskId("122");
        int update = dingTodoManager.insertSource(dingTaskVo, "test_app_id");
        System.out.println(update);
    }

    @Test
    public void testHttps(){
        int ei = 83384;
        List<Integer> list = new LinkedList<>();
        list.add(1006);
        List<String> strings = dingTodoManager.queryUserIds(ei, list, "test_app_id");
        System.out.println(strings);
    }


    @Test
    public void testDeal(){
        DealTodoArg vag = new DealTodoArg();
        vag.setEa("83384");
        vag.setEi(83384);
        vag.setSourceId("0047");
        vag.setOperators(Lists.newArrayList(1044, 10001, 10421, 45454));
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseInfoByEi(83384, null);
        dingtalkManager.dealTodo(vag, enterpriseResult.getData());
    }

    @Test
    public void testDelete(){
        DeleteTodoArg vag = new DeleteTodoArg();
        vag.setEa("83384");
        vag.setEi(83384);
        vag.setSourceId("66bc58c68634216a9dfa64be");
        vag.setDeleteEmployeeIds(Lists.newArrayList(1073));
        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseInfoByEi(83384, null);
        dingtalkManager.deleteTodo(vag, enterpriseResult.getData());
    }

    @Test
    public void testUtils(){
        List<String> s = new ArrayList<>();
        s.add("1");
        s.add("2");
        List<String> s1 = new ArrayList<>();
        BeanUtils.copyProperties(s1, s);
        System.out.println(s1);
    }

//    @Autowired
//    private DingToDoMessageListener dingToDoMessageListener;
//
//
//    @Test
//    public void test() {
//        Boolean allowBizType = dingToDoMessageListener.isAllowBizType("452");
//        System.out.println(allowBizType);
//        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseInfoByEi(83384, null);
//        Boolean pushTodo = dingToDoMessageListener.isPushTodo(enterpriseResult.getData(), DingTodoTypeEnum.DING_TODO, OAMessageTag.CREATE_TO_DO_TAG, "");
//        System.out.println(pushTodo);
//        Boolean pushTodo1 = dingToDoMessageListener.isPushTodo(enterpriseResult.getData(), DingTodoTypeEnum.DING_WORK, OAMessageTag.CREATE_TO_DO_TAG, "");
//        System.out.println(pushTodo1);
//    }
}
