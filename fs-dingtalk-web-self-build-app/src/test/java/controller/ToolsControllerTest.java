package controller;

import base.BaseAbstractTest;
import com.facishare.open.ding.web.controller.ToolsController;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.junit.Test;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

@Slf4j
public class ToolsControllerTest extends BaseAbstractTest {
    @Resource
    private ToolsController toolsController;

    @Test
    public void updateDeptBind() throws FileNotFoundException {
        String fileName = "D:\\fxiaoke\\dingding\\excel\\employee\\employeeBind.xlsx";
        File picFile = new File(fileName);
        FileInputStream fileInputStream = new FileInputStream(picFile);
        MultipartFile multipartFile = null;
        try {
            multipartFile = new MockMultipartFile(picFile.getName(), picFile.getName(),
                    ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
            toolsController.bindEmpByExcel(null, null, multipartFile);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void fixTodo() throws Exception {
        toolsController.fixTodo("91450", 0L, System.currentTimeMillis(), false);
    }
}

