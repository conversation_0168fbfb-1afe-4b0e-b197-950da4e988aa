package job;

import base.BaseAbstractTest;
import com.facishare.open.ding.web.job.ProxyLiveHandler;
import org.junit.Test;

import javax.annotation.Resource;

public class ProxyLiveHandlerTest extends BaseAbstractTest {
    @Resource
    private ProxyLiveHandler proxyLiveHandler;

    @Test
    public void execute() {
        try {
            proxyLiveHandler.execute(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
