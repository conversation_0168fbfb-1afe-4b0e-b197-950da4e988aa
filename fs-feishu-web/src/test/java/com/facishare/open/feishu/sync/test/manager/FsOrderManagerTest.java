package com.facishare.open.feishu.sync.test.manager;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.sync.manager.FsOrderManager;
import com.facishare.open.feishu.sync.test.BaseTest;
import com.facishare.open.feishu.web.template.FeishuOuterEventHandlerTemplate;
import com.facishare.paas.license.pojo.ModuleFlag;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

public class FsOrderManagerTest extends BaseTest {
    @Resource
    private FsOrderManager fsOrderManager;
    @Autowired
    private FeishuOuterEventHandlerTemplate feishuOuterEventHandlerTemplate;

    @Test
    public void judgeFeishuConnectorModule() {
        MethodContext methodContext=new MethodContext();
        String data="{\"encrypt\":\"YEczepOTPmUwzHA1JzlNkasCb7V77+ijSLJAsQDjG9fcIGwkw/J8GWf6OUz2cES6U5EEo3ByftA8zYfVHf5PnJR1mGlWnnldFd5zPXzipvbbvBhOWA3jiJ942OW4/Irv4NVLlCUNZYAcOS4kZCqdbwS3gHJC8YN25+X9Be8kwj8p+rh/yJDmUTqbkAHSfxd2lRYLO282o2ei7nV+LefJq7YXGSYIGaQeG+nsWPk95i1CO/wlILBGXMNN9QSjoC1xG/UO+86rQS2P3eWeZqjmHRbT+OVKxjP4zEFCXXp8xyFECUm4nnvlpVSJVH3jioOaDFrmoKcKaQBlGgnmWmHxG586OHMOJmLyuc+v5MK4ijWr64LpOld+FL5EIvgNZ4nR/6cAKRvUrKZXDNVzIBLe6has0o+wPAwyzRBlj18jnmZel5vX4ejMeAaAHwI0KB8zDKqAAl0VQoxftvVSQdcd/RgHvURdzUvxEYRZHf3eiBdL5SUrdnvGEms6f3pp/0zLNSi8HzYgGaz7/0ADvBFSFmeN0nAWTQjp0zPGjG70OVX616O/nBnUBWgpupCNakgNSBVSMg/mNjFVdHgBslOWpprujflpz7XJt7Kr3OCYboeKSLtdZ2/ErYUSwR2Iplbi\"}";
        methodContext.setData(data);
         feishuOuterEventHandlerTemplate.execute(data).getDataOrMsg();
    }

    @Test
    public void testOrderData(){
        String dataOrder="{\"tenant_key\":\"1b67c991129fd740\",\"buy_type\":\"buy\",\"create_time\":\"1744978748\",\"src_order_id\":\"\",\"buy_count\":1,\"type\":\"order_paid\",\"seats\":0,\"pay_time\":\"1744978748\",\"order_pay_price\":0,\"price_plan_id\":\"price_a7e8c5803b56500d\",\"price_plan_type\":\"trial\",\"app_id\":\"cli_a20192f6afb8d00c\",\"order_id\":\"7494624514269052931\"}";
        ModuleFlag moduleFlag=fsOrderManager.judgeFeishuConnectorModule("1b67c991129fd740");
    }
}
