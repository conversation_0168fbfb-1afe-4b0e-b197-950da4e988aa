use `fs-feishu-db`;
create table if not exists tb_app_info
(
id int auto_increment primary key comment '主键ID',
app_id varchar(64) not null comment '应用ID',
tenant_key varchar(128) not null comment '开通应用的企业唯一标识',
status varchar(64) not null default 'start_by_tenant' comment '应用状态 start_by_tenant: 租户启用; stop_by_tenant: 租户停用; stop_by_platform: 平台停用',
applicants varchar(256) not null comment '应用的申请者，可能有多个,json数据',
installer_open_id varchar(64) comment '当应用被管理员安装时，返回此字段。如果是自动安装或由普通成员获取时，没有此字段',
installer_employee_open_id varchar(64) comment '当应用被普通成员安装时，返回此字段',
operator varchar(256) comment '仅status=start_by_tenant时有此字段,json数据',
create_time timestamp default current_timestamp comment '创建时间',
update_time timestamp default current_timestamp on update current_timestamp comment '更新时间'
) comment '应用信息表';