package com.facishare.open.feishu.sync.manager;

import com.facishare.open.oa.base.dbproxy.mongo.dao.OaConnectorOutDepartmentInfoMongoDao;
import com.facishare.open.oa.base.dbproxy.mongo.document.OaConnectorOutDepartmentInfoDoc;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component("oaConnectorOutDepartmentInfoManager")
@Deprecated
public class OaConnectorOutDepartmentInfoManager {
    @Autowired
    private OaConnectorOutDepartmentInfoMongoDao oaConnectorOutDepartmentInfoMongoDao;

    public BulkWriteResult batchReplace(List<OaConnectorOutDepartmentInfoDoc> docs) {
        BulkWriteResult bulkWriteResult = oaConnectorOutDepartmentInfoMongoDao.batchReplace(docs);
        return bulkWriteResult;
    }

    public DeleteResult deleteDepartmentInfoByUserId(ChannelEnum channel, String outEa, String outDepartmentId) {
        DeleteResult deleteResult = oaConnectorOutDepartmentInfoMongoDao.deleteDepartmentInfoByUserId(channel, outEa, null, outDepartmentId);
        return deleteResult;
    }

    public List<OaConnectorOutDepartmentInfoDoc> queryDepartmentInfos(ChannelEnum channel, String outEa) {
        List<OaConnectorOutDepartmentInfoDoc> docs = oaConnectorOutDepartmentInfoMongoDao.queryDepartmentInfos(channel, outEa, null);
        return docs;
    }

    public List<OaConnectorOutDepartmentInfoDoc> queryDepartmentInfosByIds(ChannelEnum channel, String outEa, List<String> outDepartmentIds) {
        List<OaConnectorOutDepartmentInfoDoc> docs = oaConnectorOutDepartmentInfoMongoDao.queryDepartmentInfosByIds(channel, outEa, null, outDepartmentIds);
        return docs;
    }

    public DeleteResult deleteNotInCollectionDocs(ChannelEnum channel, String outEa,String appId, List<OaConnectorOutDepartmentInfoDoc> docs) {
        DeleteResult deleteResult = oaConnectorOutDepartmentInfoMongoDao.deleteNotInCollectionDocs(channel, outEa, appId, docs);
        return deleteResult;
    }

    public Long countDocuments(ChannelEnum channel, String outEa) {
        Long counts = oaConnectorOutDepartmentInfoMongoDao.countDocuments(channel, outEa, null);
        return counts;
    }
}
