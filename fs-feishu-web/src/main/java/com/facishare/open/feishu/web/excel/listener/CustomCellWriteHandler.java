package com.facishare.open.feishu.web.excel.listener;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;

import java.util.List;

@Slf4j
public class CustomCellWriteHandler implements CellWriteHandler {
    private I18NStringManager i18NStringManager;
    private String tenantId;
    private String lang;


    public CustomCellWriteHandler(I18NStringManager i18NStringManager, String tenantId, String lang) {
        this.i18NStringManager = i18NStringManager;
        this.tenantId = tenantId;
        this.lang = lang;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder,
                                 WriteTableHolder writeTableHolder,
                                 Row row,
                                 Head head,
                                 Integer integer,
                                 Integer integer1,
                                 Boolean aBoolean) {
        if(head!=null) {
            List<String> headNameList = head.getHeadNameList();
            if(CollectionUtils.isNotEmpty(headNameList)) {
                //多级表头需要遍历
                for(int i = 0; i < headNameList.size(); i++) {
                    String i18nKey = headNameList.get(i);
                    log.info("CustomCellWriteHandler.beforeCellCreate,i18nKey={}",i18nKey);
                    headNameList.set(i,i18NStringManager.get(i18nKey,lang,tenantId,i18nKey));
                }
            }
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder,
                                WriteTableHolder writeTableHolder,
                                Cell cell,
                                Head head,
                                Integer integer,
                                Boolean aBoolean) {

    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder,
                                       WriteTableHolder writeTableHolder,
                                       CellData cellData,
                                       Cell cell,
                                       Head head,
                                       Integer integer,
                                       Boolean aBoolean) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder,
                                 WriteTableHolder writeTableHolder,
                                 List<CellData> list,
                                 Cell cell,
                                 Head head,
                                 Integer integer,
                                 Boolean aBoolean) {
    }
}
