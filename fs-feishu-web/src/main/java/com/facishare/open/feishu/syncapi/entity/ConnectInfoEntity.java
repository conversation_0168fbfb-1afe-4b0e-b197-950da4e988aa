package com.facishare.open.feishu.syncapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("tb_connect_info")
public class ConnectInfoEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 纷享EA
     */
    private String fsEa;
    /**
     * 连接器名称
     */
    private String connectorName;
    /**
     * 企业ID,飞书对应的是displayId
     */
    private String displayId;

    private Date createTime;
    private Date updateTime;
}
