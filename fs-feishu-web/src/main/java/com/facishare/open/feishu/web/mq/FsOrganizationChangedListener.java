package com.facishare.open.feishu.web.mq;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.feishu.web.template.inner.listener.FeishuFsOrganizationChangedListenerTemplate;
import com.facishare.organization.api.event.OrganizationChangedListener;
import com.facishare.organization.api.event.organizationChangeEvent.DepartmentChangeEvent;
import com.facishare.organization.api.event.organizationChangeEvent.EmployeeChangeEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * 监听组织架构变化，缓存数据
 * <AUTHOR>
 * @Version 1.0
 */
@Slf4j
@Component("fsOrganizationChangedListener")
public class FsOrganizationChangedListener extends OrganizationChangedListener {
    @Resource
    private FeishuFsOrganizationChangedListenerTemplate feishuFsOrganizationChangedListenerTemplate;

    public FsOrganizationChangedListener() {
        super("fs-feishu-organization-changed-mq-config");
    }

    @Override
    protected void onEmployeeChanged(EmployeeChangeEvent event) {
        log.info("FsOrganizationChangedListener.onEmployeeChanged,event={}",event);
        MethodContext context = MethodContext.newInstance(event);
        feishuFsOrganizationChangedListenerTemplate.onEmployeeChanged(context);
    }

    @Override
    protected void onDepartmentChanged(DepartmentChangeEvent event) {
        log.info("FsOrganizationChangedListener.onDepartmentChanged,event={}",event);
        MethodContext context = MethodContext.newInstance(event);
        feishuFsOrganizationChangedListenerTemplate.onDepartmentChanged(context);
    }
}
