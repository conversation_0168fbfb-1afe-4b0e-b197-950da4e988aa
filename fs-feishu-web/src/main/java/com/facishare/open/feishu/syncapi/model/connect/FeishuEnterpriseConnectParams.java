package com.facishare.open.feishu.syncapi.model.connect;

import com.facishare.open.feishu.syncapi.consts.Constant;
import com.facishare.open.outer.oa.connector.common.api.info.BaseConnectParams;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @create 2024/11/24 15:00
 * @desc
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class FeishuEnterpriseConnectParams extends BaseConnectParams {

    /**
     * 请求域名
     */
    private String baseUrl;
    /**
     * 请求密钥
     */
    private String encryKey;
    /**
     * 自动开启绑定的字段
     */
    private String autoField;

    /**
     * 是不是开启自动绑定
     */
    private Integer isAutoBind;
    /**
     * 留资使用
     * @return
     */
    private Boolean isFirstLand;

    /** //拓展字段，1、这里取留资是否首次使用crm，字段：isFirstLand。2、是否已经留资,字段：isRetainInformation。
     * 留资使用
     * @return
     */
    private Boolean isRetainInformation;

    @Override
    public String getAutoField() {
        if(StringUtils.isEmpty(autoField)){
            return Constant.AUTO_BIND_FIELD_EMPNUM;
        }
        return autoField;
    }
}
