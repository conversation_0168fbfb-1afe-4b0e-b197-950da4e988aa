package com.facishare.open.feishu.sync.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.facishare.open.feishu.sync.manager.FeishuAppManager;
import com.facishare.open.feishu.sync.manager.UrlManager;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.order.contacts.proxy.api.enums.FeishuUrlEnum;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.data.QueryTenantInfoData;
import com.facishare.open.feishu.syncapi.service.FeishuTenantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;

@Service("feishuTenantService")
public class FeishuTenantServiceImpl implements FeishuTenantService {
    @Resource
    private ProxyHttpClient proxyHttpClient;
    @Resource
    private FeishuAppManager feishuAppManager;
    @Autowired
    private UrlManager urlManager;

    /**
     * 查询飞书企业信息，应用只有在启用状态下，才能查询到数据，如果应用被停用，这个接口，是查询不到数据的
     * @param appId
     * @param tenantKey
     * @return
     */
    @Override
    public Result<QueryTenantInfoData> queryTenantInfo(String appId, String tenantKey) {
        String url = urlManager.getFeishuUrl(tenantKey, appId, FeishuUrlEnum.tenant_v2_tenant_query.getUrl());
        Result<String> tenantAccessToken = feishuAppManager.getTenantAccessToken(appId, tenantKey);
        if(tenantAccessToken.isSuccess()==false) {
            return Result.newError(tenantAccessToken.getCode(),tenantAccessToken.getMsg());
        }

        Result<QueryTenantInfoData> result = getQueryTenantInfoDataResult(appId, tenantKey, url, tenantAccessToken);
        return result;
    }

    @Override
    public Result<QueryTenantInfoData> validTenantInfo(String appId, String appSecret) {

        return null;
    }

    private Result<QueryTenantInfoData> getQueryTenantInfoDataResult(String appId, String tenantKey, String url, Result<String> tenantAccessToken) {
        HashMap<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", "Bearer " + tenantAccessToken.getData());
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimit, String.format(FeishuUrlEnum.tenant_v2_tenant_query.getName(), appId, tenantKey));
        headerMap.put(com.facishare.open.order.contacts.proxy.api.consts.GlobalValue.outLimitTimes, ConfigCenter.OUT_LIMIT_TIMES);
        Result<QueryTenantInfoData> result = proxyHttpClient.getUrl(url, headerMap, new TypeReference<Result<QueryTenantInfoData>>() {
        });
        return result;
    }

    @Override
    public Result<QueryTenantInfoData> initQueryTenantInfo(String appId, String appSecret, String domain) {
        String feishuUrlByPointDomain = urlManager.getFeishuUrlByPointDomain(FeishuUrlEnum.tenant_v2_tenant_query.getUrl(), domain);
        Result<String> stringResult = feishuAppManager.initTenantAccessToken(appId, null, appSecret, domain);
        Result<QueryTenantInfoData> queryTenantInfoDataResult = getQueryTenantInfoDataResult(appId, null, feishuUrlByPointDomain, stringResult);
        return queryTenantInfoDataResult;
    }
}
