package com.facishare.open.feishu.sync.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/3/25 11:21
 * @Version 1.0
 */
@Data
public class MessageResult implements Serializable {
    private String fsEa;
    private String messageId;
    private String from;
    private List<String> toList;
    private Long messageTime;//消息发送时间戳
    private String messageType;//消息类型
    private String content;//消息内容。存储文本
    private String md5sum;//对于图片，视频.音频的作为名字
    private String sdkFileId;//资源id，解密资源需要
    private String fileSize;
    private String npath;
    private String fileName;//文件名
    private String fileExt;//拓展名

}
