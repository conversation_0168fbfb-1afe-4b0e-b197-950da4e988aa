package com.facishare.open.feishu.syncapi.data.whatsapp;

import com.facishare.open.feishu.syncapi.base.FsBase;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class WhatsappDeleteTemplate extends FsBase implements Serializable {
    /**
     * 牛信云WhatsApp应用的appkey
     */
    private String appkey;
    /**
     * 发送消息的通道，应用于WhatsApp消息的发送时，值必须为“whatsapp”
     */
    private String messaging_product = ChannelEnum.whatsapp.toString();
    /**
     * 商户的WhatsApp号码列表，需要带国码,如185xxx99
     */
    private String business_phone;
    /**
     * 模板名称(同名多语言模板一并删除)
     */
    private String name;
    /**
     * 模板id（携带了id则只删除单个模板）
     */
    private String id;
}
