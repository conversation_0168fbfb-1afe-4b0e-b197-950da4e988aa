package com.facishare.open.feishu.web.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.outer.OuterEventHandlerTemplate;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.event.FeishuBaseEvent;
import com.facishare.open.feishu.syncapi.model.event.FeishuEncryptMsgModel;
import com.facishare.open.feishu.syncapi.model.event.FeishuEventModel;
import com.facishare.open.feishu.syncapi.model.event.FeishuVerifyMsgModel;
import com.facishare.open.feishu.syncapi.model.event2.FeishuEventModel2;
import com.facishare.open.feishu.web.handler.FeishuEventHandler;
import com.facishare.open.feishu.web.handler.FeishuEventHandlerManager;
import com.facishare.open.feishu.web.template.model.FeishuEventHandleModel;
import com.facishare.open.feishu.web.utils.FeishuDecrypt;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 飞书外部事件处理器模板实现类
 * 
 * <AUTHOR>
 * @date 2024-08-19
 */

@Slf4j
@Component
public class FeishuOuterEventHandlerTemplate extends OuterEventHandlerTemplate {
    @Resource
    private FeishuEventHandlerManager eventHandlerManager;


    @Override
    public void onEventDecode(MethodContext context) {
        log.info("FeishuOuterEventHandlerTemplate.onEventDecode,context={}", context);

        String body = context.getData().toString();
        FeishuEncryptMsgModel model = JSONObject.parseObject(body, FeishuEncryptMsgModel.class);
        LogUtils.info("FeishuOuterEventHandlerTemplate.push,model={}", model);
        if (model == null || StringUtils.isEmpty(model.getEncrypt())) {
            context.setResult(TemplateResult.newError(FeishuEventHandler.FAIL));
            return;
            // return FeishuEventHandler.FAIL;
        }
        FeishuDecrypt feishuDecrypt = new FeishuDecrypt(ConfigCenter.feishuEncryptKey);
        try {
            String plainText = feishuDecrypt.decrypt(model.getEncrypt());
            LogUtils.info("FeishuOuterEventHandlerTemplate.push,plainText={}", plainText);
            if (StringUtils.isEmpty(plainText)) {
                context.setResult(TemplateResult.newError(FeishuEventHandler.FAIL));
                return;
                // return FeishuEventHandler.FAIL;
            }

            // 验证网页推送事件
            String verifyResult = verifyUrl(plainText);
            if (!StringUtils.equalsIgnoreCase(verifyResult, FeishuEventHandler.FAIL)) {
                context.setResult(TemplateResult.newError(verifyResult));
                return;
                // return verifyResult;
            }

            String eventData = null;
            String eventType = null;
            String appId = null;
            String outEa = null;
            FeishuEventModel2.EventModelHeader header = null;
            FeishuEventModel feishuEventModel = JSON.parseObject(plainText, FeishuEventModel.class);
            if (feishuEventModel != null && StringUtils.isNotEmpty(feishuEventModel.getType())) {
                eventData = feishuEventModel.getEvent().toString();
                FeishuBaseEvent feishuBaseEvent = JSON.parseObject(eventData, FeishuBaseEvent.class);
                eventType = feishuBaseEvent.getType();
                appId = feishuBaseEvent.getAppId();
                outEa = feishuBaseEvent.getTenantKey();
            } else {
                FeishuEventModel2 feishuEventModel2 = JSON.parseObject(plainText, FeishuEventModel2.class);
                header = feishuEventModel2.getHeader();
                eventType = header.getEventType();
                eventData = feishuEventModel2.getEvent().toString();
                appId = header.getAppId();
                outEa = header.getTenantKey();
            }

            LogUtils.info("FeishuOuterEventHandlerTemplate.push,eventType={},header={},eventData={}", eventType, header,
                    eventData);

            FeishuEventHandleModel eventHandleModel = new FeishuEventHandleModel();
            eventHandleModel.setEventType(eventType);
            eventHandleModel.setEventData(eventData);
            eventHandleModel.setHeader(header);

            // 准备下一步需要的context
            context.setData(eventHandleModel);
            context.setResult(TemplateResult.newSuccess(FeishuEventHandler.SUCCESS));
            return;
            // return eventHandlerManager.handle(eventType,header,eventData);
        } catch (Exception e) {
            LogUtils.info("FeishuOuterEventHandlerTemplate.push,exception={}", e.getMessage());
            context.setResult(TemplateResult.newError(FeishuEventHandler.FAIL));
            return;
            // return FeishuEventHandler.FAIL;
        }
    }

    @Override
    public void onEventFilter(MethodContext context) {
        log.info("FeishuOuterEventHandlerTemplate.onEventFilter,context={}", context);
        context.setResult(TemplateResult.newSuccess(FeishuEventHandler.SUCCESS));
    }

    @Override
    public void onEventHandle(MethodContext context) {
        log.info("FeishuOuterEventHandlerTemplate.onEventHandle,context={}", context);
        FeishuEventHandleModel eventHandleModel = context.getData();
        // 事件分发处理，这一块也需要重构成
        String result = eventHandlerManager.handle(eventHandleModel.getEventType(), eventHandleModel.getHeader(),
                eventHandleModel.getEventData());

        context.setResult(TemplateResult.newSuccess(result));
    }

    public String verifyUrl(@RequestBody String body) {
        LogUtils.info("FeishuOuterEventHandlerTemplate.verifyUrl,body={}", body);
        FeishuVerifyMsgModel verifyMsgModel = JSONObject.parseObject(body, FeishuVerifyMsgModel.class);
        LogUtils.info("FeishuOuterEventHandlerTemplate.verifyUrl,verifyMsgModel={}", verifyMsgModel);
        // 飞书服务器验证ISV服务的可用性
        if (verifyMsgModel != null && StringUtils.equalsIgnoreCase(verifyMsgModel.getType(), "url_verification")) {
            Map<String, String> map = new HashMap<>();
            map.put("challenge", verifyMsgModel.getChallenge());
            String result = JSONObject.toJSONString(map);
            LogUtils.info("FeishuOuterEventHandlerTemplate.verifyUrl,result={}", result);
            return result;
        }
        return FeishuEventHandler.FAIL;
    }
}
