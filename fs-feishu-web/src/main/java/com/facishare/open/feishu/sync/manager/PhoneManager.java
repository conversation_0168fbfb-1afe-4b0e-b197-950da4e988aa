package com.facishare.open.feishu.sync.manager;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.facishare.open.feishu.sync.mapper.PhoneMapper;
import com.facishare.open.feishu.syncapi.entity.PhoneEntity;
import com.facishare.open.order.contacts.proxy.api.utils.LogUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class PhoneManager {
    @Resource
    private PhoneMapper phoneMapper;

    public Integer insert(PhoneEntity entity) {
        int count = phoneMapper.insert(entity);
        LogUtils.info("PhoneManager.insert,count={}",count);
        return count;
    }

    public Integer update(PhoneEntity entity) {
        int count = phoneMapper.updateById(entity);
        LogUtils.info("PhoneManager.update,count={}",count);
        return count;
    }

    public List<PhoneEntity> queryEntities(String fsEa, String wabaId, String phonne) {
        LambdaQueryWrapper<PhoneEntity> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(PhoneEntity::getFsEa, fsEa);
        }
        if(StringUtils.isNotEmpty(wabaId)) {
            wrapper.eq(PhoneEntity::getWabaId, wabaId);
        }
        if(StringUtils.isNotEmpty(phonne)) {
            wrapper.eq(PhoneEntity::getPhone, phonne);
        }
        return phoneMapper.selectList(wrapper);
    }

    public Integer delete(String fsEa) {
        LambdaQueryWrapper<PhoneEntity> wrapper = new LambdaQueryWrapper<>();
        if(StringUtils.isNotEmpty(fsEa)) {
            wrapper.eq(PhoneEntity::getFsEa, fsEa);
        }
        return phoneMapper.delete(wrapper);
    }

    public Integer delete(String fsEa, String phone) {
        LambdaQueryWrapper<PhoneEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PhoneEntity::getFsEa, fsEa);
        wrapper.eq(PhoneEntity::getPhone, phone);

        return phoneMapper.delete(wrapper);
    }
}
