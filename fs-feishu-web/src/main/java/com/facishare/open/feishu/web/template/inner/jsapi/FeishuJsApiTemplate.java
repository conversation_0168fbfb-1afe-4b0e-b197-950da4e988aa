package com.facishare.open.feishu.web.template.inner.jsapi;

import com.facishare.open.erpdss.outer.oa.connector.base.context.MethodContext;
import com.facishare.open.erpdss.outer.oa.connector.base.context.TemplateResult;
import com.facishare.open.erpdss.outer.oa.connector.base.inner.jsapi.JsApiTemplate;
import com.facishare.open.feishu.syncapi.model.jsapi.JsApiSignatureModel;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.service.JsApiService;
import com.facishare.open.feishu.web.template.model.JsApiModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Slf4j
@Component
public class FeishuJsApiTemplate extends JsApiTemplate {
    @Resource
    private JsApiService jsApiService;

    @Override
    public void getJsApiSignature(MethodContext context) {
        log.info("FeishuJsApiTemplate.getJsApiSignature,context={}",context);

        JsApiModel jsApiModel = context.getData();
        Result<JsApiSignatureModel> result = jsApiService.getJsApiSignature2(jsApiModel.getAppId(),
                jsApiModel.getFsEa(),
                jsApiModel.getOutEa(),
                jsApiModel.getUrl());

        log.info("FeishuJsApiTemplate.getJsApiSignature,result={}",result);
        context.setResult(TemplateResult.newSuccess(result));
    }
}
