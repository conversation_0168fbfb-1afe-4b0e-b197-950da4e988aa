package com.facishare.open.feishu.sync.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

/**
 * @IgnoreI18n
 * 使用Xor进行加密和解密
 */
public class testUtils {
    public static void main(String[] args) throws Exception {
        Long tm = System.currentTimeMillis();
        Map<String, String> header = new HashMap<>();
        header.put("accessKey", "xxx");
        header.put("ts", tm.toString());
        header.put("bizType", "2");
        header.put("action", "mt");

        JSONObject postData = new JSONObject();
        postData.put("appkey", "xxx");
        postData.put("business_phone", "牛小信");
        String body = postData.toString();
        System.out.println(body);

        // accessKey对应的密码
        String accessSecret = "xxx";

        String md5Hash = sign(header, "test", accessSecret);
//        System.out.println(sortedQueryString);
//        String textToHash = sortedQueryString;
//        String md5Hash = generateMD5(textToHash);
        System.out.println("MD5 Hash in hex: " + md5Hash);
    }

    public static String sign(Map<String, String> header, String body, String accessSecret) {
        List<String> sortedKeys = new ArrayList<>(header.keySet());
        Collections.sort(sortedKeys);

        StringBuilder raw = new StringBuilder();
        // step1: 拼接header参数
        for (String key : sortedKeys) {
            String value = String.valueOf(header.get(key));
            raw.append(key).append("=").append(value).append("&");
        }

        // 移除最后一个多余的 '&'
        if (raw.length() > 0) {
            raw.deleteCharAt(raw.length() - 1);
        }

        // step2: 拼接body参数
        if (StringUtils.isNotEmpty(body)) {
            raw.append("&body=").append(body);
        }

        // step3: 拼接accessSecret
        raw.append("&accessSecret=").append(accessSecret);

        // step4: 计算签名
        String sign = DigestUtils.md5Hex(raw.toString());

        return sign;
    }

//    private static String generateMD5(String data) {
//
//        String s = DigestUtils.md5Hex(data);
//        System.out.println(s);
//        return s;
//
////        try {
////            // 创建MD5 MessageDigest实例
////            MessageDigest md = MessageDigest.getInstance("MD5");
////            // 执行哈希计算
////            byte[] digest = md.digest(data.getBytes());
////
////            // 转换为十六进制小写字符串
////            StringBuilder sb = new StringBuilder();
////            for (byte b : digest) {
////                sb.append(String.format("%02x", b));
////            }
////
////            return sb.toString();
////
////        } catch (NoSuchAlgorithmException e) {
////            // NoSuchAlgorithmException是一个异常，当请求的加密算法不可用时抛出
////            throw new RuntimeException("MD5 algorithm not available", e);
////        }
//    }
}
