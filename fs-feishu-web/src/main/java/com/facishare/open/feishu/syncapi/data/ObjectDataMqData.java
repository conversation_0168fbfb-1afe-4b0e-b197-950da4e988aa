package com.facishare.open.feishu.syncapi.data;

import lombok.Data;

@Data
public class ObjectDataMqData {
    /*
    calculate|CALCULATION_JOB|formula,
    calculate:计算服务
    CALCULATION_JOB：全量历史数据计算队列，新建或编辑计算字段、统计字段、引用字段触发的计算,其他：optool、batch、manual
    formula：计算字段，其他：count,quote
    import:列表导入
    optool_batch：批量工具导入
     */
    private String dataSource;
    private String name;
    private String op;
    @Deprecated
    private String tenantId;//有可能为空，不能使用，具体使用EventObject里的Context
    private EventObject[] body;

    @Data
    public static class EventObject {
        private String eventId;
        private Context context;
        private String name;
        private String entityId;
        private String triggerType;
        private String objectId;
        private ObjectData beforeTriggerData;
        private ObjectData afterTriggerData;
    }

    @Data
    public static class Context {
        private String appId;
        private String tenantId;
        private String userId;
    }

    public boolean isUpdateOp() {
        if (op.equals("u")) {
            return true;
        }
        return false;
    }

    public boolean isInsterOp() {
        if (op.equals("i")) {
            return true;
        }
        return false;
    }

    public boolean isInvalidOp() {
        if (op.equals("invalid")) {
            return true;
        }
        return false;
    }

    public boolean isRecoverOp() {
        if (op.equals("recover")) {
            return true;
        }
        return false;
    }

    public boolean isDeleteOp() {
        if (op.equals("d")) {
            return true;
        }
        return false;
    }

    public boolean isDROp() {
        if (op.equals("d_r")) {
            return true;
        }
        return false;
    }
    public boolean isDescribeDeleteOp() {
        if (op.equals("describe_delete")) {
            return true;
        }
        return false;
    }
    public boolean isDescribeUpdateOp() {
        if (op.equals("describe_update")) {
            return true;
        }
        return false;
    }
    public boolean isFieldDeleteOp() {
        if (op.equals("field_delete")) {
            return true;
        }
        return false;
    }
    public boolean isFieldUpdateOp() {
        if (op.equals("field_update")) {
            return true;
        }
        return false;
    }
}
