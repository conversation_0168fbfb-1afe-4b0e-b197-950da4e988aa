package com.facishare.open.feishu.syncapi.model.externalApprovals;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ExternalInstancesDetail implements Serializable {
    private static final long serialVersionUID = 1L;

    @J<PERSON><PERSON>ield(name = "approval_code")
    private String approvalCode;

    @JSONField(name = "status")
    private String status;

    @JSONField(name = "extra")
    private String extra;

    @JSONField(name = "instance_id")
    private String instanceId;

    @J<PERSON>NField(name = "links")
    private Links links;

    @JSONField(name = "title")
    private String title;

    @JSONField(name = "form")
    private List<Form> form;

    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "user_name")
    private String userName;

    @JSONField(name = "open_id")
    private String openId;

    @JSONField(name = "department_id")
    private String departmentId;

    @J<PERSON><PERSON>ield(name = "department_name")
    private String departmentName;

    @JSONField(name = "start_time")
    private String startTime;

    @J<PERSON><PERSON>ield(name = "end_time")
    private String endTime;

    @J<PERSON><PERSON>ield(name = "update_time")
    private String updateTime;

    @JSONField(name = "display_method")
    private String displayMethod;

    @JSONField(name = "update_mode")
    private String updateMode;

    @JSONField(name = "task_list")
    private List<Task> taskList;

    @JSONField(name = "cc_list")
    private List<CCItem> ccList;

    @JSONField(name = "i18n_resources")
    private List<I18NResource> i18nResources;

    @JSONField(name = "trusteeship_url_token")
    private String trusteeshipUrlToken;

    @JSONField(name = "trusteeship_user_id_type")
    private String trusteeshipUserIdType;

    @JSONField(name = "trusteeship_urls")
    private TrusteeshipUrls trusteeshipUrls;

    @JSONField(name = "trusteeship_cache_config")
    private TrusteeshipCacheConfig trusteeshipCacheConfig;

    // Links class
    @Data
    public static class Links implements Serializable {
        @JSONField(name = "pc_link")
        private String pcLink;

        @JSONField(name = "mobile_link")
        private String mobileLink;
    }

    // Form class
    @Data
    public static class Form implements Serializable {
        @JSONField(name = "name")
        private String name;

        @JSONField(name = "value")
        private String value;
    }

    // Task class
    @Data
    public static class Task implements Serializable {
        @JSONField(name = "task_id")
        private String taskId;

        @JSONField(name = "user_id")
        private String userId;

        @JSONField(name = "open_id")
        private String openId;

        @JSONField(name = "title")
        private String title;

        @JSONField(name = "links")
        private Links links;

        @JSONField(name = "status")
        private String status;

        @JSONField(name = "extra")
        private String extra;

        @JSONField(name = "create_time")
        private String createTime;

        @JSONField(name = "end_time")
        private String endTime;

        @JSONField(name = "update_time")
        private String updateTime;

        @JSONField(name = "action_context")
        private String actionContext;

        @JSONField(name = "action_configs")
        private List<ActionConfig> actionConfigs;

        @JSONField(name = "display_method")
        private String displayMethod;

        @JSONField(name = "exclude_statistics")
        private Boolean excludeStatistics;

        @JSONField(name = "node_id")
        private String nodeId;

        @JSONField(name = "node_name")
        private String nodeName;
    }

    // ActionConfig class
    @Data
    public static class ActionConfig implements Serializable {
        @JSONField(name = "action_type")
        private String actionType;

        @JSONField(name = "action_name")
        private String actionName;

        @JSONField(name = "is_need_reason")
        private Boolean isNeedReason;

        @JSONField(name = "is_reason_required")
        private Boolean isReasonRequired;

        @JSONField(name = "is_need_attachment")
        private Boolean isNeedAttachment;
    }

    // TrusteeshipUrls class
    @Data
    public static class TrusteeshipUrls implements Serializable {
        @JSONField(name = "form_detail_url")
        private String formDetailUrl;

        @JSONField(name = "action_definition_url")
        private String actionDefinitionUrl;

        @JSONField(name = "approval_node_url")
        private String approvalNodeUrl;

        @JSONField(name = "action_callback_url")
        private String actionCallbackUrl;

        @JSONField(name = "pull_business_data_url")
        private String pullBusinessDataUrl;
    }

    // TrusteeshipCacheConfig class
    @Data
    public static class TrusteeshipCacheConfig implements Serializable {
        @JSONField(name = "form_policy")
        private String formPolicy;

        @JSONField(name = "form_vary_with_locale")
        private boolean formVaryWithLocale;

        @JSONField(name = "form_version")
        private String formVersion;
    }

    // CCItem class
    @Data
    public static class CCItem implements Serializable {
        @JSONField(name = "cc_id")
        private String ccId;

        @JSONField(name = "user_id")
        private String userId;

        @JSONField(name = "open_id")
        private String openId;

        @JSONField(name = "links")
        private Links links;

        @JSONField(name = "read_status")
        private String readStatus;

        @JSONField(name = "extra")
        private String extra;

        @JSONField(name = "title")
        private String title;

        @JSONField(name = "create_time")
        private String createTime;

        @JSONField(name = "update_time")
        private String updateTime;

        @JSONField(name = "display_method")
        private String displayMethod;
    }

    // I18NResource class
    @Data
    public static class I18NResource implements Serializable {
        @JSONField(name = "locale")
        private String locale;

        @JSONField(name = "texts")
        private List<TextResource> texts;

        @JSONField(name = "is_default")
        private Boolean isDefault;
    }

    // TextResource class
    @Data
    public static class TextResource implements Serializable {
        @JSONField(name = "key")
        private String key;

        @JSONField(name = "value")
        private String value;
    }
}
