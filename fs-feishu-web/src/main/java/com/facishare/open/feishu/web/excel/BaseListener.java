package com.facishare.open.feishu.web.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (^_−)☆
 * @date 2020/8/28
 */
@Slf4j
@Getter
public abstract class BaseListener<T> extends AnalysisEventListener<T> {
    protected final List<T> dataList;

    public BaseListener() {
        dataList = new ArrayList<>();
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        log.info("invoke,data={}", data);
        dataList.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("analyse excels finished,context:{}", context);
    }
}
