package com.facishare.open.feishu.sync.manager;

import com.alibaba.fastjson.JSONObject;
import com.facishare.open.feishu.syncapi.config.ConfigCenter;
import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.params.OuterOaEnterpriseBindParams;
import com.facishare.open.outer.oa.connector.common.api.admin.FeiShuConnectorVo;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 根据应用信息以及企业，返回对应的真实的url
 *
 * <AUTHOR>
 * @create 2024/11/25 11:43
 */
@Component
public class UrlManager {

    private static final String DEFAULT_DOMAIN = "feishu.cn";

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    public String getFeishuUrlByPointDomain( String path,String pointDomain) {
        return path.replace("feishu.cn",pointDomain);
    }

    /**
     * outea 有时候是会空的，因为上层传递参数只有appid
     * @param outEa
     * @param appId
     * @param path
     * @return
     */
    public String getFeishuUrl(String outEa, String appId, String path) {
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().outEa(outEa).appId(appId).build();
        OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(outerOaEnterpriseBindParams);
        if(ObjectUtils.isNotEmpty(entity)&& StringUtils.isNotEmpty(entity.getConnectInfo())){
            FeiShuConnectorVo feiShuConnectorVo= JSONObject.parseObject(entity.getConnectInfo(),FeiShuConnectorVo.class);
            if(feiShuConnectorVo.getBaseUrl()!=null){
                String feishuUrl=path.replace("feishu.cn", feiShuConnectorVo.getBaseUrl());
                return feishuUrl;
            }
        }
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId)!=null){
            return path.replace("feishu.cn",ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId));
        }
        return path;
    }

    public String getFeishuUrlByFsEa(String fsEa, String appId, String path) {
        OuterOaEnterpriseBindParams outerOaEnterpriseBindParams = OuterOaEnterpriseBindParams.builder().fsEa(fsEa).channel(ChannelEnum.feishu).appId(appId).build();
        List<OuterOaEnterpriseBindEntity> entities = outerOaEnterpriseBindManager.getEntities(outerOaEnterpriseBindParams);
        if(ObjectUtils.isNotEmpty(entities)&& StringUtils.isNotEmpty(entities.get(0).getConnectInfo())){
            OuterOaEnterpriseBindEntity entity = entities.get(0);
            FeishuEnterpriseConnectParams feishuEnterpriseConnectParams = entity.getChannel().getConnectParam(entity.getConnectInfo());
            if(feishuEnterpriseConnectParams.getBaseUrl()!=null){
                String feishuUrl=path.replace("feishu.cn", feishuEnterpriseConnectParams.getBaseUrl());
                return feishuUrl;
            }
        }
        if(ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId)!=null){
            return path.replace("feishu.cn",ConfigCenter.FEISHU_ISV_REQUEST_MAP.get(appId));
        }
        return path;
    }
}
