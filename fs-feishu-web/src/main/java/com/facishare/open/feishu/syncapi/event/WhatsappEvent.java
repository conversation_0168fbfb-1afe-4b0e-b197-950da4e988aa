package com.facishare.open.feishu.syncapi.event;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class WhatsappEvent implements Serializable {
    private String business_phone;
    private String messaging_product;
    private List<StatusModel> statuses;
    private List<ContactModel> contacts;
    private List<MessageModel> messages;

    @Data
    public static class StatusModel implements Serializable {
        private Conversation conversation;
        private List<ErrorModel> errors;
        private String recipient_id;
        private String timestamp;
        private String status;
        private String id;
        private List<CostModel> costs;
        private String meta_message_id;

        @Data
        public static class Conversation implements Serializable {
            private String id;
            private String expiration_timestamp;
            private Origin origin;

            @Data
            public static class Origin implements Serializable {
                private String type;
            }
        }

        @Data
        public static class ErrorModel implements Serializable {
            private Integer code;
            private Integer meta_code;
            private String title;
        }
    }

    @Data
    public static class CostModel implements Serializable {
        private String currency;
        private Double price;
        private Double foreign_price;
        private Integer cdr_type;
        private String message_id;
        private Integer direction;
    }

    @Data
    public static class ContactModel implements Serializable {
        private Profile profile;
        private String wa_id;

        @Data
        public static class Profile implements Serializable {
            private String name;
        }
    }

    @Data
    public static class MessageModel implements Serializable {
        private String from;
        private String id;
        private String timestamp;
        private String type;
        private Context context;
        private CostModel cost;
        private TextModel text;
        private ImageModel image;
        private VideoModel video;
        private VoiceModel voice;
        private AudioModel audio;
        private DocumentModel document;
        private LocationModel location;
        private StickerModel sticker;
        private InteractiveModel interactive;
        private OrderModel order;
        private ReferralModel referral;

        @Data
        public static class Context implements Serializable {
            private String from;
            private String id;
            private String meta_message_id;
            private Boolean forwarded;
            private Boolean frequently_forwarded;
            private ReferredProduct referred_product;

            @Data
            public static class ReferredProduct implements Serializable {
                private String catalog_id;
                private String product_retailer_id;
            }
        }

        @Data
        public static class TextModel implements Serializable {
            private String body;
        }

        @Data
        public static class ImageModel implements Serializable {
            private String id;
            private String npath;
            private String caption;
            private String mime_type;
            private String sha256;
        }

        @Data
        public static class VideoModel implements Serializable {
            private String id;
            private String npath;
            private String caption;
            private String mime_type;
            private String sha256;
        }

        @Data
        public static class VoiceModel implements Serializable {
            private String id;
            private String npath;
            private String mime_type;
            private String sha256;
        }

        @Data
        public static class AudioModel implements Serializable {
            private String id;
            private String npath;
            private String mime_type;
            private String sha256;
        }

        @Data
        public static class DocumentModel implements Serializable {
            private String id;
            private String npath;
            private String mime_type;
            private String sha256;
            private String filename;
        }

        @Data
        public static class LocationModel implements Serializable {
            private String longitude;
            private String latitude;
            private String name;
            private String address;
        }

        @Data
        public static class StickerModel implements Serializable {
            private String id;
            private String npath;
            private String mime_type;
            private String sha256;
            private Metadata metadata;

            @Data
            public static class Metadata implements Serializable {
                private List<String> emojis;
                @JSONField(name = "is-first-party-sticker")
                private String is_first_party_sticker;
                @JSONField(name = "sticker-pack-id")
                private String sticker_pack_id;
            }
        }

        @Data
        public static class InteractiveModel implements Serializable {
            private String type;
            private ReplyModel button_reply;
            private ReplyModel list_reply;
            private NfmReply nfm_reply;

            @Data
            public static class ReplyModel implements Serializable {
                private String id;
                private String title;
                private String description;
            }

            @Data
            public static class NfmReply implements Serializable {
                private String name;
                private String body;
                private String response_json;
            }
        }

        @Data
        public static class OrderModel implements Serializable {
            private String catalog_id;
            private String text;
            private List<ProductItem> product_items;

            @Data
            public static class ProductItem implements Serializable {
                private String product_retailer_id;
                private Double quantity;
                private Double item_price;
                private String currency;
            }
        }

        @Data
        public static class ReferralModel implements Serializable {
            private String source_url;
            private String source_type;
            private String headline;
            private String body;
            private String media_type;
            private String image_url;
            private String video_url;
            private String thumbnail_url;
            private String ctwa_clid;
        }
    }
}
