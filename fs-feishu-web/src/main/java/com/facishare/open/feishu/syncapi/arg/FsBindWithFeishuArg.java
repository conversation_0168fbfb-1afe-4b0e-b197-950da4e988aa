package com.facishare.open.feishu.syncapi.arg;

import com.facishare.open.feishu.syncapi.model.connect.FeishuEnterpriseConnectParams;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.Data;

import java.io.Serializable;

@Data
public class FsBindWithFeishuArg implements Serializable {
    /**
     * 集成平台连接器ID
     */
    private String dataCenterId;
    /**
     * 集成平台飞书连接器名称
     */
    private String dataCenterName;
    /**
     * 纷享企业EA
     */
    private String fsEa;
    /**
     * 飞书企业ID=tenantKey
     */
    private String outEa;

    private Integer tenantTag;

    /**
     * 飞书企业ID
     */
    private String displayId;
    /**
     * 飞书企业名称
     */
    private String outEn;

    private ChannelEnum channelEnum;

    private FeishuEnterpriseConnectParams feishuEnterpriseConnectParams;
}
