package com.facishare.open.feishu.syncapi.service.whatsapp;

import com.facishare.open.feishu.syncapi.data.whatsapp.WhatsappBindInfo;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.feishu.syncapi.result.whatsapp.WhatsappBalanceResult;

public interface WhatsappCommonService {
    Result<WhatsappBalanceResult> getBalance(String fsEa);

    Result<WhatsappBalanceResult> getBalance(WhatsappBindInfo bindInfo);
}
