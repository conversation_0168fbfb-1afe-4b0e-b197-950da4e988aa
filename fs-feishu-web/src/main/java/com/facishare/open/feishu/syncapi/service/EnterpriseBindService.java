package com.facishare.open.feishu.syncapi.service;

import com.facishare.open.feishu.syncapi.arg.FsBindWithFeishuArg;
import com.facishare.open.feishu.syncapi.arg.QueryConnectInfoArg;
import com.facishare.open.feishu.syncapi.entity.CorpInfoEntity;
import com.facishare.open.feishu.syncapi.entity.EnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.feishu.syncapi.model.EnterpriseModel;
import com.facishare.open.feishu.syncapi.model.FeiShuConnectParam;
import com.facishare.open.feishu.syncapi.result.Result;
import com.facishare.open.outer.oa.connector.common.api.enums.BindTypeEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;

import java.util.List;

/**
 * CRM和飞书企业绑定服务
 * <AUTHOR>
 * @date 20220728
 */
public interface EnterpriseBindService {
    /**
     * 获取和飞书绑定的企业信息
     * @param outEa
     * @return
     */
    Result<List<OuterOaEnterpriseBindEntity>> getEnterpriseBindList(String outEa,String appId,String dataCenterId);


    /**
     * 获取和飞书绑定的CRM企业EA
     * @param outEa
     * @return
     */
    Result<List<EnterpriseModel>> getFsEaList(String outEa, String outUserId);

    /**
     * 纷享企业和飞书企业绑定
     * @param arg
     * @return
     */
    Result<CorpInfoEntity> fsBindWithFeishu(FsBindWithFeishuArg arg);

    /**
     * 纷享企业和飞书企业解绑
     * @param outerOaEnterpriseBindEntity
     * @return
     */
    Result<Void> fsUnBindWithFeishu(OuterOaEnterpriseBindEntity outerOaEnterpriseBindEntity);

    /**
     * 纷享企业和飞书企业解绑
     * @param displayId 飞书企业编码
     * @return
     */
    Result<Void> fsUnBindWithFeishu2(String displayId);

    /**
     * 飞书企业和纷享企业解绑
     * @param displayId 飞书企业编码
     * @return
     */
    Result<Void> feishuUnBindWithFs(String displayId);

    /**
     * 查询飞书企业连接信息
     * @param arg
     * @return
     */
    Result<FeiShuConnectParam> queryConnectInfo(QueryConnectInfoArg arg);

    /**
     * 查询飞书扫码授权URL
     * @return
     */
    Result<String> queryScanCodeAuthUrl(String fsEa, String dataCenterId);

    /**
     * 是否存在手动绑定的企业
     * @param outEa
     * @return
     */
    Result<Boolean> hasManualBind(String outEa);

    /**
     * 更新企业绑定拓展字段
     * @param fsEa
     * @param extendField
     * @param extendValue
     * @return
     */
    Result<Void> updateEnterpriseExtend(String fsEa, String outEa, String extendField, Object extendValue);

    Result<List<OuterOaEnterpriseBindEntity>> queryEnterprisesByBindType(BindTypeEnum bindType);

    /**
     * 账号自动绑定
     */
    Result<Integer> saveAutoBind(String fsEa, String outEa, Integer autoBind, ChannelEnum channelEnum);

    Result<Integer> queryAutoBind(String fsEa, String outEa);

    /**
     * 检查并初始化飞书连接器，仅供集成平台调用
     * @param fsEa
     * @return
     */
    Result<Void> checkAndInitConnector(String fsEa, String dataCenterId,String channel);

    Result<OuterOaEnterpriseBindEntity> getEnterpriseBind(String outEa, String fsEa);

    Result<Void> cloudFsBindWithFeishu(String outEa, String appId, String fsEa, String domain);

    Result<OuterOaEnterpriseBindEntity> getEntity(String fsEa);

    Result<Void> updateConnectParams(FsBindWithFeishuArg fsBindWithFeishuArg);
}