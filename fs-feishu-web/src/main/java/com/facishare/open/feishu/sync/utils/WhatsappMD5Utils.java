package com.facishare.open.feishu.sync.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 使用MD5进行加密
 */
public class WhatsappMD5Utils {
    public static String sign(Map<String, String> header, String body, String accessSecret) {
        List<String> sortedKeys = new ArrayList<>(header.keySet());
        Collections.sort(sortedKeys);

        StringBuilder raw = new StringBuilder();
        // step1: 拼接header参数
        for (String key : sortedKeys) {
            String value = String.valueOf(header.get(key));
            raw.append(key).append("=").append(value).append("&");
        }

        // 移除最后一个多余的 '&'
        if (raw.length() > 0) {
            raw.deleteCharAt(raw.length() - 1);
        }

        // step2: 拼接body参数
        if (StringUtils.isNotEmpty(body)) {
            raw.append("&body=").append(body);
        }

        // step3: 拼接accessSecret
        raw.append("&accessSecret=").append(accessSecret);

        // step4: 计算签名
        String sign = DigestUtils.md5Hex(raw.toString());

        return sign;
    }
}
