package com.facishare.open.outer.oa.connector.common.api.admin;

import com.facishare.open.outer.oa.connector.common.api.annotation.ConnectorType;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ConnectorType(channel = ChannelEnum.feishu, fieldName = "feishu")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeiShuConnectorVo extends BaseConnectorVo {

    /**
     * 飞书企业展示id
     */

    private String displayId;
    /**
     * 飞书企业id
     */

    /**
     * 飞书企业corpid
     */
    private String corpId;
    /**
     * 企业名称
     */

    private String enterpriseName;


    private String baseUrl;

    //内部应用


    /**
     *  飞书应用ID
     */

    private String appId;


    /**
     * 飞书应用密钥
     */

    private String appSecret;

    /**
     * 留资使用
     * @return
     */
    private Boolean isFirstLand;

    /** //拓展字段，1、这里取留资是否首次使用crm，字段：isFirstLand。2、是否已经留资,字段：isRetainInformation。
     * 留资使用
     * @return
     */
    private Boolean isRetainInformation;



}
