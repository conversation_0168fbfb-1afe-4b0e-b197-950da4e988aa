package com.facishare.open.outer.oa.connector.common.api.result;

import com.facishare.open.outer.oa.connector.common.api.annotation.SystemAnnotation;
import com.facishare.open.outer.oa.connector.common.api.enums.FieldTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 系统字段映射结果
 */
@Data
public class SystemFieldMappingResult implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 字段列表绑定
     */
    private List<ItemFieldMapping> itemFieldMappings;



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ItemFieldMapping implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 字段API名称
         */
        private String crmFieldApiName;

        /**
         * 字段显示名称
         */
        private String crmFieldLabel;
        /**
         * 字段显示名称
         */
        private String outerOAFieldApiName;
        /**
         * 字段显示名称
         */
        private String outerOAFieldLabel;

        /**
         * 对应outerdata text 字段
         */
        private String outerDataText;

        /**
         *字段是不是用来匹配唯一性
         */
        @Builder.Default
        private Boolean matchUnique=false;


        /**
         * 记录类型,历史数据是空的，需要注意兼容
         */
        private FieldTypeEnum fieldType;

        private String outerI18nKey;

        private String crmI18nKey;

    }





    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SystemField implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 字段API名称
         */
        private String fieldApiName;

        /**
         * 字段显示名称
         */
        private String fieldLabel;

        /**
         * 对应outerdata text 字段
         */
        private String text;
        /**
         * 对应类型
         */
        private FieldTypeEnum fieldType;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CRMUserIds implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 字段API名称
         */
        private List<String> CRMUserIds;
    }
}