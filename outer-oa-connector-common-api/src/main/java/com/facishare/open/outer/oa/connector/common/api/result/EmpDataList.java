package com.facishare.open.outer.oa.connector.common.api.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * 账号绑定列表VO
 */
@Data
public class EmpDataList implements Serializable {
    /**
     * 总数
     */
    private Long total;

    /**
     * 列表数据
     */
    private List<EmpDataListItemVO> userList;

    /**
     * 返回动态列
     */
    private Set<DynamicColumn> dynamicColumns;

    @Data
    public static class DynamicColumn implements Serializable {
        private String name;
        private String label;
    }
}