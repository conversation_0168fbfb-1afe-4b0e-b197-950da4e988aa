package com.facishare.open.ding.cloud.service.impl;

import com.facishare.open.ding.api.model.OAConnectorOpenDataModel;
import com.facishare.open.ding.api.service.cloud.CloudMonitorService;
import com.facishare.open.ding.cloud.manager.OAConnectorOpenDataManager;
import com.facishare.open.outer.oa.connector.common.api.result.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("cloudMonitorServiceImpl")
public class CloudMonitorServiceImpl implements CloudMonitorService {
    @Autowired
    private OAConnectorOpenDataManager oaConnectorOpenDataManager;

    @Override
    public Result<Void> uploadOaConnectorOpenData(OAConnectorOpenDataModel oaConnectorOpenDataModel) {
        oaConnectorOpenDataManager.send(oaConnectorOpenDataModel);
        return Result.newSuccess();
    }
}
