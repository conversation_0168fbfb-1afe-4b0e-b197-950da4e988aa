package com.facishare.open.ding.web.arg;

import com.facishare.open.ding.common.model.BaseArg;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * Created by system on 2018/4/9.
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ConfigurationCreateArg extends BaseArg {

    /** 历史数据导入状态：0、未导入 1、导入 **/
    private Integer historyDataImport;

    /** 产品导入状态：0、未导入 1、导入 **/
    private Integer productImport;

    /** 客户导入状态：0、未导入 1、导入 **/
    private Integer customerImport;

    /** 数据同步类型：1、手动同步 2、定时同步 **/
    private Integer syncType;

    /** 定时同步频率类型： **/
    private  Integer rateType;

    /** 定时同步开始时间，默认值 00:00 **/
    private String startTime;

    /** 定时同步结束时间, 默认值 23:59 **/
    private String endTime;

}
