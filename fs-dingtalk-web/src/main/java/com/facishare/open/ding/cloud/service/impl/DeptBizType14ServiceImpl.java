package com.facishare.open.ding.cloud.service.impl;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.ding.api.enums.DingDeptEventEnum;
import com.facishare.open.ding.api.model.AppParams;
import com.facishare.open.ding.api.model.BizDeptModel;
import com.facishare.open.ding.api.service.DingCorpMappingService;
import com.facishare.open.ding.api.service.cloud.CloudDeptService;
import com.facishare.open.ding.api.vo.DingCorpMappingVo;
import com.facishare.open.ding.cloud.constants.ConfigCenter;
import com.facishare.open.ding.cloud.entity.HighBizDataDo;
import com.facishare.open.ding.cloud.service.api.DingEventService;
import com.facishare.open.ding.common.model.Dept;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaDepartmentBindEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/5/15 14:00 部门
 * @Version 1.0
 */
@Service("deptBizType14ServiceImpl")
@Slf4j
public class DeptBizType14ServiceImpl implements DingEventService {

    @Autowired
    private CloudDeptService cloudDeptService;
    @Autowired
    private DingCorpMappingService dingCorpMappingService;
    private Integer DEPT_AUTH_NOT_RANGE = 50004;
    @Override
    public void executeEvent(HighBizDataDo eventData) {
        BizDeptModel bizDeptModel = JSONObject.parseObject(eventData.getBizData(), new TypeReference<BizDeptModel>() {
        });
        String suiteId=eventData.getSubscribeId().split("_")[0];
        log.info("bizdept:{}", bizDeptModel);
        AppParams appParams = ConfigCenter.APP_PARAMS_MAP.get(suiteId);
        final String appId = appParams.getAppId();
        Result<List<DingCorpMappingVo>> corpResult = dingCorpMappingService.queryCorpMappingByCorpId(eventData.getCorpId(), appId);
        log.info("ding corpResult:{}", corpResult);
        if (CollectionUtils.isEmpty(corpResult.getData())||DEPT_AUTH_NOT_RANGE.equals(bizDeptModel.getErrcode())) {
            log.warn("bizType update dept fail,dingCorpId:{}", eventData.getCorpId());
            return;
        }
        Integer ei = corpResult.getData().get(0).getEi();
        Result<OuterOaDepartmentBindEntity> deptVoResult = cloudDeptService.queryDept(ei, Long.valueOf(eventData.getBizId()), appId);
        Dept dept = new Dept();
        dept.setId(bizDeptModel.getId());
        dept.setParentid(bizDeptModel.getParentId());
        dept.setName(bizDeptModel.getName());
        //old:部门负责人，优先钉钉部门的主管 对应 CRM部门的负责人，如果主管没有值群主有值，则取群主
        //new:部门负责人，只取钉钉部门的主管 对应 CRM部门的负责人
        String deptOwner = convert(bizDeptModel.getDeptManagerUseridList());
//        if(StringUtils.isNotEmpty(bizDeptModel.getOrgDeptOwner()) && StringUtils.isEmpty(deptOwner)) {
//            deptOwner = bizDeptModel.getOrgDeptOwner();
//        }
        dept.setDeptOwner(deptOwner);
        log.info("bizType execute dept:{}", dept);
        if (ObjectUtils.isEmpty(deptVoResult.getData())) {
            if(DEPT_AUTH_NOT_RANGE.equals(bizDeptModel.getErrcode())||bizDeptModel.getSyncAction().equals(DingDeptEventEnum.ORG_DEPT_REMOVE.getAction())){
                log.info("create dept not auth:{}",eventData.getBizId());
                return;
            }
            if (bizDeptModel.getSyncAction().equals(DingDeptEventEnum.ORG_DEPT_CREATE.getAction()) || bizDeptModel.getSyncAction().equals(DingDeptEventEnum.ORG_DEPT_MODIFY.getAction())) {
                //创建部门
                Result<Integer> createResult = cloudDeptService.createDept(dept, eventData.getCorpId(), ei, appId, suiteId);
                log.info("result createResult:{}",createResult);
            }
        } else {
            if (bizDeptModel.getSyncAction().equals(DingDeptEventEnum.ORG_DEPT_REMOVE.getAction())) {
                //移除部门
                Result<Integer> removeDept = cloudDeptService.removeDept(Long.valueOf(eventData.getBizId()), eventData.getCorpId(), ei, appId);
                log.info("result removeDept:{}",removeDept);
                return;
            }
            if(bizDeptModel.getErrcode().equals(DEPT_AUTH_NOT_RANGE)){
                log.info("modify dept not auth:{}",eventData.getBizId());
                return;
            }
            //修改部门
            Result<Integer> result = cloudDeptService.modifyDept(dept, eventData.getCorpId(), ei, appId, suiteId);
            log.info("result modifyDept:{}",result);
        }
    }

    /**
     * 钉钉主动推送的部门信息，格式为老板：deptManagerUseridList":"070918523636548759|czx2|manager7605
     * @param managerList
     * @return
     */
    private static String convert(String managerList){
        if(StringUtils.isNotEmpty(managerList)){
            String[] split = managerList.split("\\|");
            return split[0];
        }
        return null;
    }
}
