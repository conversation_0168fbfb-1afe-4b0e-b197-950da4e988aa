package com.facishare.open.ding.provider.manager;

import com.facishare.open.ding.api.result.KcSyncLogResult;
import com.facishare.open.ding.common.result.PageObject;
import com.facishare.open.ding.common.utils.BeanUtil;
import com.facishare.open.ding.provider.mongodb.DingSyncLogDao;
import com.facishare.open.ding.provider.mongodb.entity.DingSyncLogDO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>类的详细说明</p>
 *
 * @<NAME_EMAIL>
 * @version 1.0
 * @dateTime 2018/7/23 16:53
 */
@Slf4j
@Component
public class DingSyncLogManager {

    @Autowired
    private DingSyncLogDao dingSyncLogDao;

    /**
     * 记录同步日志
     *
     * @param dingSyncLogDO
     */
    public void logDingSync(DingSyncLogDO dingSyncLogDO) {
        dingSyncLogDao.saveDingSyncLogDO(dingSyncLogDO);
    }

    /**
     * 分页查询日志
     *
     * @param ei
     * @param operationType
     * @param operationStatus
     * @param pageSize
     * @param pageNo
     * @return
     */
    public PageObject<KcSyncLogResult> queryDingSyncLogPage(Integer ei, Integer operationType, Integer operationStatus, Integer pageSize, Integer pageNo,
                                                          String apiName, String contentKey, Long startTime, Long endTime) {
        PageObject<KcSyncLogResult> pageObject = new PageObject();
        Date startDate = null, endDate = null;

        if (startTime != null) {
            startDate = new Date(startTime);
        }
        if (endTime != null) {
            endDate = new Date(endTime);
        }
        if (Objects.isNull(ei)) {
            log.warn("queryKcSyncLogDOPage param error, ei=[{}].", ei);
            return pageObject;
        }
        pageSize = Objects.isNull(pageSize) ? 10 : pageSize;
        pageNo = Objects.isNull(pageNo) ? 1 : pageNo;
        pageObject.setPageNo(pageNo);
        pageObject.setPageSize(pageSize);
        pageObject.setTotalCount(dingSyncLogDao.queryTotalCount(ei, operationType, operationStatus, apiName, contentKey, startDate, endDate));
        List<DingSyncLogDO> dingSyncLogDOS = dingSyncLogDao.queryDingSyncLogDOPage(ei, operationType, operationStatus, pageSize, pageNo, apiName, contentKey, startDate, endDate);
        List<KcSyncLogResult> kcSyncLogResultList = null;
        if (!CollectionUtils.isEmpty(dingSyncLogDOS)) {
            kcSyncLogResultList = Lists.newArrayList();
            for (int i = 0; i < dingSyncLogDOS.size(); i++) {
                DingSyncLogDO dingSyncLogDO = dingSyncLogDOS.get(i);
                KcSyncLogResult kcSyncLogResult = BeanUtil.copy(dingSyncLogDO, KcSyncLogResult.class);
                kcSyncLogResult.setId(dingSyncLogDO.getId().toString());
                kcSyncLogResultList.add(kcSyncLogResult);
            }
        }
        pageObject.setResult(kcSyncLogResultList);
        return pageObject;
    }

}
