package com.facishare.open.ding.cloud.service.api;

import com.facishare.open.ding.api.model.OrderModel;
import com.facishare.open.ding.cloud.result.CreateFsCustomerResult;
import com.facishare.open.ding.cloud.arg.WaitingTenantCreateArg;
import com.facishare.open.ding.cloud.result.SaveOrderResult;
import com.facishare.open.ding.common.result.Result;

/**
 * 钉钉订单模板服务
 * <AUTHOR>
 * @date 20240925
 */

public interface DingOrderService {
    /**
     * 保存订单
     * @param orderModel
     * @return
     */
    Result<SaveOrderResult> saveOrder(OrderModel orderModel);

    /**
     * 保存订单并创建纷享订单
     * @param orderModel
     * @return
     */
    Result<Void> saveOrderAndAddFsOrder(OrderModel orderModel);
    /**
     * 创建纷享客户
     * @param orderModel
     * @return
     */
    Result<CreateFsCustomerResult> createFsCustomer(OrderModel orderModel);

    /**
     * 创建纷享订单
     * @param saveOrderResult
     * @param enterPriseAccount
     * @param enterPriseName
     * @return
     */
    Result<String> createFsOrder(SaveOrderResult saveOrderResult,
                                 String enterPriseAccount,
                                 String enterPriseName);

    /**
     * 判断企业是否绑定
     * @param ea
     * @return
     */
    Result<Boolean> isEnterpriseBind(String ea);

    /**
     * 更新企业和管理员映射表
     *
     * @param ea
     * @param ei
     * @return
     */
    Result<WaitingTenantCreateArg> updateEnterpriseAndAdminMapping(String ea,
                                                                   Integer ei);

    /**
     * 发送欢迎消息
     * @param ea
     * @return
     */
    Result<Void> sendWelcomeMsg(String ea);

    /**
     * 初始化企业通讯录
     * @param ea
     * @param outEa
     * @param suiteId
     * @return
     */
    Result<Void> initEnterpriseContacts(String ea,
                                        String outEa,
                                        String suiteId);
}
