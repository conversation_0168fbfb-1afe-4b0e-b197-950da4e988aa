package com.facishare.open.ding.common.utils.threadpool;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

/**
 * Created by system on 2018/5/16.
 */
public class ThreadPoolUtils {

    private static ExecutorService
        service = NamedThreadPool.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 4, "k3cloud");

    public static void execute(Runnable command) {
        service.execute(command);
    }

    public static Future<?> submit(Runnable command) {
        return service.submit(command);
    }

}
