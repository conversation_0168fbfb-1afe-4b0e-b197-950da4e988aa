package com.facishare.open.ding.provider.arg;

import com.fxiaoke.message.extrnal.platform.model.KeyValueItem;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2020/10/14 11:37
 * @Version 1.0
 */
@Data
public class TextCardMessageArg extends BaseExternalMessageArg {

    //H5端使用url
    private String url;

    private String title;

    private Integer generateUrlType;
    /**
     * 0 使用业务侧默认Url
     * 1 标识对象详情页Url
     * 2 标识业务流详情页Url
     */
     private Map<String,String> extraDataMap;//url扩展参数

    private List<KeyValueItem> form;


}
