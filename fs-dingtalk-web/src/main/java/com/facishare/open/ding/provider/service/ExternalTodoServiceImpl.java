package com.facishare.open.ding.provider.service;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.facishare.open.ding.api.result.DingEnterpriseResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.provider.dingding.DingRequestUtil;
import com.facishare.open.ding.provider.dingding.DingUrl;
import com.facishare.open.ding.provider.manager.DingEnterpriseManager;
import com.facishare.open.ding.provider.manager.DingMappingEmployeeManager;
import com.fxiaoke.message.extrnal.platform.api.ExternalTodoService;
import com.fxiaoke.message.extrnal.platform.model.arg.CreateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DealTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.DeleteTodoArg;
import com.fxiaoke.message.extrnal.platform.model.arg.UpdateTodoArg;
import com.fxiaoke.message.extrnal.platform.model.result.CreateTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DealTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.DeleteTodoResult;
import com.fxiaoke.message.extrnal.platform.model.result.UpdateTodoResult;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019-09-16 20:23
 */
@Slf4j
@Service("externalTodoServiceImpl")
public class ExternalTodoServiceImpl implements ExternalTodoService{
    @Autowired
    private DingEnterpriseManager dingEnterpriseManager;

    @Autowired
    private DingMappingEmployeeManager employeeManager;

    private static final String MID_URL = "https://www.ceshi112.com/dingtalk/business/authorize?direct_uri=";

    //客户端
    private static final String DING_SINGLE_URL = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_auth&state=STATE";

    //网页版
    private static final String DING_SINGLE_URL_WEB = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?response_type=code&scope=snsapi_login&state=STATE";

    private static final String MESSAGE_TYPE = "action_card";

    @Override
    public CreateTodoResult createTodo(CreateTodoArg arg) {
//        if (Objects.isNull(arg)){
//            log.warn("sendMessage param is null");
//        }
//        Integer ei = arg.getEi();
//        Result<DingEnterpriseResult> enterpriseResult = dingEnterpriseManager.queryEnterpriseByEi(ei);
//        if (Objects.isNull(enterpriseResult) || Objects.isNull(enterpriseResult.getData())){
//            log.warn("the fx enterprise is not binded, ei={}.", ei);
//        }
//        List<Integer> empUsers = arg.getReceiverIds();
//        if ( CollectionUtils.isEmpty(empUsers)){
//            log.warn("toUser is empty, arg={}.", arg);
//        }
//        StringBuilder strBuilder = new StringBuilder();
//        for (Integer fxId : empUsers){
//            DingMappingEmployeeResult mappingEmployeeResult = employeeManager.queryMappingEmployeeByEi(ei, fxId);
//            if (Objects.isNull(mappingEmployeeResult)){
//                log.info("emp not bind,ei={},fxId={}", ei, fxId);
//                continue;
//            }
//            strBuilder.append(mappingEmployeeResult.getDingEmployeeId());
//        }
//        if (strBuilder.length() <= 0){
//            log.info("no user need to send message");
//        }
//        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
//        request.setUseridList(strBuilder.toString());
//        request.setAgentId(Long.parseLong(enterpriseResult.getData().getAgentId()));
//        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
//        msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
//        msg.setMsgtype(MESSAGE_TYPE);
//        msg.getActionCard().setTitle(arg.getTitle());
//        msg.getActionCard().setMarkdown(arg.getContent());
//        msg.getActionCard().setSingleTitle("客户端查看详情");
//        //url处理
//        String directAppId = enterpriseResult.getData().getRedirectAppId();
//        String directUri = MID_URL + URLEncoder.encode(arg.getUrl()) + "&ei=" + arg.getEi();
//        StringBuilder stringBuilder = new StringBuilder();
//        stringBuilder.append(DING_SINGLE_URL).append("&appid=").append(directAppId).append("&redirect_uri=").append(URLEncoder.encode(directUri));
//        msg.getActionCard().setSingleUrl(stringBuilder.toString());
//        request.setMsg(msg);
//        DingTalkClient client = new DefaultDingTalkClient(DingUrl.CORP_MESSAGE);
//        String appKey = enterpriseResult.getData().getAppKey();
//        String appSecret = enterpriseResult.getData().getAppSecret();
//        try {
//            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, DingRequestUtil.getToken(appKey, appSecret));
//            log.info("response={}",response);
//        } catch (ApiException e) {
//            log.warn("send message to dingding exception,e={}", e);
//        }
//        CreateTodoResult result= new CreateTodoResult();
//        result.setCode(200);
//        result.setMessage("发送成功");
//        return result;
        return null;
    }

    @Override
    public UpdateTodoResult updateTodo(UpdateTodoArg arg) {
        return null;
    }

    @Override
    public DealTodoResult dealTodo(DealTodoArg arg) {
        return null;
    }

    @Override
    public DeleteTodoResult deleteTodo(DeleteTodoArg arg) {
        return null;
    }
}
