package com.facishare.open.ding.web.utils;

import com.google.common.base.Splitter;
import com.google.common.collect.ImmutableSet;

import java.util.HashSet;
import java.util.Set;
import java.util.TreeMap;

public class DealPagesEiUtils {
    public static Set<String> pagesEiSet = new HashSet<>();
    public static TreeMap<Integer, Integer> rangeMap = new TreeMap<>();
    public static void addOrUpdatePagesEis(String config) {
        //","分隔
        Set<String> eiConfigSet = ImmutableSet.copyOf(Splitter.on(",").split(config));

        for(String stringFile : eiConfigSet) {
            if(stringFile.contains(";")) {
                //";"分隔
                Set<String> pagesEiSet2 = ImmutableSet.copyOf(Splitter.on(";").split(stringFile));
                for(String stringFile2 : pagesEiSet2) {
                    if(stringFile2.contains("-")) {
                        String[] bounds = stringFile2.split("-");
                        int startRange = Integer.parseInt(bounds[0]);
                        int endRange = Integer.parseInt(bounds[1]);
                        rangeMap.put(startRange, endRange);
                    } else {
                        //普通
                        pagesEiSet.add(stringFile2);
                    }
                }
            } else {
                pagesEiSet.add(stringFile);
            }
        }
    }

    public static Boolean isNewPagesEi(Integer ei) {
        if(pagesEiSet.contains(String.valueOf(ei))) {
            return Boolean.TRUE;
        }

        Integer lowerKey = rangeMap.floorKey(ei); // 最接近的开始边界（包含ei的）
        if(lowerKey == null) {
            return Boolean.FALSE; // 没有找到小于等于id的开始边界
        }

        Integer upperBoundary = rangeMap.get(lowerKey); // 对应的结束边界
        return ei >= lowerKey && ei <= upperBoundary; // ei必须在开始和结束边界之间
    }

    public static void main(String[] args) {
        //添加
        String config = "735924,784936,62036502,700146,784532,62005560,749018,747515,782879,767522,787891,749926,769785,769387,62040848,62005696,725153,774321,786720,762333,784547,786328,60000262,783584,67000054,62012093,62001925,788195,62001142,62001140,783104,781682,780230,756682,747730,62011867,62015398,771839,785014,776680,771791,62000980,62003179,705746,780404,773605,62036063,786470,787565,67000076,67000078,607429,782390,778859,780650,782433,722491,718797,746614,787054,674495,62000620,754999,738327,787619,592739,62026458,787578,749224,760995,725598,778040,767156,744295,759135,775723,733321,774912,62029632,781763,757059,62002158,787986,761797,778670,619615,766852,785296,762133,732561,642028,679487,770942,62003132,62005551,67000041,787633,614158,784011,784134,67000035,67000037,62001627,778938,788348,788354,788838,788943,789321,789501,789495,790010,738547,604452,62003282,790571,749720,738996,62042258,62042369,787007,755850,792362,62005613,62042520,792716,772047,793092,785823,757810,793413,590056,706644,794471,615153,794743,795132,795553,795536,795787,795783,795779,795899,62007012,62000620;590061-590063;62036935-63000000;772436-40000000;738327;60000097;707365;732136;62002883;62000069;62001773;";
        addOrUpdatePagesEis(config);
        Boolean newPagesEi = isNewPagesEi(700146);
        System.out.println(newPagesEi);
        Boolean newPagesEi2 = isNewPagesEi(62000069);
        System.out.println(newPagesEi2);
        Boolean newPagesEi3 = isNewPagesEi(772436);
        System.out.println(newPagesEi3);
        Boolean newPagesEi4 = isNewPagesEi(40000000);
        System.out.println(newPagesEi4);
        Boolean newPagesEi5 = isNewPagesEi(30000000);
        System.out.println(newPagesEi5);
    }
}
