package com.facishare.open.ding.provider.constants;

import java.util.HashMap;
import java.util.Map;

public class ObjectFieldMappingConfig {

    private static Map<String,Map<String,String>> objectFieldMappings=new HashMap<String,Map<String, String>>(){{
       put("AccountObj",new HashMap<String, String>(){{
           put("creator_userid","owner");
           put("customer_name","name");
           put("address","address");
           put("customer_phone","phone_number");
           put("email","email");
       }});
       put("ContactObj",new HashMap<String, String>(){{
           put("creator_userid","owner");
           put("contact_name","name");
           put("contact_related_customer","account_id");
           put("contact_phone","tel1");
       }});
    }};


    public static Map<String,String> getFieldMappingByObjectApiName(String objectApiName){
        return objectFieldMappings.get(objectApiName);
    }

}
