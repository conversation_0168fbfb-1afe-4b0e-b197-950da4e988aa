package com.facishare.open.ding.api.model;

import lombok.Data;

import java.io.Serializable;

@Data
public class SyncDataMappingModel implements Serializable {

    private Long id;
    private Integer ei;
    private String cropId;
    private String crmObjectApiName;
    private String dingObjectApiName;
    private String crmDataId;
    private String dingDataId;

    private Integer director;
    //同步状态1同步失败，2同步成功
    private Integer status;
    //数据是否已经创建：1未创建，2已经创建.3作废
    private Integer created=1;
    private String remark;
    private String mdStr;
    private Long createTime;
    private Long updateTime;
    private Integer version;
}
