package com.facishare.open.ding.transfer.handler;

import com.facishare.open.ding.provider.arg.DingRefuseDataEntity;
import com.facishare.open.ding.provider.dao.DingRefuseDataDao;
import com.facishare.open.oa.base.dbproxy.pg.entity.dingtalk.isv.DingRefuseDataPgEntity;
import com.facishare.open.oa.base.dbproxy.pg.mapper.dingtalkisv.DingRefuseDataPgMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 钉钉拒绝数据迁移处理器
 * <AUTHOR>
 * @date 2024/1/22 15:46:25
 */
@Component
public class DingTalkIsvDingRefuseDataHandler extends DingTalkIsvHandler<DingRefuseDataEntity, DingRefuseDataPgEntity> {

    @Autowired
    private DingRefuseDataDao dingRefuseDataDao;

    @Autowired
    private DingRefuseDataPgMapper dingRefuseDataPgMapper;

    @Override
    protected void update(int enterpriseId, DingRefuseDataEntity sourceData, DingRefuseDataPgEntity targetData) {
        final DingRefuseDataPgEntity entity = convert2PgEntity(sourceData, targetData);
        if (Objects.isNull(entity)) {
            return;
        }
        if (Objects.isNull(targetData)) {
            dingRefuseDataPgMapper.insert(entity);
        } else {
            dingRefuseDataPgMapper.updateById(entity);
        }
    }

    private DingRefuseDataPgEntity convert2PgEntity(DingRefuseDataEntity sourceData, DingRefuseDataPgEntity targetData) {
        final DingRefuseDataPgEntity entity = new DingRefuseDataPgEntity();
        // 保持ID一致
        entity.setId(sourceData.getId());
        entity.setBizData(sourceData.getBizData());
        entity.setCorpId(sourceData.getCorpId());
        entity.setEi(sourceData.getEi());
        entity.setRefuseAppId(sourceData.getRefuseAppId());
        entity.setRefuseSuiteId(sourceData.getRefuseSuiteId());
        entity.setActiveStatus(sourceData.getActiveStatus());
        entity.setOrderId(sourceData.getOrderId());
        return entity;
    }

    @Override
    protected boolean checkDataEquals(DingRefuseDataEntity sourceData, DingRefuseDataPgEntity targetData) {
        if (Objects.isNull(targetData)) {
            return false;
        }

        return Objects.equals(targetData.getBizData(), sourceData.getBizData()) &&
                Objects.equals(targetData.getCorpId(), sourceData.getCorpId()) &&
                Objects.equals(targetData.getEi(), sourceData.getEi()) &&
                Objects.equals(targetData.getRefuseAppId(), sourceData.getRefuseAppId()) &&
                Objects.equals(targetData.getRefuseSuiteId(), sourceData.getRefuseSuiteId()) &&
                Objects.equals(targetData.getActiveStatus(), sourceData.getActiveStatus()) &&
                Objects.equals(targetData.getOrderId(), sourceData.getOrderId());
    }

    @Override
    protected DingRefuseDataPgEntity getTargetData(int enterpriseId, DingRefuseDataEntity k) {
        return dingRefuseDataPgMapper.selectById(k.getId());
    }

    @Override
    protected List<DingRefuseDataEntity> getSourceDataPage(Integer enterpriseId, DingRefuseDataEntity maxId) {
        Long id = Objects.isNull(maxId) ? null : maxId.getId();
        return dingRefuseDataDao.pageById(enterpriseId, id, 1000);
    }
} 