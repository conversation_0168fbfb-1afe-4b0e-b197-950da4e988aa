package com.facishare.open.ding.provider.manager;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.facishare.converter.EIEAConverter;
import com.facishare.open.ding.api.enums.EmpStatusEnum;
import com.facishare.open.ding.api.result.BindFxUserResult;
import com.facishare.open.ding.api.result.DingMappingEmployeeResult;
import com.facishare.open.ding.api.vo.DeptVo;
import com.facishare.open.ding.common.model.User;
import com.facishare.open.ding.common.result.Result;
import com.facishare.open.ding.common.result.ResultCode;
import com.facishare.open.ding.provider.service.DingDeptService;
import com.facishare.open.oa.base.dbproxy.manager.ObjectDataManager;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEmployeeDataEntity;
import com.facishare.open.oa.base.dbproxy.pg.entity.OuterOaEnterpriseBindEntity;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeBindManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEmployeeDataManager;
import com.facishare.open.oa.base.dbproxy.pg.manager.OuterOaEnterpriseBindManager;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeBindMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEmployeeDataMapper;
import com.facishare.open.oa.base.dbproxy.pg.mapper.OuterOaEnterpriseBindMapper;
import com.facishare.open.outer.oa.connector.common.api.enums.BindStatusEnum;
import com.facishare.open.outer.oa.connector.common.api.enums.ChannelEnum;
import com.facishare.open.outer.oa.connector.common.api.object.DingTalkEmployeeObject;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2018/7/16 10:48
 */
@Slf4j
@Component
public class DingMappingEmployeeManager {

    @Autowired
    private DingDeptService dingDeptService;

    @Autowired
    private EIEAConverter eieaConverter;

    @Autowired
    private OuterOaEmployeeBindMapper outerOaEmployeeBindMapper;

    @Autowired
    private OuterOaEnterpriseBindManager outerOaEnterpriseBindManager;

    @Autowired
    private OuterOaEmployeeDataMapper outerOaEmployeeDataMapper;

    @Autowired
    private OuterOaEmployeeBindManager outerOaEmployeeBindManager;

    @Autowired
    private OuterOaEmployeeDataManager outerOaEmployeeDataManager;

    @Autowired
    private ObjectDataManager objectDataManager;
    @Autowired
    private OuterOaEnterpriseBindMapper outerOaEnterpriseBindMapper;

    /**
     * 将JSONObject转换为EmployeeMongoData
     *
     * @param jsonObject JSONObject对象
     * @return EmployeeMongoData对象
     */
    private DingTalkEmployeeObject convertToEmployeeMongoData(JSONObject jsonObject) {
        if (jsonObject == null) {
            return new DingTalkEmployeeObject();
        }
        return jsonObject.toJavaObject(DingTalkEmployeeObject.class);
    }

    /**
     * 将EmployeeMongoData转换为JSONObject
     *
     * @param employeeData EmployeeMongoData对象
     * @return JSONObject对象
     */
    private JSONObject convertToJsonObject(DingTalkEmployeeObject employeeData) {
        if (employeeData == null) {
            return new JSONObject();
        }
        return JSONObject.parseObject(JSON.toJSONString(employeeData));
    }

    public Result<List<Integer>> queryEmpList(Integer ei, String appId) {
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        return Result.newSuccess(outerOaEmployeeBindMapper.queryEmpIdByEi(ChannelEnum.dingding.name(), ea, appId));
    }

    /**
     /**
     * 条件查询员工
     */
    public Result<List<DingMappingEmployeeResult>> conditionQueryEmployee(Integer ei, String appId, Integer bindStatus, Integer pageNumber, Integer pageSize, String dingNameOrPhone, Long dingDeptId) {
        if (ei == null || StringUtils.isEmpty(appId)) {
            log.warn("conditionQueryEmployee param illegal, ei=[{}], appId=[{}].", ei, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        Integer offset = null;
        if (Objects.nonNull(pageNumber) && Objects.nonNull(pageSize)) {
            offset = (pageNumber - 1) * pageSize;
        }
        //递归查询子部门
        List<String> deptIds = null;
        if (dingDeptId != null) {
            // 递归获取部门子部门
            List<DeptVo> depts = dingDeptService.getDeptByEI(fsEa, appId);
            if (CollectionUtils.isEmpty(depts)) {
                return Result.newSuccess(new ArrayList<>());
            }
            List<Long> dingParentIds = Lists.newArrayList(dingDeptId);
            List<Long> longDeptIds = recallChildDepts(depts, dingParentIds, new ArrayList<>());
            longDeptIds.add(dingDeptId);
            deptIds = longDeptIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        // 绑定状态
        final BindStatusEnum bindStatusEnum = bindStatus != null ? BindStatusEnum.convertDingtalkEmployeeBindStatus(bindStatus) : null;

        // 使用联表查询
        List<Map<String, Object>> results = outerOaEmployeeBindMapper.queryDingEmployeeBindWithFilters(
                ChannelEnum.dingding.name(),
                bindStatusEnum,
                outEa,
                fsEa,
                appId,
                dingNameOrPhone,
                deptIds,
                pageSize,
                offset
        );

        if (CollectionUtils.isEmpty(results)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为Result对象
        List<DingMappingEmployeeResult> employeeResults = results.stream()
                .map(result -> convertMapToResult(result, fsEa))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return Result.newSuccess(employeeResults);
    }

    /**
     * 条件查询总数
     */
    public Integer conditionEmployeeCount(Integer ei, String appId, Integer bindStatus, String dingNameOrPhone, Long dingDeptId) {
        if (ei == null || StringUtils.isEmpty(appId)) {
            log.warn("conditionEmployeeCount param illegal, ei=[{}], appId=[{}].", ei, appId);
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return 0;
        }

        // 部门ID列表
        List<String> deptIds = null;
        if (dingDeptId != null) {
            dingDeptId = Optional.ofNullable(dingDeptId).orElse(1L);
            List<DeptVo> depts = dingDeptService.getDeptByEI(fsEa, appId);
            if (CollectionUtils.isEmpty(depts)) {
                return 0;
            }
            List<Long> dingParentIds = Lists.newArrayList(dingDeptId);
            List<Long> longDeptIds = recallChildDepts(depts, dingParentIds, new ArrayList<>());
            longDeptIds.add(dingDeptId);
            deptIds = longDeptIds.stream().map(String::valueOf).collect(Collectors.toList());
        }

        // 查询总数
        return outerOaEmployeeBindMapper.countDingEmployeeBindWithFilters(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                dingNameOrPhone,
                deptIds,
                Objects.isNull(bindStatus) ? null : BindStatusEnum.convertDingtalkEmployeeBindStatus(bindStatus));
    }

    private List<Long> recallChildDepts(List<DeptVo> dingDeptEntities, List<Long> dingParentIds, List<Long> ids) {
        if (!CollectionUtils.isEmpty(dingParentIds)) {
            for (int i = 0; i < dingParentIds.size(); i++) {
                Long id = dingParentIds.get(i);
                List<Long> deptS = dingDeptEntities.stream().filter(item -> id.equals(item.getDingParentId())).map(DeptVo::getDingDeptId).collect(Collectors.toList());
                List<Long> dingPars = Lists.newArrayList();
                dingPars.addAll(deptS);
                ids.addAll(deptS);
                recallChildDepts(dingDeptEntities, deptS, ids);
            }
        }
        return ids;
    }

    @Transactional
    public Integer initMappingEmployee(List<User> list, Integer ei, Long dingDeptId, String dingDeptName, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("initMappingEmployee参数列表为空");
            return 0;
        }
        // 创建一个列表来收集所有要更新的员工数据实体
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return 0;
        }

        for (User user : list) {
            // 创建员工数据
            DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
            employeeData.setUserid(user.getUserid());
            employeeData.setName(user.getName());
            employeeData.setPhone(user.getMobile());
            employeeData.setDeptId(dingDeptId);
            employeeData.setDeptName(dingDeptName);
            employeeData.setEmail(user.getEmail());
            employeeData.setUnionId(user.getUnionid());

            // 添加到集合而不是直接插入
            dataEntities.add(employeeData);
        }

        // 批量更新或插入员工数据
        return outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
    }

    @Transactional
    public Integer insertModeEmployee(List<User> userList, Integer ei, Long dingDeptId, Integer userId, String dingDeptName, Integer crmDeptId, String appId) {
        if (CollectionUtils.isEmpty(userList)) {
            log.warn("insertModeEmployee param list is null.");
            return 0;
        }

        return initMappingEmployee(userList, ei, dingDeptId, dingDeptName, appId);
    }

    @Transactional
    public Integer insertAllModelData(List<DingMappingEmployeeResult> list, Integer ei, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("insertAllModelData param list is null.");
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        List<OuterOaEmployeeBindEntity> bindEntities = list.stream()
                .map(employee -> {
                    OuterOaEmployeeBindEntity entity = new OuterOaEmployeeBindEntity();
                    entity.setChannel(ChannelEnum.dingding);
                    entity.setFsEa(fsEa);
                    entity.setOutEa(outEa);
                    entity.setAppId(appId);
                    entity.setDcId(dcId);
                    entity.setFsEmpId(String.valueOf(employee.getEmployeeId()));
                    entity.setOutEmpId(employee.getDingEmployeeId());
                    entity.setBindStatus(BindStatusEnum.normal);
                    entity.setCreateTime(System.currentTimeMillis());
                    entity.setUpdateTime(System.currentTimeMillis());
                    return entity;
                })
                .collect(Collectors.toList());

        if (bindEntities.isEmpty()) {
            return 0;
        }

        Integer count = outerOaEmployeeBindManager.batchUpsert(bindEntities);


        List<DingTalkEmployeeObject> dataEntities = list.stream()
                .map(employee -> {
                    DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
                    employeeData.setStatus(employee.getDingEmployeeStatus());
                    employeeData.setName(employee.getDingEmployeeName());
                    employeeData.setPhone(employee.getDingEmployeePhone());
                    employeeData.setUnionId(employee.getDingUnionId());
                    employeeData.setDeptId(employee.getDingDeptId());
                    employeeData.setDeptName(employee.getDingDeptName());
                    employeeData.setPhone(employee.getDingEmployeePhone());
                    return employeeData;
                })
                .collect(Collectors.toList());

        if (!dataEntities.isEmpty()) {
            outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
        }

        return count;
    }

    /**
     * 批量保存职员
     *
     * @param list
     * @param appId
     * @return 设置超时2s, 钉钉更新员工的时候会发起多次请求
     */
    public Integer saveMappingEmployee(List<DingMappingEmployeeResult> list, String appId) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("saveMappingEmployee param list is null.");
            return 0;
        }
        // 创建一个列表来收集所有要更新的员工数据实体
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();
        List<OuterOaEmployeeBindEntity> bindEntities = new ArrayList<>();

        final Integer ei = list.get(0).getEi();
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        for (DingMappingEmployeeResult employee : list) {

            DingTalkEmployeeObject dataEntity = convert2DingTalkEmployeeObject(employee);
            // 添加到集合而不是直接插入
            dataEntities.add(dataEntity);

            // 只绑定有员工id的
            if (employee.getEmployeeId() != null) {
                final OuterOaEmployeeBindEntity bindEntity = convert2OuterOaEmployeeBindEntity(fsEa, dcId, outEa, appId, employee);
                bindEntities.add(bindEntity);
            }
        }
        log.info("update mapping dataEntities :{} bindEntities:{}", dataEntities, bindEntities);

        // 批量更新或插入员工数据
        outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);

        return outerOaEmployeeBindManager.batchUpsert(bindEntities);
    }

    @NotNull
    private OuterOaEmployeeBindEntity convert2OuterOaEmployeeBindEntity(String fsEa, String dcId, String outEa, String appId, DingMappingEmployeeResult employee) {
        OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
        bindEntity.setDcId(dcId);
        bindEntity.setOutEa(outEa);
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(fsEa);
        bindEntity.setAppId(appId);
        bindEntity.setDcId(dcId);
        bindEntity.setOutEmpId(employee.getDingEmployeeId());
        bindEntity.setFsEmpId(String.valueOf(employee.getEmployeeId()));
        bindEntity.setBindStatus(BindStatusEnum.normal);
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());
        return bindEntity;
    }

    /**
     * 解绑
     *
     * @param dingEmployeeId
     * @return
     */
    @Transactional
    public Integer relieveBind(Integer ei, String dingEmployeeId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId)) {
            log.warn("deleteBind param ei or dingEmployeeId is null.");
            return 0;
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        return outerOaEmployeeBindManager.updateStatusByFsEaAndOutEmpId(ChannelEnum.dingding, fsEa, appId, dingEmployeeId, BindStatusEnum.stop);
    }

    /**
     * 删除绑定关系
     * 理论上,不应该有删除逻辑,唯一的删除逻辑在页面的删除上,在新接口上
     */
    @Deprecated
    @Transactional
    public Integer deleteBind(Integer ei, String dingEmployeeId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId)) {
            log.warn("deleteBind param ei or dingEmployeeId is null.");
            return 0;
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        return outerOaEmployeeBindManager.deleteByOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                dingEmployeeId
        );
    }

    /**
     * 根据纷享ID删除绑定关系
     *
     * @param ei              企业ID
     * @param fsEmployeeIds 纷享员工ID
     * @return 删除的记录数
     */
    @Transactional
    public Integer deleteBindByFsId(Integer ei, List<Integer> fsEmployeeIds) {
        if (Objects.isNull(ei) || Objects.isNull(fsEmployeeIds)) {
            log.warn("deleteBindByFsId param ei or fsEmployeeId is null.");
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final List<String> empIds = fsEmployeeIds.stream().map(String::valueOf).collect(Collectors.toList());
        return outerOaEnterpriseBindMapper.updateStatusBySFsEaAndFsEmpId(ChannelEnum.dingding, fsEa, empIds, BindStatusEnum.stop, System.currentTimeMillis());
    }

    /**
     * 根据部门ID删除绑定关系
     *
     * @param ei             企业ID
     * @param dingEmployeeId 钉钉员工ID
     * @param dingDeptId     钉钉部门ID
     * @param appId
     * @return 删除的记录数
     */
    @Transactional
    public Integer deleteByDeptId(Integer ei, String dingEmployeeId, Long dingDeptId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId) || Objects.isNull(dingDeptId)) {
            log.warn("deleteByDeptId param illegal: ei=[{}], dingEmployeeId=[{}], deptId=[{}]", ei, dingEmployeeId, dingDeptId);
            return 0;
        }

        // Convert enterprise account
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // Find employee data with matching department
        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataMapper.selectOne(
                new LambdaQueryWrapper<OuterOaEmployeeDataEntity>()
                        .eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                        .eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                        .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                        .eq(OuterOaEmployeeDataEntity::getOutUserId, dingEmployeeId)
                        .eq(OuterOaEmployeeDataEntity::getOutDeptId, String.valueOf(dingDeptId))
        );

        if (dataEntity != null) {
            return outerOaEmployeeBindManager.updateStatusByFsEaAndOutEmpId(ChannelEnum.dingding,
                    fsEa,
                    appId,
                    dingEmployeeId,
                    BindStatusEnum.stop);
        }

        return 0;
    }

    public Integer physicalDeleteByDeptId(Integer ei, String dingEmployeeId, Long dingDeptId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmployeeId) || Objects.isNull(dingDeptId)) {
            log.warn("deleteByDeptId param illegal: ei=[{}], dingEmployeeId=[{}], deptId=[{}]", ei, dingEmployeeId, dingDeptId);
            return 0;
        }

        // Convert enterprise account
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // Find employee data with matching department
        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataMapper.selectOne(
                new LambdaQueryWrapper<OuterOaEmployeeDataEntity>()
                        .eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                        .eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                        .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                        .eq(OuterOaEmployeeDataEntity::getOutUserId, dingEmployeeId)
                        .eq(OuterOaEmployeeDataEntity::getOutDeptId, String.valueOf(dingDeptId))
        );

        if (dataEntity == null) {
            return 0;
        }

        return outerOaEmployeeBindManager.deleteEmployeeBindEntity(ChannelEnum.dingding, outEa, fsEa, appId, null, Collections.singletonList(dingEmployeeId));
    }



    /**
     * 根据fsEmpId和appId查询员工信息
     */
    public DingMappingEmployeeResult queryMappingEmployeeByEi(Integer ei, Integer fxEmpId, String appId) {
        if (Objects.isNull(fxEmpId)) {
            log.warn("queryMappingEmployeeByEi param fxEmpId is null.");
            return null;
        }

        Result<DingMappingEmployeeResult> result = findById(ei, fxEmpId, appId);
        return result.isSuccess() ? result.getData() : null;
    }

    /**
     * 更新绑定关系表中钉钉员工
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    public Integer updateDingEmpByDingId(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("updateDingEmpByDingId param dingMappingEmployeeResult is null.");
            return 0;
        }

        // 调用saveMappingEmployee方法处理单个员工
        List<DingMappingEmployeeResult> employees = Collections.singletonList(dingMappingEmployeeResult);
        return saveMappingEmployee(employees, appId);
    }

    /**
     * 更新钉钉员工模型
     *
     * @param dingMappingEmployeeResult 钉钉员工映射结果
     * @param appId
     * @return 更新的记录数
     */
    @Transactional
    public Integer updateModelEmp(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("updateModelEmp param dingMappingEmployeeResult is null.");
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(dingMappingEmployeeResult.getEi());
        final String dcId = outerOaEnterpriseBindManager.getDcIdByEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        // 使用EmployeeMongoData
        DingTalkEmployeeObject employeeData = convert2DingTalkEmployeeObject(dingMappingEmployeeResult);

        // 使用批量更新方法
        List<DingTalkEmployeeObject> dataEntities = Collections.singletonList(employeeData);
        return outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);
    }

    /**
     * 查询绑定关系表中钉钉员工是否已经存在
     *
     * @param dingMappingEmployeeResult
     * @param appId
     * @return
     */
    public DingMappingEmployeeResult findIsBindByDingId(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("findIsBindByDingId param dingMappingEmployeeResult is null.");
            return null;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(dingMappingEmployeeResult.getEi());

        // 查询绑定关系
        OuterOaEmployeeBindEntity bindEntity = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                dingMappingEmployeeResult.getDingEmployeeId());

        return convertToResult(bindEntity);
    }

    /**
     * 根据dingEmpId返回列表的数据
     *
     * @param ei,dingEmpId
     * @param appId
     * @return
     */
    public List<DingMappingEmployeeResult> findDingEmpIdList(Integer ei, String dingEmpId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmpId)) {
            log.warn("findDingEmpIdList param illegal: ei=[{}], dingEmpId=[{}]", ei, dingEmpId);
            return Collections.emptyList();
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        final OuterOaEmployeeBindEntity employeeBindEntity = outerOaEmployeeBindManager.getNormalEmployeeBindEntity(ChannelEnum.dingding, outEa, fsEa, appId, dingEmpId);

        final DingMappingEmployeeResult dingMappingEmployeeResult = convertToResult(employeeBindEntity);
        return Objects.isNull(dingMappingEmployeeResult) ? new ArrayList<>() : Lists.newArrayList(dingMappingEmployeeResult);
    }


    public DingMappingEmployeeResult findByDingEmpId(Integer ei, String dingEmpId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingEmpId) || StringUtils.isEmpty(appId)) {
            log.warn("findByDingEmpId param illegal, ei=[{}], dingEmpId=[{}], appId=[{}]", ei, dingEmpId, appId);
            return null;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return null;
        }

        // 使用联表查询
        Map<String, Object> result = outerOaEmployeeBindMapper.queryEmployeeBindByOutEmpId(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                dingEmpId
        );

        if (result == null || result.isEmpty()) {
            return null;
        }

        return convertMapToResult(result, fsEa);
    }

    /**
     * 绑定关系表中新增钉钉员工 第一种模式
     *
     * @param dingMappingEmployeeResult
     * @return
     */
    public Integer insertDingEmp(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("insertDingEmp param dingMappingEmployeeResult is null.");
            return 0;
        }

        // 调用saveMappingEmployee方法处理单个员工
        List<DingMappingEmployeeResult> employees = Collections.singletonList(dingMappingEmployeeResult);
        return saveMappingEmployee(employees, appId);
    }

    /**
     * 钉钉云模式插入员工
     */
    public Integer insertModelEmp(DingMappingEmployeeResult dingMappingEmployeeResult, String appId) {
        if (Objects.isNull(dingMappingEmployeeResult)) {
            log.warn("insertDingEmp param dingMappingEmployeeResult is null.");
            return 0;
        }

        Integer ei = dingMappingEmployeeResult.getEi();
        Integer employeeId = dingMappingEmployeeResult.getEmployeeId();

        if (ei == null || employeeId == null) {
            return 0;
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity enterpriseBindEntity = outerOaEnterpriseBindManager.getByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final String outEa = enterpriseBindEntity.getOutEa();
        final String dcId = enterpriseBindEntity.getId();
        if (StringUtils.isEmpty(outEa)) {
            return 0;
        }
        String fsEmpId = String.valueOf(employeeId);

        // 检查员工是否已被绑定
        OuterOaEmployeeBindEntity existingBind = outerOaEmployeeBindManager.queryByFsEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                fsEmpId
        );

        if (existingBind != null) {
            log.warn("该纷享职员已被绑定！employeeName=[{}],employeePhone=[{}].",
                    dingMappingEmployeeResult.getEmployeeName(),
                    dingMappingEmployeeResult.getEmployeePhone());
            return 0;
        }

        // 设置绑定状态
        if (Objects.nonNull(dingMappingEmployeeResult.getEmployeeId())) {
            dingMappingEmployeeResult.setBindStatus(EmpStatusEnum.BIND.getStatus());
        }

        // 创建员工数据
        final DingTalkEmployeeObject employeeData = convert2DingTalkEmployeeObject(dingMappingEmployeeResult);

        // 使用批量更新方法
        List<DingTalkEmployeeObject> dataEntities = Collections.singletonList(employeeData);
        outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, dcId);

        // 创建新的绑定关系
        final OuterOaEmployeeBindEntity bindEntity = convert2OuterOaEmployeeBindEntity(dingMappingEmployeeResult, appId, fsEa, outEa, dcId, fsEmpId);
        return outerOaEmployeeBindManager.batchUpsert(Lists.newArrayList(bindEntity));
    }

    @NotNull
    private static OuterOaEmployeeBindEntity convert2OuterOaEmployeeBindEntity(DingMappingEmployeeResult dingMappingEmployeeResult, String appId, String fsEa, String outEa, String dcId, String fsEmpId) {
        OuterOaEmployeeBindEntity bindEntity = new OuterOaEmployeeBindEntity();
        bindEntity.setId(IdGenerator.get());
        bindEntity.setChannel(ChannelEnum.dingding);
        bindEntity.setFsEa(fsEa);
        bindEntity.setOutEa(outEa);
        bindEntity.setAppId(appId);
        bindEntity.setDcId(dcId);
        bindEntity.setFsEmpId(fsEmpId);
        bindEntity.setOutEmpId(dingMappingEmployeeResult.getDingEmployeeId());
        bindEntity.setBindStatus(BindStatusEnum.convertDingtalkEmployeeBindStatus(dingMappingEmployeeResult.getBindStatus()));
        bindEntity.setCreateTime(System.currentTimeMillis());
        bindEntity.setUpdateTime(System.currentTimeMillis());
        return bindEntity;
    }


    /**
     * 通过unionid查询钉钉员工
     *
     * @param ei
     * @param unionId
     * @param appId
     * @return
     */
    public Result<DingMappingEmployeeResult> queryEmpByUnionId(Integer ei, String unionId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(unionId)) {
            log.warn("queryEmpByUnionId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);

        final OuterOaEmployeeDataEntity outerOaEmployeeDataEntity = outerOaEmployeeDataMapper.selectDingByUnionId(ChannelEnum.dingding.name(), outEa, appId, unionId);
        if (Objects.isNull(outerOaEmployeeDataEntity)) {
            return Result.newSuccess(null);
        }

        // 查询所有绑定关系
        final OuterOaEmployeeBindEntity outerOaEmployeeBindEntity = outerOaEmployeeBindManager.queryNormalByFsEaAndOutEmpId(
                ChannelEnum.dingding,
                fsEa,
                appId,
                outerOaEmployeeDataEntity.getOutUserId()
        );


        return Result.newSuccess(convertToResult(outerOaEmployeeBindEntity, convertToEmployeeMongoData(outerOaEmployeeDataEntity.getOutUserInfo())));
    }

    /**
     * 通过钉钉userId查询钉钉员工
     *
     * @param ei
     * @param dingUserId
     * @return
     */
    public Result<DingMappingEmployeeResult> queryEmpByDingUserId(Integer ei, String dingUserId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(dingUserId)) {
            log.warn("queryEmpByDingUserId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }


        // 使用findByDingEmpId方法查询
        DingMappingEmployeeResult result = findByDingEmpId(ei, dingUserId, appId);
        return Result.newSuccess(result);
    }

    public Result<List<OuterOaEmployeeBindEntity>> queryEmpByDingUserId(String unionId, String appId) {
        if (Objects.isNull(unionId)) {
            log.warn("queryEmpByUnionId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        final OuterOaEmployeeDataEntity dataEntity = outerOaEmployeeDataMapper.selectDingByUnionId(ChannelEnum.dingding.name(), null, appId, unionId);
        if (Objects.isNull(dataEntity)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 查询对应的绑定关系
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> bindQueryWrapper = new LambdaQueryWrapper<>();
        bindQueryWrapper.eq(OuterOaEmployeeBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getOutEa, dataEntity.getOutEa())
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal)
                .eq(OuterOaEmployeeBindEntity::getOutEmpId, dataEntity.getOutUserId());
        return Result.newSuccess(outerOaEmployeeBindMapper.selectList(bindQueryWrapper));
    }


    /**
     * 通过crm员工id查询
     *
     * @param ei
     * @param appId
     * @param crmUserIds
     * @return
     */
    public Result<List<DingMappingEmployeeResult>> batchQueryMapping(Integer ei, String appId, List<Integer> crmUserIds) {
        if (Objects.isNull(ei) || Objects.isNull(crmUserIds)) {
            log.warn("queryEmpByUnionId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final List<OuterOaEmployeeBindEntity> outerOaEmployeeBindEntities = outerOaEmployeeBindManager.batchGetNormalEmployeeBindEntity(ChannelEnum.dingding, outEa, fsEa, appId, crmUserIds.stream().map(String::valueOf).collect(Collectors.toList()), null);
        final List<DingMappingEmployeeResult> collect = outerOaEmployeeBindEntities.stream().map(this::convertToResult).collect(Collectors.toList());
        return Result.newSuccess(collect);
    }


    /**
     * 根据crmId查询信息
     */
    public Result<List<DingMappingEmployeeResult>> fixNoMainDeptByUserID(Integer ei, List<Integer> userIds, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(userIds)) {
            log.warn("queryEmpByUnionId param illegal");
            return Result.newError(ResultCode.PARAMS_ERROR);
        }
        final String ea = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, ea, appId);
        final List<String> ids = userIds.stream().map(String::valueOf).collect(Collectors.toList());
        final List<Map<String, Object>> maps = outerOaEmployeeBindMapper.batchQueryEmployeeBindByFsEmpIds(ChannelEnum.dingding.name(), outEa, ea, appId, ids);
        final List<DingMappingEmployeeResult> results = maps.stream().map(result -> convertMapToResult(result, ea)).collect(Collectors.toList());
        return Result.newSuccess(results);
    }


    /**
     * 分页查询绑定了的纷享的员工
     *
     * @param offset
     * @param limit
     * @return
     */
    public Result<List<BindFxUserResult>> queryBindFxEmp(Integer offset, Integer limit, String appId) {
        if (offset == null || limit == null) {
            log.warn("queryBindFxEmp param error: offset=[{}], limit=[{}]", offset, limit);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 分页查询所有绑定状态为normal的绑定关系
        Page<OuterOaEmployeeBindEntity> page = new Page<>(offset / limit + 1, limit);
        LambdaQueryWrapper<OuterOaEmployeeBindEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaEmployeeBindEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeBindEntity::getAppId, appId)
                .eq(OuterOaEmployeeBindEntity::getBindStatus, BindStatusEnum.normal)
                .orderByDesc(OuterOaEmployeeBindEntity::getUpdateTime);

        outerOaEmployeeBindMapper.selectPage(page, queryWrapper);

        List<OuterOaEmployeeBindEntity> bindEntities = page.getRecords();
        if (CollectionUtils.isEmpty(bindEntities)) {
            return Result.newSuccess(new ArrayList<>());
        }

        // 转换为BindFxUserResult
        List<BindFxUserResult> results = new ArrayList<>();
        for (OuterOaEmployeeBindEntity bindEntity : bindEntities) {
            BindFxUserResult result = new BindFxUserResult();
            result.setEi(eieaConverter.enterpriseAccountToId(bindEntity.getFsEa()));
            result.setEmployeeId(Integer.valueOf(bindEntity.getFsEmpId()));
            results.add(result);
        }

        return Result.newSuccess(results);
    }

    /**
     * 批量更新员工
     */
    public Result<Integer> batchUpdate(Integer ei, List<User> userList, String appId) {
        if (CollectionUtils.isEmpty(userList)) {
            return Result.newSuccess(0);
        }

        final String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final OuterOaEnterpriseBindEntity entity = outerOaEnterpriseBindManager.getEntity(ChannelEnum.dingding, fsEa, null, appId);
        List<DingTalkEmployeeObject> dataEntities = new ArrayList<>();
        for (User user : userList) {
            if (StringUtils.isEmpty(user.getUserid())) {
                continue;
            }

            // 获取用户数据
            OuterOaEmployeeDataEntity dataEntity = getEmployeeDataByOutUserId(
                    entity.getOutEa(),
                    appId,
                    user.getUserid()
            );

            if (dataEntity == null) {
                continue;
            }

            // 获取现有用户信息并转换为EmployeeMongoData
            DingTalkEmployeeObject employeeData = convertToEmployeeMongoData(dataEntity.getOutUserInfo());
            if (employeeData == null) {
                employeeData = new DingTalkEmployeeObject();
            }

            // 更新信息
            employeeData.setUserid(user.getUserid());
            employeeData.setName(user.getName());
            employeeData.setPhone(user.getMobile());

            // 保存更新
            dataEntities.add(employeeData);
        }
        Integer count = outerOaEmployeeDataManager.batchUpsert(dataEntities, ChannelEnum.dingding, entity.getId());

        return Result.newSuccess(count);
    }

    /**
     * 修改员工的状态
     */
    public Result<Integer> batchUpdateEmpStatus(Integer status, Integer ei, List<Integer> empIds, String appId) {
        if (CollectionUtils.isEmpty(empIds)) {
            return Result.newSuccess();
        }
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        Integer count = outerOaEmployeeBindMapper.batchUpdateEmpStatus(ChannelEnum.dingding.name(), fsEa, appId, BindStatusEnum.convertDingtalkEmployeeBindStatus(status).name(), empIds.stream().map(String::valueOf).collect(Collectors.toList()));
        return Result.newSuccess(count);
    }

    public Result<List<DingMappingEmployeeResult>> batchGetEmpIds(Integer ei, List<Integer> empIds, String appId) {
        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        final List<Map<String, Object>> maps = outerOaEmployeeBindMapper.batchQueryEmployeeBindByFsEmpIds(ChannelEnum.dingding.name(), outEa, fsEa, appId, empIds.stream().map(String::valueOf).collect(Collectors.toList()));
        final List<DingMappingEmployeeResult> results = maps.stream().map(result -> convertMapToResult(result, fsEa)).collect(Collectors.toList());
        return Result.newSuccess(results);
    }


    public Result<List<DingMappingEmployeeResult>> queryEmpsBindByOutDeptId(Integer ei, String appId, String outEa, Long outDeptId) {
        return null;
    }

    private Integer convertBindStatus(BindStatusEnum bindStatus) {
        if (bindStatus == null) {
            return EmpStatusEnum.INIT.getStatus();
        }
        switch (bindStatus) {
            case create:
                return EmpStatusEnum.INIT.getStatus();
            case normal:
                return EmpStatusEnum.BIND.getStatus();
            default:
                return EmpStatusEnum.NOTBIND.getStatus();
        }
    }

    /**
     * 将联表查询结果Map转换为DingMappingEmployeeResult对象
     */
    private DingMappingEmployeeResult convertMapToResult(Map<String, Object> result, String fsEa) {
        if (result == null) {
            return null;
        }

        DingMappingEmployeeResult dingResult = new DingMappingEmployeeResult();
        // 设置基本信息
        dingResult.setEi(eieaConverter.enterpriseAccountToId(fsEa));

        // 设置员工ID
        String fsEmpId = (String) result.get("fs_emp_id");
        if (!StringUtils.isEmpty(fsEmpId)) {
            dingResult.setEmployeeId(Integer.valueOf(fsEmpId));
        }

        // 设置绑定状态
        String bindStatusStr = (String) result.get("bind_status");
        if (!StringUtils.isEmpty(bindStatusStr)) {
            dingResult.setBindStatus(convertBindStatus(BindStatusEnum.valueOf(bindStatusStr)));
        }

        // 设置时间
        Long createTime = (Long) result.get("create_time");
        if (createTime != null) {
            dingResult.setCreateTime(new Date(createTime));
        }

        Long updateTime = (Long) result.get("update_time");
        if (updateTime != null) {
            dingResult.setUpdateTime(new Date(updateTime));
        }

        // 解析员工信息JSON
        String outUserInfoJson = (String) result.get("out_user_info");
        if (!StringUtils.isEmpty(outUserInfoJson)) {
            JSONObject outUserInfo = JSONObject.parseObject(outUserInfoJson);
            if (outUserInfo != null) {
                DingTalkEmployeeObject employeeData = convertToEmployeeMongoData(outUserInfo);
                if (employeeData != null) {
                    dingResult.setDingEmployeeId(employeeData.getUserid());
                    dingResult.setDingEmployeeName(employeeData.getName());
                    dingResult.setDingEmployeePhone(employeeData.getPhone());
                    dingResult.setDingDeptId(employeeData.getDeptId());
                    dingResult.setDingDeptName(employeeData.getDeptName());
                    dingResult.setDingEmployeeStatus(employeeData.getStatus());
                    dingResult.setDingUnionId(employeeData.getUnionId());
                }
            }
        } else {
            // 如果没有用户信息JSON，则直接从result中获取钉钉员工ID
            String outEmpId = (String) result.get("out_emp_id");
            if (!StringUtils.isEmpty(outEmpId)) {
                dingResult.setDingEmployeeId(outEmpId);
            }
        }

        return dingResult;
    }

    /**
     * 将PG数据转换为Result对象
     */
    private DingMappingEmployeeResult convertToResult(OuterOaEmployeeBindEntity bindEntity, DingTalkEmployeeObject employeeData) {
        if (bindEntity == null) {
            return null;
        }

        DingMappingEmployeeResult result = new DingMappingEmployeeResult();
//        result.setEmployeeStatus();
//        result.setEmployeeName();
//        result.setEmployeePhone();
//        result.setCreateBy();
//        result.setUpdateBy();
//        result.setGender();
//        result.setLastModifyTime();
//        result.setCrmDeptId();
        result.setEi(eieaConverter.enterpriseAccountToId(bindEntity.getFsEa()));
        result.setEmployeeId(Integer.valueOf(bindEntity.getFsEmpId()));
        result.setDingEmployeeId(bindEntity.getOutEmpId());
        result.setBindStatus(convertBindStatus(bindEntity.getBindStatus()));
        result.setCreateTime(new Date(bindEntity.getCreateTime()));
        result.setUpdateTime(new Date(bindEntity.getUpdateTime()));

        if (Objects.nonNull(employeeData)) {
            result.setDingEmployeeId(employeeData.getUserid());
            result.setDingEmployeeName(employeeData.getName());
            result.setDingEmployeePhone(employeeData.getPhone());
            result.setDingDeptId(employeeData.getDeptId());
            result.setDingDeptName(employeeData.getDeptName());
            result.setDingEmployeeStatus(employeeData.getStatus());
            result.setDingUnionId(employeeData.getUnionId());
        }

        return result;
    }

    private DingMappingEmployeeResult convertToResult(OuterOaEmployeeBindEntity bindEntity) {
        if (bindEntity == null) {
            return null;
        }

        // 查询员工数据
        OuterOaEmployeeDataEntity dataEntity = getEmployeeDataByOutUserId(
                bindEntity.getOutEa(),
                bindEntity.getAppId(),
                bindEntity.getOutEmpId());

        // 转换为Result对象
        return convertToResult(bindEntity, convertToEmployeeMongoData(dataEntity.getOutUserInfo()));
    }

    /**
     * 根据外部用户ID获取员工数据
     *
     * @param outEa     外部企业ID
     * @param appId     应用ID
     * @param outUserId 外部用户ID
     * @return 员工数据实体
     */
    private OuterOaEmployeeDataEntity getEmployeeDataByOutUserId(String outEa, String appId, String outUserId) {
        if (StringUtils.isEmpty(outEa) || StringUtils.isEmpty(appId) || StringUtils.isEmpty(outUserId)) {
            log.warn("getEmployeeDataByOutUserId param illegal: channel={}, outEa={}, appId={}, outUserId={}",
                    ChannelEnum.dingding, outEa, appId, outUserId);
            return null;
        }

        LambdaQueryWrapper<OuterOaEmployeeDataEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OuterOaEmployeeDataEntity::getChannel, ChannelEnum.dingding)
                .eq(OuterOaEmployeeDataEntity::getOutEa, outEa)
                .eq(OuterOaEmployeeDataEntity::getAppId, appId)
                .eq(OuterOaEmployeeDataEntity::getOutUserId, outUserId);

        return outerOaEmployeeDataMapper.selectOne(queryWrapper);
    }

    @NotNull
    private static DingTalkEmployeeObject convert2DingTalkEmployeeObject(DingMappingEmployeeResult emp) {
        DingTalkEmployeeObject employeeData = new DingTalkEmployeeObject();
        employeeData.setUserid(emp.getDingEmployeeId());
        employeeData.setName(emp.getDingEmployeeName());
        employeeData.setPhone(emp.getDingEmployeePhone());
        employeeData.setDeptId(emp.getDingDeptId());
        employeeData.setDeptName(emp.getDingDeptName());
        employeeData.setStatus(emp.getDingEmployeeStatus());
        employeeData.setUnionId(emp.getDingUnionId());
        // 性别设置
        if (emp.getGender() != null) {
            employeeData.setSexType(emp.getGender());
        }
        return employeeData;
    }

    /**
     * 根据fsEmpId查询员工绑定信息
     *
     * @param ei 企业ID
     * @param fsEmpId 纷享员工ID
     * @param appId 应用ID
     * @return 员工绑定信息
     */
    public Result<DingMappingEmployeeResult> findById(Integer ei, Integer fsEmpId, String appId) {
        if (Objects.isNull(ei) || Objects.isNull(fsEmpId) || StringUtils.isEmpty(appId)) {
            log.warn("findById param error, ei=[{}] ,fsEmpId=[{}], appId=[{}]", ei, fsEmpId, appId);
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        String fsEa = eieaConverter.enterpriseIdToAccount(ei);
        final String outEa = outerOaEnterpriseBindManager.getOutEaByFsEaAndAppId(ChannelEnum.dingding, fsEa, appId);
        if (StringUtils.isEmpty(outEa)) {
            return Result.newError(ResultCode.PARAMS_ERROR);
        }

        // 使用联表查询
        Map<String, Object> result = outerOaEmployeeBindMapper.queryEmployeeBindByFsEmpId(
                ChannelEnum.dingding.name(),
                outEa,
                fsEa,
                appId,
                String.valueOf(fsEmpId)
        );

        if (result == null || result.isEmpty()) {
            return Result.newSuccess(null);
        }

        DingMappingEmployeeResult employeeResult = convertMapToResult(result, fsEa);
        return Result.newSuccess(employeeResult);
    }

}
