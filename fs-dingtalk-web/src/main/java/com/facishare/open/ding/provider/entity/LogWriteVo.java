package com.facishare.open.ding.provider.entity;

import com.facishare.open.ding.api.enums.OperationStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/02/24 20:05
 */
@Data
@AllArgsConstructor
public class LogWriteVo implements Serializable {

    //抽离出日志的主体
    private Integer ei;
    /** @see com.facishare.open.ding.api.enums.OperationTypeEnum **/
    private Integer operateType;
    private String name;
    //同步状态
    private Integer status;
    private String message;

}
