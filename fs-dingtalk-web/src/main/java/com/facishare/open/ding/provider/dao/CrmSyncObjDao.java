package com.facishare.open.ding.provider.dao;

import com.facishare.open.ding.api.arg.NeedSyncDataArg;
import com.facishare.open.ding.api.result.AppAuthResult;
import com.facishare.open.ding.provider.arg.CrmSyncObjEntity;
import com.github.mybatis.mapper.ICrudMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/1/18 12:03
 * @Version 1.0
 */
public interface CrmSyncObjDao extends ICrudMapper<CrmSyncObjEntity> {

    @Select("<script>" +
            "select * from crm_sync_obj_info where is_init=1" +
            "<if test='needSyncDataArg.dingApiName!=null'>" +
            "and ding_api_name=#{needSyncDataArg.dingApiName}" +
            "</if>"+
            "<if test='needSyncDataArg.crmApiName!=null'>" +
            "and crm_api_name=#{needSyncDataArg.crmApiName}" +
            "</if>"+
            "<if test='needSyncDataArg.ei!=null'>" +
            "and ei=#{needSyncDataArg.ei}" +
            "</if>" +
            "<if test='needSyncDataArg.direction!=null'>" +
            "and direction=#{needSyncDataArg.direction}" +
            "</if>" +
            "limit #{page},#{size}" +
            "</script>")
    List<CrmSyncObjEntity> conditionNeedSynData (@Param("needSyncDataArg") NeedSyncDataArg needSyncDataArg,@Param("page") Integer page,@Param("size") Integer size);


    @Insert("<script>" +
            "INSERT IGNORE INTO crm_sync_obj_info" +
            "(corp_id,ei,ding_api_name,crm_api_name,direction,is_init,last_sync_time)" +
            "values" +
            "<foreach collection=\"objList\"  item=\"item\" separator=\",\" index=\"index\">" +
            "(#{item.corpId},#{item.ei},#{item.dingApiName},#{item.crmApiName},#{item.direction},#{item.isInit},#{item.lastSyncTime})" +
            "</foreach>" +
            "</script>")
    Integer batchSaveData(@Param("objList") List<CrmSyncObjEntity> objEntityList);

    @Update("<script>" +
            "update crm_sync_obj_info set is_init=#{needSyncDataArg.isInit} where ei=#{needSyncDataArg.ei} " +
            "and ding_api_name=#{needSyncDataArg.dingApiName} and crm_api_name=#{needSyncDataArg.crmApiName} " +
            "</script>")
    Integer updateInit(@Param("needSyncDataArg") NeedSyncDataArg needSyncDataArg) ;

    @Select("<script>" +
            "select count(*) from crm_sync_obj_info where corp_id=#{dingCorpId}" +
            "</script>")
    Integer queryIsSyncData(@Param("dingCorpId") String dingCorpId);

    @Update("<script>" +
            "update crm_sync_obj_info set last_sync_time=#{needSyncDataArg.lastSyncTime} where corp_id=#{needSyncDataArg.corpId} " +
            "and ding_api_name=#{needSyncDataArg.dingApiName} and crm_api_name=#{needSyncDataArg.crmApiName} and direction=#{needSyncDataArg.direction}" +
            "</script>")
    Integer updateLastSyncTIme(@Param("needSyncDataArg") NeedSyncDataArg needSyncDataArg) ;

    @Select("select * from crm_sync_obj_info where ei=#{ei} and id > #{id} order by id asc limit #{limit}")
    List<CrmSyncObjEntity> pageById(@Param("ei") Integer ei, @Param("id") Long id, @Param("limit") Integer limit);
}
