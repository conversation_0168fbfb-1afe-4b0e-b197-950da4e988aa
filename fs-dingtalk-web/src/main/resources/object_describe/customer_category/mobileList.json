{"agent_type": "agent_type_mobile", "components": [{"api_name": "table_component", "ref_object_api_name": "CustomerCategoryObj", "include_fields": [{"api_name": "name", "label": "客户分类名称", "render_type": "text"}, {"api_name": "category_code", "label": "客户分类编码", "render_type": "text"}, {"api_name": "parent_category_code", "label": "父客户分类编码", "render_type": "text"}], "type": "table"}], "package": "CRM", "api_name": "layout_CustomerCategoryObj_mobile", "ref_object_api_name": "CustomerCategoryObj", "layout_type": "list", "is_default": false, "display_name": "移动列表页布局", "is_show_fieldname": true}