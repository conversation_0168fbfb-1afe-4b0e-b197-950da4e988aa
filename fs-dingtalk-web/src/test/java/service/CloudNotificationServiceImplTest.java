package service;

import com.facishare.open.ding.api.arg.SendTextNoticeArg;
import com.facishare.open.ding.api.service.cloud.CloudNotificationService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class CloudNotificationServiceImplTest extends BaseAbstractTest{
    @Autowired
    private CloudNotificationService notificationService;

    @Test
    public void sendDingCloudNoticeTest() {
        SendTextNoticeArg arg = new SendTextNoticeArg();
        arg.setEnterpriseAccount("ddqybhzyl");
        arg.setTenantId("82379");
        arg.setReceivers(Lists.newArrayList(2161));
        arg.setMsgTitle("【dd测试】警告推送");
        arg.setMsg("【dd测试】警告内容");
        notificationService.sendDingCloudNotice(arg);
    }
}
