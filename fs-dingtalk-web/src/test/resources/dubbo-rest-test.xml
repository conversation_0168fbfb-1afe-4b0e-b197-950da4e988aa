<?xml version="1.0" encoding="UTF-8"?>
<beans
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="http://www.springframework.org/schema/beans"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans-3.2.xsd">

<!--    <import resource="classpath:spring/fs-spring-dubbo-rest-plugin-client.xml"/>-->
    <!-- 使用OkHttpSupport -->

<!--    <import resource="classpath:META-INF/fs-spring-dubbo-plugin.xml"/>-->

    <bean id="dingtalkSercive" class="com.facishare.dubbo.plugin.client.ServerHostProfile">
        <property name="configName" value="fs-open-dingtalk-all"/>
    </bean>

    <!--open api-->
    <bean class="com.facishare.dubbo.plugin.client.DubboRestFactoryBean" lazy-init="true">
        <property name="objectType"
                  value="com.facishare.open.ding.api.service.cloud.DingAuthService"/>
        <property name="serverHostProfile" ref="dingtalkSercive"/>
    </bean>

</beans>
