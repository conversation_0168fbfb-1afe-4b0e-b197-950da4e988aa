package com.facishare.open.outer.oa.connector.web.util;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 查询条件工具类
 */
public class SearchQueryUtils {

    private static final Integer DEFAULT_LIMIT = 100;
    private static final Integer DEFAULT_OFFSET = 0;
    private static final String DEFAULT_ORDER_FIELD = "last_modified_time";
    private static final boolean DEFAULT_ORDER_IS_ASC = false;

    /**
     * 构建查询条件
     * 
     * @param fieldName   字段名
     * @param fieldValues 字段值列表
     * @return 查询条件JSON字符串
     */
    public static String buildSearchQueryInfo(String fieldName, List<Object> fieldValues,String operator) {
        SearchQueryInfo searchQueryInfo = new SearchQueryInfo();
        // 设置默认分页
        searchQueryInfo.setLimit(100);
        searchQueryInfo.setOffset(0);

        // 设置过滤条件
        if (fieldName != null && CollectionUtils.isNotEmpty(fieldValues)) {
            SearchFilter filter = new SearchFilter();
            filter.setField_name(fieldName);
            filter.setField_values(fieldValues);
            filter.setOperator(operator);
            searchQueryInfo.setFilters(Lists.newArrayList(filter));
        }

        // 设置默认排序
        SearchOrder order = new SearchOrder();
        order.setFieldName(DEFAULT_ORDER_FIELD);
        order.setAsc(DEFAULT_ORDER_IS_ASC);
        searchQueryInfo.setOrders(Lists.newArrayList(order));

        return JSONObject.toJSONString(searchQueryInfo);
    }



    @Data
    private static class SearchQueryInfo {
        private Integer limit;
        private Integer offset;
        private List<SearchFilter> filters;
        private List<SearchOrder> orders;
    }

    @Data
    private static class SearchFilter {
        private String field_name;
        private List<Object> field_values;
        private String operator;
    }

    @Data
    private static class SearchOrder {
        private String fieldName;
        private boolean isAsc;
    }
}