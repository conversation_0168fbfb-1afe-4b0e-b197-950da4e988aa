package com.facishare.open.outer.oa.connector.web.exception;

import com.facishare.open.outer.oa.connector.common.api.result.Result;
import com.facishare.open.outer.oa.connector.i18n.I18NStringEnum;
import com.facishare.open.outer.oa.connector.i18n.I18NStringManager;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/15 16:54:46
 */
@Data
public class OutOaConnectorException extends RuntimeException {
    private I18NStringEnum errorEnum;
    private List<String> errorParam;

    public OutOaConnectorException(I18NStringEnum errorEnum, String... params) {
        super(I18NStringManager.getByTraceLocal(errorEnum, params));
        this.errorEnum = errorEnum;
        this.errorParam = Arrays.stream(params).collect(Collectors.toList());
    }

    public OutOaConnectorException(Result<?> result) {
        super(I18NStringManager.getByTraceLocal(result.getI18nKey(), result.getMsg(), result.getI18nExtra()));

    }
}
