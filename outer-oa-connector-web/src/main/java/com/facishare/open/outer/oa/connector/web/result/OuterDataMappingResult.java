package com.facishare.open.outer.oa.connector.web.result;

import com.facishare.open.outer.oa.connector.common.api.info.SettingAccountRulesModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class OuterDataMappingResult  implements Serializable {

    private Integer total;

    private Integer pageSize;

    private Integer pageNumber;

    private SettingAccountRulesModel settingAccountRulesModel;

    private List<EmployeeBindResult> employeeBindResults;

    private List<DepartmentBindResult> departmentBindResults;
}
