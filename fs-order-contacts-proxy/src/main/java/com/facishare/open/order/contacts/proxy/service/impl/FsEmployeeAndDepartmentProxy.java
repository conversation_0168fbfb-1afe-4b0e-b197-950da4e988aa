package com.facishare.open.order.contacts.proxy.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.facishare.open.order.contacts.proxy.api.result.HttpResponseMessage;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.network.ProxyHttpClient;
import com.fxiaoke.crmrestapi.common.data.ObjectData;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class FsEmployeeAndDepartmentProxy {
    @Resource
    private ProxyHttpClient proxyHttpClient;

    public Result<ObjectData> postUrl(String url, Map<String,Object> paramMap, String ei) {
        Map<String,String> hearsMap= getHeaderMap(ei, GlobalValue.SYSTEM_USER_ID+"");

        HttpResponseMessage response = proxyHttpClient.postUrl(url,paramMap,hearsMap,new TypeReference<HttpResponseMessage>(){});
        Integer code=response.getCode();
        String message=response.getMessage();
        if(code==0){
            JSONObject objectDataMap = response.getData().getJSONObject("objectData");
            ObjectData data = new ObjectData();
            data.putAll(objectDataMap);
            return Result.newSuccess(data);
        }
        return Result.newError(code,message);
    }

    public Result<Void> postUrl2(String url, Map<String,Object> paramMap, String ei) {
        Map<String,String> hearsMap= getHeaderMap(ei, GlobalValue.SYSTEM_USER_ID+"");

        HttpResponseMessage response = proxyHttpClient.postUrl(url,paramMap,hearsMap,new TypeReference<HttpResponseMessage>(){});
        Integer code=response.getCode();
        String message=response.getMessage();
        return Result.newError(code,message);
    }

    public Result<List<ObjectData>> postUrl3(String url, Object paramMap, String ei) {
        Map<String,String> hearsMap= getHeaderMap(ei, GlobalValue.SYSTEM_USER_ID+"");

        HttpResponseMessage response = proxyHttpClient.postUrl(url,paramMap,hearsMap,new TypeReference<HttpResponseMessage>(){});
        Integer code=response.getCode();
        String message=response.getMessage();
        if(code==0){
            JSONArray dataListArray = response.getData().getJSONArray("dataList");

            List<ObjectData> dataList = new ArrayList<>();
            for(int i=0;i<dataListArray.size();i++) {
                ObjectData data = new ObjectData();
                data.putAll(dataListArray.getJSONObject(i));
                dataList.add(data);
            }
            return Result.newSuccess(dataList);
        }
        return Result.newError(code,message);
    }

    public Result<ObjectData> postUrl4(String url, Object paramMap, String ei) {
        Map<String,String> hearsMap= getHeaderMap(ei, GlobalValue.SYSTEM_USER_ID+"");

        HttpResponseMessage response = proxyHttpClient.postUrl(url,paramMap,hearsMap,new TypeReference<HttpResponseMessage>(){});
        Integer code=response.getCode();
        String message=response.getMessage();
        if(code==0){
            JSONObject data = response.getData().getJSONObject("data");
            ObjectData objectData = new ObjectData();
            objectData.putAll(data);
            return Result.newSuccess(objectData);
        }
        return Result.newError(code,message);
    }

    public Result<ObjectData> postUrl5(String url, Object paramMap, String ei) {
        Map<String,String> hearsMap= getHeaderMap(ei, GlobalValue.SYSTEM_USER_ID+"");

        HttpResponseMessage response = proxyHttpClient.postUrl(url,paramMap,hearsMap,new TypeReference<HttpResponseMessage>(){});
        Integer code=response.getCode();
        String message=response.getMessage();
        if(code==0){
            JSONObject dataJSONObject = response.getData().getJSONObject("data");
            ObjectData data = new ObjectData();
            data.putAll(dataJSONObject);

            return Result.newSuccess(data);
        }
        return Result.newError(code,message);
    }

    public Result<List<ObjectData>> postUrl6(String url, Object paramMap, String ei, String dataResult) {
        Map<String,String> hearsMap= getHeaderMap(ei, GlobalValue.SYSTEM_USER_ID+"");

        HttpResponseMessage response = proxyHttpClient.postUrl(url,paramMap,hearsMap,new TypeReference<HttpResponseMessage>(){});
        Integer code=response.getCode();
        String message=response.getMessage();
        if(code==0){
            JSONArray dataListArray = response.getData().getJSONArray(dataResult);

            List<ObjectData> dataList = new ArrayList<>();
            for(int i=0;i<dataListArray.size();i++) {
                ObjectData data = new ObjectData();
                data.putAll(dataListArray.getJSONObject(i));
                dataList.add(data);
            }
            return Result.newSuccess(dataList);
        }
        return Result.newError(code,message);
    }

    public Map<String,String> getHeaderMap(String ei,String operatorId) {
        Map<String,String> hearsMap= Maps.newHashMap();
        hearsMap.put("x-fs-ei",ei);
        hearsMap.put("x-fs-userinfo",operatorId);
        return hearsMap;
    }
}
