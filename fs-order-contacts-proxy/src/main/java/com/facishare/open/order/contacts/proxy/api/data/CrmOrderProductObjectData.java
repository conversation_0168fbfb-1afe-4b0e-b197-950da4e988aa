package com.facishare.open.order.contacts.proxy.api.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * Created by chenzx on 2025/01/03.
 */
@Data
public class CrmOrderProductObjectData {
    /**
     * SalesOrderProductObj product_id
     */
    @JSONField(name = "product_id")
    private String productId;

    /**
     * SalesOrderProductObj product_price
     */
    @JSONField(name = "product_price")
    private String productPrice;

    /**
     * SalesOrderProductObj price_book_product_id
     */
    @JSONField(name = "price_book_product_id")
    private String priceBookProductId;

    /**
     * SalesOrderProductObj unit
     */
    @JSONField(name = "unit")
    private String unit;

    /**
     * SalesOrderProductObj quantity
     */
    @JSONField(name = "quantity")
    private String quantity;

    /**
     * SalesOrderProductObj UDInt4__c
     */
    @JSONField(name = "UDInt4__c")
    private String allResourceCount;

    /**
     * SalesOrderProductObj UDDate1__c
     */
    @JSONField(name = "UDDate1__c")
    private Long beginTime;

    /**
     * SalesOrderProductObj UDDate2__c
     */
    @JSONField(name = "UDDate2__c")
    private Long endTime;

    /**
     * SalesOrderProductObj UDMoney1__c
     */
    @JSONField(name = "UDMoney1__c")
    private String originalPrice;

    /**
     * SalesOrderProductObj UDMoney2__c
     */
    @JSONField(name = "UDMoney2__c")
    private String orderPrice;

    /**
     * SalesOrderProductObj UDRef1__c
     */
    @JSONField(name = "UDRef1__c")
    private String productCode;

    /**
     * SalesOrderProductObj UDRef2__c
     */
    @JSONField(name = "UDRef2__c")
    private String productType;

    /**
     * SalesOrderProductObj UDRef4__c
     * 是否允许试用：是-允许试用，空字符串-不允许试用
     */
    @JSONField(name = "UDRef4__c")
    private String canTry;

    /**
     * SalesOrderProductObj record_type
     */
    @JSONField(name = "record_type")
    private String recordType;

    /**
     * SalesOrderProductObj UDInt2__c
     */
    @JSONField(name = "UDInt2__c")
    private String days;

    /**
     * SalesOrderProductObj object_describe_id
     */
    @JSONField(name = "object_describe_id")
    private String objectDescribeId;

    /**
     * SalesOrderProductObj object_describe_api_name
     */
    @JSONField(name = "object_describe_api_name")
    private String objectDescribeApiName;
}