package com.facishare.open.order.contacts.proxy.api.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ModifyNameUtils {
    private static final Pattern employeePATTERN = Pattern.compile("([^ .\\-·\\[\\]【】()（）_0-9/\\p{L}\\p{M},，+&])|( .*])|(].* )|(^\\[.*]$)");
    private static final Pattern departmentPATTERN = Pattern.compile("([^ .\\-·\\[\\]【】()（）_0-9/\\p{L}\\p{M},，+&])|( .*])|(].* )|(^\\[.*]$)");
    public static String employeeValidName(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        Matcher m = employeePATTERN.matcher(name);
        return m.replaceAll("-");
    }

    public static String departmentValidName(String name) {
        if (StringUtils.isEmpty(name)) {
            return name;
        }
        Matcher m = departmentPATTERN.matcher(name);
        return m.replaceAll("-");
    }

    public static String nameExtend(String name) {
        Integer suffix = (int) (Math.random() * 1000);
        if (StringUtils.isEmpty(name)) {
            return "__".concat(suffix.toString());
        }
        return name.concat("__").concat(suffix.toString());
    }
}
