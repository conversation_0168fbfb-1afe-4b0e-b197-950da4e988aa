//package com.facishare.open.order.contacts.proxy.datasource;
//
//import com.github.jedis.support.MergeJedisCmd;
//
///**
// * Created by fengyh on 2018/3/1.
// */
//public class RedisDataSource {
//    private MergeJedisCmd jedisCmd;
//
//    public MergeJedisCmd getRedisClient() {
//        return jedisCmd;
//    }
//
//    public void setJedisCmd(MergeJedisCmd jedisCmd) {
//        this.jedisCmd = jedisCmd;
//    }
//}