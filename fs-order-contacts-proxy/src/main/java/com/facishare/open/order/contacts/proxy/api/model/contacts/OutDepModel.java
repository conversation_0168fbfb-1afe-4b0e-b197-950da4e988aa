package com.facishare.open.order.contacts.proxy.api.model.contacts;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 标准的外部部门model
 * <AUTHOR>
 * @date 2023.02.16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OutDepModel implements Serializable {
    /**
     * 部门ID
     */
    private String id;
    /**
     * 部门名称
     */
    private String name;
    /**
     * 父部门ID
     */
    private String parentId;
    /**
     * 部门的顺序
     */
    private String order;
    /**
     * 部门负责人ID
     */
    private String leaderId;
    /**
     * 部门群ID
     */
    private String chatId;
}
