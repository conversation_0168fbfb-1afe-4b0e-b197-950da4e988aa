package com.facishare.open.order.contacts.proxy.test.service;

import com.facishare.open.order.contacts.proxy.test.BaseTest;
import com.facishare.open.order.contacts.proxy.api.consts.GlobalValue;
import com.facishare.open.order.contacts.proxy.api.result.Result;
import com.facishare.open.order.contacts.proxy.api.service.FsContactsServiceProxy;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.List;

public class FsContactsServiceProxyTest extends BaseTest {
    @Resource
    private FsContactsServiceProxy fsContactsServiceProxy;

    @Test
    public void batchStopFsDep() {
        Result<List<String>> result = fsContactsServiceProxy.batchStopFsDep(88102, "88102",
                GlobalValue.ALL_COMPANY_DEPARTMENT_ID + "");
        System.out.println(result);
    }

    @Test
    public void batchStopFsEmp() {
        Result<List<String>> result = fsContactsServiceProxy.batchStopFsEmp(88142, "bbzgqy6731",
                "1005",null);
        System.out.println(result);
    }
}

